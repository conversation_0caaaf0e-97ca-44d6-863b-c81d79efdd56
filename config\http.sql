CREATE TABLE http_download_20 (
    TestcaseId BIGINT,
    OrderId BIGINT,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict TEXT,
    TestDefinitionPath TEXT,
    User TEXT,
    UserGroup TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry TEXT,
    errorSideId INT,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel INT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId INT,
    Completed INT,
    DataDownloadDuration TIME(3),
    DataDownloadDurationApp TIME(3),
    DnsDuration TIME(3),
    DnsDurationApp TIME(3),
    DnsOverHttpsUsed INT,
    DownloadDuration TIME(3),
    DownloadDurationApp TIME(3),
    DownloadedContentSize BIGINT,
    DownloadedContentSizeApp BIGINT,
    DownloadingRate DECIMAL(10,2),
    DownloadingRateApp DECIMAL(10,2),
    Failure INT,
    HTTPMeanDataRate DECIMAL(10,2),
    HTTPMeanDataRateApp DECIMAL(10,2),
    HTTP_RTT TIME(3),
    HTTP_RTT_App TIME(3),
    HappyEyeBalls_Delay TIME(3),
    Incomplete INT,
    LocalIpAddress TEXT,
    NumberOfTcpConnections INT,
    PageContent BIGINT,
    PageContentApp BIGINT,
    ServiceType TEXT,
    Success INT,
    SumOfBytes BIGINT,
    SumOfBytesReceived BIGINT,
    SumOfBytesSent BIGINT,
    TotalKBPerSecDownload DECIMAL(10,3),
    TotalKBPerSecDownloadApp DECIMAL(10,3),
    TotalLoadTime TIME(3),
    URL TEXT,
    a_AttachSuccessRatio INT,
    a_CellId2 TEXT,
    a_EcNO TEXT,
    a_GPRSAttachDuration TIME(3),
    a_GPRS_APN TEXT,
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_Version TEXT,
    a_IPv4_Used INT,
    a_IPv6_Used INT,
    a_LTE_RgAttachDuration TIME(3),
    a_LupDuration TIME(3),
    a_PDPContextActivationDuration TIME(3),
    a_PDPIpUpDuration TIME(3),
    a_SimAuthenticationAfterAttach INT,
    a_SimAuthenticationAfterLup INT,
    a_TAC TEXT,
    a_TcpHandshakeDuration TIME(3),
    a_UsedPLMNNameShort TEXT,
    a_accessType TEXT,
    b_EcNO TEXT,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_Version TEXT,
    b_IPv4_Used INT,
    b_IPv6_Used INT,
    b_LTE_RgAttachDuration TIME(3),
    b_LupDuration TIME(3),
    b_SimAuthenticationAfterAttach INT,
    b_SimAuthenticationAfterLup INT,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNO TEXT,
    c_LTE_RgAttachDuration TIME(3),
    c_LupDuration TIME(3),
    c_UsedPLMNNameShort TEXT,
    d_EcNO TEXT,
    d_LTE_RgAttachDuration TIME(3),
    d_LupDuration TIME(3),
    d_UsedPLMNNameShort TEXT,
    APN_AMBR_dl INT,
    APN_AMBR_ul INT,
    CA_RRC_Configured_L3 INT,
    CA_SCC_Band_1_L3 INT,
    CA_SCC_Band_2_L3 INT,
    CA_SCC_Bw_1_L3 INT,
    CA_SCC_Bw_2_L3 INT,
    CA_SCC_Freq_1_L3 INT,
    CA_SCC_Freq_2_L3 INT,
    CA_SCC_RSRP_1 INT,
    CA_SCC_RSRP_2 INT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    GBR_dl_dedicated INT,
    GBR_dl_negotiated INT,
    GBR_dl_requested INT,
    GBR_ul_dedicated INT,
    GBR_ul_negotiated INT,
    GBR_ul_requested INT,
    MBR_dl_dedicated INT,
    MBR_dl_negotiated INT,
    MBR_dl_requested INT,
    MBR_ul_dedicated INT,
    MBR_ul_negotiated INT,
    MBR_ul_requested INT,
    QCI_dedicated INT,
    QCI_default INT,
    TCDuration TIME(3),
    TestId BIGINT,
    TestrunId BIGINT,
    TrafficClass_negotiated INT,
    TrafficClass_requested INT,
    a_5QI_dedicated_L3 INT,
    a_5QI_default_L3 INT,
    a_AttachDuration_L3 TIME(3),
    a_AttachSuccessRatio_L3 INT,
    a_LTE_Freq INT,
    a_PDPCADuration_L3 TIME(3),
    a_PDPCASuccessRatio_L3 INT,
    b_5QI_dedicated_L3 INT,
    b_5QI_default_L3 INT,
    b_LTE_Freq INT,
    c_LTE_Freq INT,
    d_LTE_Freq INT,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance INT,
    ExecutionHost TEXT,
    ExecutionId BIGINT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability INT,
    a_5G_NSA_used INT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_CI TEXT,
    a_DCNR_restricted INT,
    a_EcIo TEXT,
    a_LAC TEXT,
    a_LTE_Band TEXT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP INT,
    a_NR_RSRQ INT,
    a_NR_SINR INT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC TEXT,
    a_RSCP INT,
    a_RSRP INT,
    a_RxLevel INT,
    a_SID TEXT,
    a_SIM_AuthenticationEnd DATETIME(3),
    a_SIM_AuthenticationStart DATETIME(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage INT,
    a_uuCqiSamples INT,
    a_uuPppHsdpaUsed INT,
    a_uuPppHsupaUsed INT,
    b_5G_NSA_availability INT,
    b_5G_NSA_used INT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_CI TEXT,
    b_DCNR_restricted INT,
    b_EcIo TEXT,
    b_LAC TEXT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP INT,
    b_NR_RSRQ INT,
    b_NR_SINR INT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP INT,
    b_RSRP INT,
    b_RxLevel INT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd DATETIME(3),
    b_SIM_AuthenticationStart DATETIME(3),
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage INT,
    b_uuCqiSamples INT,
    b_uuPppHsdpaUsed INT,
    b_uuPppHsupaUsed INT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_CI TEXT,
    c_DCNR_restricted INT,
    c_EcIo TEXT,
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP INT,
    c_NR_RSRQ INT,
    c_NR_SINR INT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP INT,
    c_RSRP INT,
    c_RxLevel INT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd DATETIME(3),
    c_SIM_AuthenticationStart DATETIME(3),
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_CI TEXT,
    d_DCNR_restricted INT,
    d_EcIo TEXT,
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP INT,
    d_NR_RSRQ INT,
    d_NR_SINR INT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP INT,
    d_RSRP INT,
    d_RxLevel INT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd DATETIME(3),
    d_SIM_AuthenticationStart DATETIME(3),
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset DECIMAL(5,1),
    uuCqiAverage INT,
    uuCqiSamples INT,
    uuPppHsdpaUsed INT,
    uuPppHsupaUsed INT,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid INT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP TEXT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId BIGINT,
    InsertId BIGINT
);