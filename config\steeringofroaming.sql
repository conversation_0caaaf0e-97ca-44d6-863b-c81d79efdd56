-- Création de la base de données
CREATE DATABASE IF NOT EXISTS roaming_data;
USE roaming_data;

-- Table SteeringOfRoaming (principale table basée sur le fichier fourni)
CREATE TABLE IF NOT EXISTS SteeringOfRoaming (
    TestcaseId BIGINT AUTO_INCREMENT PRIMARY KEY,
    OrderId BIGINT,
    TCName  text,
    TimestamINT DATATIME(3),
    Verdict varchar(10),
    TestDefinitionPath TEXT,
    User VARCHAR(50),
    UserGroup VARCHAR(50),
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry  text,
    errorSideId INT,
    errorSideLocation  text,
    errorSideNumber VARCHAR(20),
    errorSidePlmn VARCHAR(50),
    errorSideProbe  text,
    errorSideRxLevel INT,
    errorSideUsedPLMNName  text,
    errorState VARCHAR(50),
    errorStateId INT,
    Completed TINYINT(1),
    Failure TINYINT(1),
    Incomplete TINYINT(1),
    L3_Flag TINYINT(1),
    ServiceType varchar(10),
    Success TINYINT(1),
    a_EcNO DECIMAL(5,1),
    a_HappyEyeBallSelectedIPVersion VARCHAR(10),
    a_IP_Version varchar(10),
    a_IPv4_Used TINYINT(1),
    a_IPv6_Used TINYINT(1),
    a_InitialLupReqTime DATETIME(3),
    a_LTE_RgAttachDuration TIME(3),
    a_LupAcceptDuration TIME(3),
    a_LupAcceptDuration_All_VPLMN TIME(3),
    a_LupDuration TIME(3),
    a_LupMode varchar(10),
    a_LupRejectDuration TIME(3),
    a_NrOfLupRejects_All_VPLMN INT,
    a_NrOfLupRequests INT,
    a_NrOfLupRequests_All_VPLMN INT,
    a_NrOfPlmnsRejected INT,
    a_OverallNrOfLupRequests INT,
    a_RejectCauses TEXT,
    a_RejectedPLMNs TEXT,
    a_SimAuthenticationAfterAttach TINYINT(1),
    a_SimAuthenticationAfterLup TINYINT(1),
    a_TAC VARCHAR(10),
    a_UsedPLMNNameShort VARCHAR(50),
    a_VPLMN_registered TINYINT(1),
    b_EcNO DECIMAL(5,1),
    b_HappyEyeBallSelectedIPVersion VARCHAR(10),
    b_IP_Version varchar(10),
    b_IPv4_Used TINYINT(1),
    b_IPv6_Used TINYINT(1),
    b_InitialLupReqTime DATETIME(3),
    b_LTE_RgAttachDuration TIME(3),
    b_LupAcceptDuration TIME(3),
    b_LupDuration TIME(3),
    b_LupMode varchar(10),
    b_LupRejectDuration TIME(3),
    b_NrOfLupRequests INT,
    b_NrOfPlmnsRejected INT,
    b_OverallNrOfLupRequests INT,
    b_RejectCauses TEXT,
    b_RejectedPLMNs TEXT,
    b_SimAuthenticationAfterAttach TINYINT(1),
    b_SimAuthenticationAfterLup TINYINT(1),
    b_TAC VARCHAR(10),
    b_UsedPLMNNameShort VARCHAR(50),
    c_EcNO DECIMAL(5,1),
    c_InitialLupReqTime DATETIME(3),
    c_LTE_RgAttachDuration TIME,
    c_LupAcceptDuration TIME,
    c_LupDuration TIME,
    c_LupMode varchar(10),
    c_LupRejectDuration TIME,
    c_NrOfLupRequests INT,
    c_NrOfPlmnsRejected INT,
    c_OverallNrOfLupRequests INT,
    c_RejectCauses TEXT,
    c_RejectedPLMNs TEXT,
    c_UsedPLMNNameShort VARCHAR(50),
    d_EcNO DECIMAL(5,1),
    d_InitialLupReqTime DATETIME(3),
    d_LTE_RgAttachDuration TIME(3),
    d_LupAcceptDuration TIME(3),
    d_LupDuration TIME(3),
    d_LupMode varchar(10),
    d_LupRejectDuration TIME(3),
    d_NrOfLupRequests INT,
    d_NrOfPlmnsRejected INT,
    d_OverallNrOfLupRequests INT,
    d_RejectCauses TEXT,
    d_RejectedPLMNs TEXT,
    d_UsedPLMNNameShort VARCHAR(50),
    CauseText_L3  text,
    CauseValue_L3 INT,
    TCDuration TIME(3),
    TestId BIGINT,
    TestrunId BIGINT,
    a_5QI_dedicated_L3 INT,
    a_5QI_default_L3 INT,
    a_LTE_Freq INT,
    b_5QI_dedicated_L3 INT,
    b_5QI_default_L3 INT,
    b_LTE_Freq INT,
    c_LTE_Freq INT,
    d_LTE_Freq INT,
    DayOfMonth INT,
    DayOfWeek VARCHAR(10),
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone VARCHAR(10),
    WeekOfYear INT,
    Distance DECIMAL(10,2),
    ExecutionHost VARCHAR(50),
    ExecutionId BIGINT,
    ExternalNumber VARCHAR(20),
    ExternalNumberOrURI  text,
    a_5G_NSA_availability TINYINT(1),
    a_5G_NSA_used TINYINT(1),
    a_ATR_Mobile VARCHAR(50),
    a_ATR_SimEmu VARCHAR(50),
    a_CI INT,
    a_DCNR_restricted TINYINT(1),
    a_EcIo DECIMAL(5,1),
    a_LAC INT,
    a_LTE_Band INT,
    a_LTE_Bandwidth INT,
    a_MMEName  text,
    a_NID INT,
    a_NRI_cs INT,
    a_NRI_ps INT,
    a_NR_Band INT,
    a_NR_DL_Bandwidth INT,
    a_NR_RSRP INT,
    a_NR_RSRQ INT,
    a_NR_SINR INT,
    a_NR_pCI INT,
    a_NSSAI  text,
    a_RAC INT,
    a_RSCP INT,
    a_RSRP INT,
    a_RxLevel INT,
    a_SID INT,
    a_SIM_AuthenticationEnd DATETIME(3),
    a_SIM_AuthenticationStart DATETIME(3),
    a_TADIG VARCHAR(10),
    a_UsedPLMN VARCHAR(20),
    a_UsedPLMNName  text,
    a_imsi BIGINT,
    a_location  text,
    a_location_country  text,
    a_number VARCHAR(20),
    a_plmn VARCHAR(50),
    a_plmnShort VARCHAR(50),
    a_probe  text,
    a_uuCqiAverage INT,
    a_uuCqiSamples INT,
    a_uuPppHsdpaUsed TINYINT(1),
    a_uuPppHsupaUsed TINYINT(1),
    b_5G_NSA_availability TINYINT(1),
    b_5G_NSA_used TINYINT(1),
    b_ATR_Mobile VARCHAR(50),
    b_ATR_SimEmu VARCHAR(50),
    b_CI INT,
    b_DCNR_restricted TINYINT(1),
    b_EcIo DECIMAL(5,1),
    b_LAC INT,
    b_LTE_Band INT,
    b_LTE_Bandwidth INT,
    b_MMEName  text,
    b_NID INT,
    b_NRI_cs INT,
    b_NRI_ps INT,
    b_NR_Band INT,
    b_NR_DL_Bandwidth INT,
    b_NR_RSRP INT,
    b_NR_RSRQ INT,
    b_NR_SINR INT,
    b_NR_pCI INT,
    b_NSSAI  text,
    b_RAC INT,
    b_RSCP INT,
    b_RSRP INT,
    b_RxLevel INT,
    b_SID INT,
    b_SIM_AuthenticationEnd DATETIME(3),
    b_SIM_AuthenticationStart DATETIME(3),
    b_TADIG VARCHAR(10),
    b_UsedPLMN VARCHAR(20),
    b_UsedPLMNName  text,
    b_imsi BIGINT,
    b_location  text,
    b_location_country  text,
    b_number VARCHAR(20),
    b_plmn VARCHAR(50),
    b_plmnShort VARCHAR(50),
    b_probe  text,
    b_uuCqiAverage INT,
    b_uuCqiSamples INT,
    b_uuPppHsdpaUsed TINYINT(1),
    b_uuPppHsupaUsed TINYINT(1),
    c_ATR_Mobile VARCHAR(50),
    c_ATR_SimEmu VARCHAR(50),
    c_CI INT,
    c_DCNR_restricted TINYINT(1),
    c_EcIo DECIMAL(5,1),
    c_LAC INT,
    c_LTE_Band INT,
    c_LTE_Bandwidth INT,
    c_MMEName  text,
    c_NID INT,
    c_NRI_cs INT,
    c_NRI_ps INT,
    c_NR_Band INT,
    c_NR_DL_Bandwidth INT,
    c_NR_RSRP INT,
    c_NR_RSRQ INT,
    c_NR_SINR INT,
    c_NR_pCI INT,
    c_RAC INT,
    c_RSCP INT,
    c_RSRP INT,
    c_RxLevel INT,
    c_SID INT,
    c_SIM_AuthenticationEnd DATETIME(3),
    c_SIM_AuthenticationStart DATETIME(3),
    c_TADIG VARCHAR(10),
    c_UsedPLMN VARCHAR(20),
    c_UsedPLMNName  text,
    c_imsi BIGINT,
    c_location  text,
    c_number VARCHAR(20),
    c_plmn VARCHAR(50),
    c_plmnShort VARCHAR(50),
    c_probe  text,
    d_ATR_Mobile VARCHAR(50),
    d_ATR_SimEmu VARCHAR(50),
    d_CI INT,
    d_DCNR_restricted TINYINT(1),
    d_EcIo DECIMAL(5,1),
    d_LAC INT,
    d_LTE_Band INT,
    d_LTE_Bandwidth INT,
    d_MMEName  text,
    d_NID INT,
    d_NRI_cs INT,
    d_NRI_ps INT,
    d_NR_Band INT,
    d_NR_DL_Bandwidth INT,
    d_NR_RSRP INT,
    d_NR_RSRQ INT,
    d_NR_SINR INT,
    d_NR_pCI INT,
    d_RAC INT,
    d_RSCP INT,
    d_RSRP INT,
    d_RxLevel INT,
    d_SID INT,
    d_SIM_AuthenticationEnd DATETIME(3),
    d_SIM_AuthenticationStart DATETIME(3),
    d_TADIG VARCHAR(10),
    d_UsedPLMN VARCHAR(20),
    d_UsedPLMNName  text,
    d_imsi BIGINT,
    d_location  text,
    d_number VARCHAR(20),
    d_plmn VARCHAR(50),
    d_plmnShort VARCHAR(50),
    d_probe  text,
    unitsMaxunitsMaxTimeOffset DECIMAL (10,2),
    Description TEXT,
    a_NetworkType VARCHAR(10),
    b_NetworkType VARCHAR(10),
    c_NetworkType VARCHAR(10),
    d_NetworkType VARCHAR(10),
    a_hlr VARCHAR(50),
    b_hlr VARCHAR(50),
    c_hlr VARCHAR(50),
    d_hlr VARCHAR(50),
    Valid TINYINT(1),
    a_mobileType VARCHAR(50),
    a_type  text,
    b_mobileType VARCHAR(50),
    b_type  text,
    c_mobileType VARCHAR(50),
    d_mobileType VARCHAR(50),
    a_UnitGPS POINT,
    b_UnitGPS POINT,
    c_UnitGPS POINT,
    d_UnitGPS POINT,
    a_SearchedPLMN VARCHAR(50),
    a_SearchedPLMNName  text,
    a_SearchedPLMNNameShort VARCHAR(50),
    b_SearchedPLMN VARCHAR(50),
    b_SearchedPLMNName  text,
    b_SearchedPLMNNameShort VARCHAR(50),
    c_SearchedPLMN VARCHAR(50),
    c_SearchedPLMNName  text,
    c_SearchedPLMNNameShort VARCHAR(50),
    d_SearchedPLMN VARCHAR(50),
    d_SearchedPLMNName  text,
    d_SearchedPLMNNameShort VARCHAR(50),
    GRP VARCHAR(50),
    GlobalResourceCount INT,
    a_GlobalUsedPLMN VARCHAR(50),
    b_GlobalUsedPLMN VARCHAR(50),
    c_GlobalUsedPLMN VARCHAR(50),
    d_GlobalUsedPLMN VARCHAR(50),
    RecordId BIGINT,
    InsertId BIGINT,
    PRIMARY KEY (TestcaseId)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;