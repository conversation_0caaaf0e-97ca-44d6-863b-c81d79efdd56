import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@tremor/react';
import React, { useEffect, useRef, useState } from 'react';
import {
    checkApiConnection,
    checkDatabaseConnection,
    getAnnualTrend,
    getAttachmentRatesByCountry,
    getCountryOverview,
    getFailureAnalysis,
    getFailureCauses,
    getFailureCausesByCountryOperator,
    getFailureDetails,
    getHttpDownloadPerformance,
    getHttpPeriodData,
    getNetworkPerformanceStats,
    getOperators,
    getSteeringChartData,
    getTopDataConsumptionCountries,
    getWeeklyTrends,
    NetworkPerformanceStats,
    SelectionItem,
    SteeringChartResult
} from '../services/kpiService';
import FailureDetailsTable from './FailureDetailsTable';
import SteeringAnalysisChart from './SteeringAnalysisChart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';

// Style CSS personnalisé pour masquer la section "Analyse des Échecs"
const customStyles = `
  /* Styles d'origine pour steering-tab-content */
  .tremor-BarChart-xAxis text,
  .tremor-BarChart-axisBottom text,
  .tremor-BarChart text {
    font-size: 11px;
  }
  .steering-tab-content .chart-container {
    height: 1800px;
    overflow-y: auto;
    padding-right: 20px;
    margin-bottom: 30px;
  }
`;

// Fonction pour déterminer la couleur selon le taux de succès
const getKpiColor = (value: number): string => {
  if (value > 90) return '#10b981';  // vert-500 (emerald-500) si KPI > 90%
  if (value > 75) return '#f59e0b';  // orange-500 (amber-500) si 75% < KPI < 90%
  return '#ef4444';                  // rouge-500 (red-500) si KPI < 75%
};

// Composant personnalisé pour afficher une barre colorée selon la valeur du KPI
const ColoredKpiBar: React.FC<{country: string; value: number; maxWidth?: number; data?: any}> = ({ country, value, maxWidth = 400, data }) => {
  const barColor = getKpiColor(value);
  const barWidth = `${Math.min(value, 100) / 100 * maxWidth}px`;
  const [showTooltip, setShowTooltip] = useState(false);
  
  // Déterminer l'icône selon la valeur
  const getStatusIcon = () => {
    if (value > 90) return '✓'; // Coche pour bon
    if (value > 75) return '!'; // Point d'exclamation pour moyen
    return '✗'; // Croix pour mauvais
  };
  
  return (
    <div className="flex items-center mb-3 relative">
      <div className="w-32 text-sm font-bold truncate pr-2 flex items-center" style={{ color: '#000000' }}>
        <span 
          className="mr-1 w-5 h-5 rounded-full flex items-center justify-center text-xs text-white"
          style={{ backgroundColor: barColor }}
        >
          {getStatusIcon()}
        </span>
        {country}
      </div>
      <div className="flex-1">
        <div 
          style={{ 
            width: barWidth, 
            backgroundColor: barColor,
            height: '20px',
            borderRadius: '4px',
            position: 'relative',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
          }}
          onMouseEnter={() => setShowTooltip(true)}
          onMouseLeave={() => setShowTooltip(false)}
          className="hover:brightness-110"
        >
          <span 
            style={{ 
              position: 'absolute', 
              right: '-45px', 
              top: '0px',
              fontSize: '13px',
              fontWeight: 'bold',
              color: barColor
            }}
          >
            {value}%
          </span>
          
          {/* Tooltip avec informations détaillées */}
          {showTooltip && data && (
            <div 
              className="absolute z-50 bg-white p-3 rounded-md shadow-lg border border-gray-200"
              style={{ 
                top: '25px', 
                left: '0', 
                width: '250px',
                fontSize: '12px'
              }}
            >
              <div className="font-semibold border-b pb-1 mb-1">{country}</div>
              <div className="grid grid-cols-2 gap-1">
                <div><strong>Taux de succès:</strong></div>
                <div className="text-right" style={{ color: barColor }}><strong>{value}%</strong></div>
                
                {data.rejection_rate !== undefined && (
                  <>
                    <div><strong>Taux de rejet:</strong></div>
                    <div className="text-right">{data.rejection_rate}%</div>
                  </>
                )}
                
                {data.avg_duration !== undefined && (
                  <>
                    <div><strong>Durée moyenne:</strong></div>
                    <div className="text-right">{data.avg_duration}s</div>
                  </>
                )}
                
                {data.total_requests !== undefined && (
                  <>
                    <div><strong>Total requêtes:</strong></div>
                    <div className="text-right">{data.total_requests}</div>
                  </>
                )}
                
                {data.total_rejected !== undefined && (
                  <>
                    <div><strong>Total rejetés:</strong></div>
                    <div className="text-right">{data.total_rejected}</div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Type pour les données de taux de succès
interface SuccessRateData {
  [country: string]: {
    Success: number;
    Fail: number;
    total_entries: number;
    operator?: string;
  };
}

// Type pour les données d'attachement
interface AttachmentData {
  country: string;
  total_tests: number;
  attachment_distribution: {
    [key: string]: number;
  };
}

// Type pour les données d'analyse des échecs
interface FailureData {
  label: string;
  value: number;
}

// Définir les interfaces correctes pour les données
interface SteeringSuccessItem {
  country: string;
  success_rate: number;
  total_attempts: number;
  failures: number;
}

// Type pour les données d'attachement
interface AttachmentRatesItem {
  country: string;
  total_tests: number;
  attachment_distribution: {
    [key: string]: number;
  };
  success_rate?: number;
}

// Définir les interfaces pour les nouvelles données de détail d'échec
interface FailureDetail {
  a_location_country?: string;
  a_UsedPLMNName?: string;
  a_usedplmn?: string;
  errortext?: string;
  a_rejectCause?: string;
  a_rejectedPLMNs?: string;
  a_NrOfPlmnRejected?: number;
  a_nrofluprequests?: number;
  a_networkType?: string;
  a_location?: string;
  a_number?: string;
  a_imsi?: string;
  a_Usedplmnname?: string;
  a_TADIG?: string;
  dayofweek?: number;
  weekofyear?: number;
  timestamp?: string;
  details?: any[];
}

interface DetailedStats {
  reject_causes?: { [key: string]: number };
  rejected_plmns?: { [key: string]: number };
  network_stats?: { [key: string]: number };
  distribution?: { [key: string]: number };
}

// Interface pour les causes d'échec
interface FailureCauses {
  top_failures: {
    [key: string]: number;
  };
  details?: FailureDetail[];
  raw_failures?: FailureDetail[];
}

// Interface pour l'analyse des échecs
interface FailureAnalysis {
  attachment_stats: {
    attachment_distribution: { [key: string]: number };
    total_tests: number;
  };
  detailed_stats?: DetailedStats;
}

// Utiliser une icône d'upload simple pour éviter l'erreur avec lucide-react
const UploadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
    <polyline points="17 8 12 3 7 8"></polyline>
    <line x1="12" y1="3" x2="12" y2="15"></line>
  </svg>
);

// Composant Button simplifié pour éviter l'erreur d'importation 
const Button: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement> & { children: React.ReactNode }> = ({ children, ...props }) => (
  <button 
    className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 bg-blue-600 text-white"
    {...props}
  >
    {children}
  </button>
);

// Ajout des interfaces pour les réponses API
interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}

interface FailureDetailResponse {
  errortext?: string;
  a_LupRejectDuration?: number;
  a_NrOfLupRejects_All_VPLMN?: number;
  a_NrOfPlmnsRejected?: number;
  a_RejectCauses?: string;
  a_RejectedPLMNs?: string;
  b_LupRejectDuration?: number;
  b_NrOfPlmnsRejected?: number;
  [key: string]: any;
}

const RoamingKpiDashboard: React.FC = () => {
  // États avec les bons types
  const [countryList, setCountryList] = useState<SelectionItem[]>([]);
  const [operatorList, setOperatorList] = useState<SelectionItem[]>([]);
  const [selectedCountry, setSelectedCountry] = useState<string>('');
  const [selectedOperator, setSelectedOperator] = useState<string>('');
  const [selectedVerdict, setSelectedVerdict] = useState<string>('');

  // États pour stocker les données des graphiques
  const [successRateData, setSuccessRateData] = useState<any[]>([]);
  const [attachmentData, setAttachmentData] = useState<AttachmentData[]>([]);
  const [failureCausesData, setFailureCausesData] = useState<FailureData[]>([]);
  const [weeklyTrendData, setWeeklyTrendData] = useState<any[]>([]);
  const [annualTrendData, setAnnualTrendData] = useState<any[]>([]);
  const [verdictFilter, setVerdictFilter] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Nouveaux états pour les graphiques spécifiques
  const [steeringSuccessData, setSteeringSuccessData] = useState<SteeringSuccessItem[]>([]);
  const [attachmentRatesData, setAttachmentRatesData] = useState<AttachmentRatesItem[]>([]);
  const [filteredAttachmentRatesData, setFilteredAttachmentRatesData] = useState<AttachmentRatesItem[]>([]);

  // Cache des données pour éviter les changements à chaque actualisation
  const [cachedSteeringSuccessData, setCachedSteeringSuccessData] = useState<SteeringSuccessItem[] | null>(null);
  const [cachedAttachmentRatesData, setCachedAttachmentRatesData] = useState<AttachmentRatesItem[] | null>(null);
  const [lastRefreshTime, setLastRefreshTime] = useState<string>(new Date().toLocaleTimeString());

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Nouveaux états pour les détails d'échec
  const [failureDetails, setFailureDetails] = useState<any[]>([]);
  const [detailedStats, setDetailedStats] = useState<DetailedStats>({});
  const [failureRawData, setFailureRawData] = useState<any[]>([]);

  // États pour les performances réseau et HTTP
  const [networkPerformance, setNetworkPerformance] = useState<NetworkPerformanceStats | null>(null);
  const [httpDownloadPerformance, setHttpDownloadPerformance] = useState<any | null>(null);
  const [httpPeriodData, setHttpPeriodData] = useState<any[]>([]);
  const [topDataConsumptionCountries, setTopDataConsumptionCountries] = useState<any[]>([]);
  const [failureCausesByCountryOperatorData, setFailureCausesByCountryOperatorData] = useState<any[]>([]);
  const [apiConnected, setApiConnected] = useState<boolean>(false);
  const [dbConnected, setDbConnected] = useState<boolean>(false);
  const [currentPeriod, setCurrentPeriod] = useState<string>('month'); // Valeur par défaut ou celle désirée

  // Effet pour déboguer successRateData
  useEffect(() => {
    console.log('État actuel de successRateData:', successRateData);
    console.log('Longueur de successRateData:', successRateData.length);
    
    // Si les données sont disponibles, vérifier le format
    if (successRateData.length > 0) {
      console.log('Format de la première entrée:', successRateData[0]);
      console.log('Vérification des clés nécessaires:', 
        'country' in successRateData[0], 
        'Taux de succès' in successRateData[0], 
        'Échecs' in successRateData[0]
      );
    }
  }, [successRateData]);

  // Fonction de chargement des données
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('Chargement des données KPI...');
      
      // Charger les données de steering
      const steeringResult: SteeringChartResult = await getSteeringChartData({});
      if (steeringResult.success) {
        console.log(`${steeringResult.data.length} pays chargés avec succès`);
        console.log("Premier pays:", steeringResult.data[0]);
        setSteeringSuccessData(steeringResult.data);
        setCachedSteeringSuccessData(steeringResult.data);
      } else {
        console.error("Erreur lors du chargement des données de steering:", steeringResult.error);
        setError(steeringResult.error || "Erreur lors du chargement des données de steering");
        setSteeringSuccessData([]);
        setCachedSteeringSuccessData(null);
      }
      
      // Charger les données d'attachement
      const attachmentResponse = await getAttachmentRatesByCountry();
      if (attachmentResponse.success) {
        console.log(`${attachmentResponse.data.length} données d'attachement chargées avec succès`);
        setAttachmentRatesData(attachmentResponse.data);
        setCachedAttachmentRatesData(attachmentResponse.data);
      } else {
        console.error("Erreur lors du chargement des données d'attachement:", attachmentResponse.message || "Erreur inconnue");
        setError(attachmentResponse.message || "Erreur lors du chargement des données d'attachement");
        setAttachmentRatesData([]);
        setCachedAttachmentRatesData(null);
      }
      
      // Charger les autres données...
      // const countriesResponse = await getCountries(); // Retiré pour éviter les conflits
      // setCountryList(countriesResponse); // Retiré pour éviter les conflits

      const operatorsResponse = await getOperators();
      setOperatorList(operatorsResponse);
      
      // Récupérer la liste des pays disponibles
      const countryOverviewResponse = await getCountryOverview();
      console.log('1. Réponse de getCountryOverview():', countryOverviewResponse); // Log détaillé de la réponse brute
      
      // Extraire uniquement les pays disponibles dans les données
      if (countryOverviewResponse && countryOverviewResponse.length > 0) {
        const availableCountries: SelectionItem[] = countryOverviewResponse
          .filter((item: { a_location_country?: string }) => {
            const isValid = item && item.a_location_country && item.a_location_country !== '';
            if (!isValid) {
              console.warn('Skipping invalid country item:', item); // Log pour les éléments filtrés
            }
            return isValid;
          })
          .map((item: { a_location_country: string }) => ({
            value: item.a_location_country,
            label: item.a_location_country
          }));
        console.log(`2. Pays disponibles après filtrage et formatage (${availableCountries.length}):`, availableCountries); // Log de la liste finale
        setCountryList(availableCountries);
      } else {
        console.log("3. getCountryOverview() a retourné des données vides ou invalides."); // Log si la réponse est vide
        setCountryList([]);
      }
      
      // Charger les taux de succès (maintenant depuis getSteeringChartData)
      const successRateResult = await getSteeringChartData({
        country: selectedCountry || undefined,
        operator: selectedOperator || undefined
      });

      if (successRateResult.success) {
        console.log('Données brutes de steering reçues:', successRateResult.data);
        // Transformer les données pour correspondre au format attendu par successRateData
        const formattedSuccessRate = successRateResult.data.map(item => ({
          country: item.country,
          'Taux de succès': item['Taux de succès'],
          'Échecs': item['Échecs'],
          'Entrées': item['Entrées']
        }));
        console.log('Données de taux de succès formatées pour le graphique:', formattedSuccessRate);
        setSuccessRateData(formattedSuccessRate);
      } else {
        console.error("Erreur lors de la récupération du taux de succès:", successRateResult.error);
        setSuccessRateData([]);
      }
      
      // Charger les causes d'échec
      const failureCausesResponse = await getFailureCauses(
        selectedCountry || undefined, 
        selectedOperator || undefined
      );
      console.log('Données reçues du backend - causes d\'échec:', failureCausesResponse);
      
      if (failureCausesResponse && failureCausesResponse.top_failures) {
        const failureCausesFormatted = Object.entries(failureCausesResponse.top_failures).map(([cause, count]) => ({
          label: cause,
          value: count as number
        }));
        setFailureCausesData(failureCausesFormatted);
      }
      
      // Stocker les détails des échecs
      if (failureCausesResponse && 'details' in failureCausesResponse) {
        setFailureDetails(failureCausesResponse.details as FailureDetail[]);
      }
      
      // Stocker les données brutes d'échec
      if (failureCausesResponse && 'raw_failures' in failureCausesResponse && Array.isArray(failureCausesResponse.raw_failures)) {
        setFailureRawData(failureCausesResponse.raw_failures);
      } else {
        setFailureRawData([]);
      }
      
      // Charger les détails statistiques
      const failureAnalysisResponse = await getFailureAnalysis(
        selectedCountry || undefined, 
        selectedOperator || undefined
      );
      console.log('Données reçues du backend - analyse des échecs:', failureAnalysisResponse);
      
      if (failureAnalysisResponse && 'detailed_stats' in failureAnalysisResponse) {
        setDetailedStats(failureAnalysisResponse.detailed_stats as DetailedStats);
      }
      
      // Charger les performances réseau
      const networkPerformanceResponse = await getNetworkPerformanceStats(
        selectedCountry || undefined,
        selectedOperator || undefined
      );
      setNetworkPerformance(networkPerformanceResponse);

      // Charger les performances de téléchargement HTTP
      const httpDownloadPerformanceResponse = await getHttpDownloadPerformance();
      if (httpDownloadPerformanceResponse) {
        setHttpDownloadPerformance(httpDownloadPerformanceResponse);
      }

      // Charger les données de période HTTP
      const httpPeriodDataResponse = await getHttpPeriodData(
        currentPeriod,
        selectedCountry || undefined,
        selectedOperator || undefined
      );
      setHttpPeriodData(httpPeriodDataResponse);

      // Charger les pays avec la plus forte consommation de données
      const topDataConsumptionCountriesResponse = await getTopDataConsumptionCountries();
      setTopDataConsumptionCountries(topDataConsumptionCountriesResponse);

      // Charger les causes d'échec par pays/opérateur
      const failureCausesByCountryOperatorResponse = await getFailureCausesByCountryOperator(
        selectedCountry || '',
        selectedOperator || ''
      );
      setFailureCausesByCountryOperatorData(failureCausesByCountryOperatorResponse);

      // Vérifier la connexion API/DB
      const apiConnected = await checkApiConnection();
      const dbConnected = await checkDatabaseConnection();
      setApiConnected(apiConnected);
      setDbConnected(dbConnected);

      // Charger les tendances
      if (verdictFilter !== 'fail') {
        // Tendances annuelles
        const annualTrendResponse = await getAnnualTrend(
          'month',
          selectedCountry || undefined, 
          selectedOperator || undefined
        );
        console.log('Données reçues du backend - tendances annuelles:', annualTrendResponse);
        setAnnualTrendData(annualTrendResponse.map((item: any) => ({
          mois: item.month_name,
          'Taux de succès': item.success_rate,
          'Durée moyenne': item.avg_duration,
          ...Object.entries(item.attachment_distribution || {}).reduce((acc, [level, value]) => ({
            ...acc,
            [`Niveau ${level}`]: value
          }), {})
        })));
        
        // Tendances hebdomadaires
        const weeklyTrendResponse = await getWeeklyTrends(
          selectedCountry || undefined, 
          selectedOperator || undefined
        );
        console.log('Données reçues du backend - tendances hebdomadaires:', weeklyTrendResponse);
        setWeeklyTrendData(weeklyTrendResponse.map((item: any) => ({
          semaine: `Semaine ${item.weekofyear}`,
          pays: item.a_location_country,
          'Taux de succès': item.success_rate,
          'Durée moyenne': item.avg_duration,
          ...Object.entries(item.attachment_distribution || {}).reduce((acc, [level, value]) => ({
            ...acc,
            [`Niveau ${level}`]: value
          }), {})
        })));
      }
      
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Fonction pour rafraîchir les données de steering
  const refreshSteeringData = () => {
    // Vider le cache des données
    setCachedSteeringSuccessData(null);
    setCachedAttachmentRatesData(null);
    // Recharger les données
    loadData();
  };

  // Effet pour charger les données au montage et lorsque les filtres changent
  useEffect(() => {
    console.log('Chargement initial des données...');
    loadData();
  }, [selectedCountry, selectedOperator, verdictFilter]);

  // Effet pour gérer la sélection automatique de l'onglet en fonction du filtre verdict
  useEffect(() => {
    // Si le filtre verdict est sur "fail", sélectionner automatiquement l'onglet "failures"
    if (verdictFilter === 'fail') {
      // Utiliser un délai court pour s'assurer que le DOM est bien mis à jour
      setTimeout(() => {
        const failuresTab = document.querySelector('[data-value="failures"]');
        if (failuresTab) {
          (failuresTab as HTMLElement).click();
          console.log("Onglet 'Analyse des échecs' automatiquement sélectionné");
        }
      }, 100);
    }
  }, [verdictFilter]);

  // Gestion du changement de pays
  const handleCountryChange = (value: string) => {
    setSelectedCountry(value);
  };
  
  // Gestion du changement d'opérateur
  const handleOperatorChange = (value: string) => {
    setSelectedOperator(value);
  };

  // Gestion du changement de filtre verdict
  const handleVerdictFilterChange = (value: string) => {
    const upper = value.toUpperCase();
    console.log(`Changement de filtre verdict: ${upper}`);
    setVerdictFilter(upper.toLowerCase()); // pour l'UI éventuellement
    setSelectedVerdict(upper);
    
    // Si on passe au filtre "Échecs", on inclut FAIL et INCONC (FAIL / INCONC)
    if (upper === 'FAIL') {
      console.log("Filtre changé pour 'Échecs', chargement des détails d'échec...");
      
      const loadFailureDetails = async () => {
        try {
          console.log("Chargement des détails d'échec depuis handleVerdictFilterChange...");
          console.log("Paramètres de filtrage:", {
            verdict: 'FAIL',
            country: selectedCountry || 'all',
            operator: selectedOperator || 'all'
          });
          
          // Un seul appel : le backend renvoie FAIL + INCONC lorsque verdict=FAIL
          const apiResp = await getFailureDetails({
              verdict: 'FAIL',
              country: selectedCountry || 'all',
              operator: selectedOperator || 'all'
          });

          console.log('Réponse failure_details:', apiResp);
          
          const combinedData: FailureDetailResponse[] = Array.isArray(apiResp?.data) ? apiResp.data : [];
          
          console.log("Données combinées avant filtrage:", combinedData);
          
          // Filtrer pour ne garder que les colonnes nécessaires
          const filteredData = combinedData.map(item => ({
            errortext: item.errortext || '',
            a_LupRejectDuration: item.a_LupRejectDuration || 0,
            a_NrOfLupRejects_All_VPLMN: item.a_NrOfLupRejects_All_VPLMN || 0,
            a_NrOfPlmnsRejected: item.a_NrOfPlmnsRejected || 0,
            a_RejectCauses: item.a_RejectCauses || '',
            a_RejectedPLMNs: item.a_RejectedPLMNs || '',
            b_LupRejectDuration: item.b_LupRejectDuration || 0,
            b_NrOfPlmnsRejected: item.b_NrOfPlmnsRejected || 0
          }));
          
          console.log("Données filtrées finales:", filteredData);
          setFailureDetails(filteredData);

          // Forcer la navigation vers l'onglet des échecs
          setTimeout(() => {
            const failuresTab = document.querySelector('[data-value="failures"]');
            if (failuresTab) {
              (failuresTab as HTMLElement).click();
              console.log("Navigation forcée vers l'onglet des échecs");
            }
          }, 100);
        } catch (error) {
          console.error("Erreur lors du chargement des détails d'échec:", error);
          setFailureDetails([]);
        }
      };
      
      loadFailureDetails();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
      setLoading(true);
      
      // Créer un formData et envoyer directement le fichier
      const formData = new FormData();
      formData.append('file', event.target.files[0]);
      
      // Appel au service d'upload avec l'URL correcte (backend FastAPI)
      fetch('http://localhost:8000/api/upload/csv', {
        method: 'POST',
        body: formData,
      })
      .then(response => response.json())
      .then(data => {
        console.log('Fichier téléchargé avec succès:', data);
        
        // Recharger TOUTES les données après le téléchargement pour s'assurer que tout est à jour
        loadData();
      })
      .catch(error => {
        console.error('Erreur lors du téléchargement du fichier:', error);
        setError('Erreur lors du téléchargement du fichier CSV');
        setLoading(false);
      });
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Formateurs de valeurs pour les graphiques
  const percentFormatter = (value: number) => `${value}%`;
  const countFormatter = (value: number) => `${value}`;
  const failureFormatter = (value: number) => `${value} échecs`;

  const fetchFailureDetails = async () => {
    try {
      console.log("Appel de fetchFailureDetails avec les paramètres:", {
        verdict: selectedVerdict,
        country: selectedCountry || 'all',
        operator: selectedOperator || 'all'
      });

      const response = await getFailureDetails({
        verdict: selectedVerdict || 'ALL',
        country: selectedCountry || 'all',
        operator: selectedOperator || 'all'
      });
      
      console.log("Réponse de l'API fetchFailureDetails:", response);
      
      if (response && response.data && Array.isArray(response.data)) {
        const filteredData = response.data.map((item: FailureDetailResponse) => ({
          errortext: item.errortext || '',
          a_LupRejectDuration: item.a_LupRejectDuration || 0,
          a_NrOfLupRejects_All_VPLMN: item.a_NrOfLupRejects_All_VPLMN || 0,
          a_NrOfPlmnsRejected: item.a_NrOfPlmnsRejected || 0,
          a_RejectCauses: item.a_RejectCauses || '',
          a_RejectedPLMNs: item.a_RejectedPLMNs || '',
          b_LupRejectDuration: item.b_LupRejectDuration || 0,
          b_NrOfPlmnsRejected: item.b_NrOfPlmnsRejected || 0
        }));
        
        console.log("Données filtrées dans fetchFailureDetails:", filteredData);
        setFailureDetails(filteredData);
      } else {
        console.log("Aucune donnée valide reçue de l'API");
        setFailureDetails([]);
      }
    } catch (error) {
      console.error("Erreur dans fetchFailureDetails:", error);
      setFailureDetails([]);
    }
  };

  useEffect(() => {
    fetchFailureDetails();
  }, [selectedVerdict, selectedCountry, selectedOperator]);

  // Fonction pour forcer le chargement des données d'attachement
  const forceLoadAttachmentData = async () => {
    try {
      console.log("Chargement forcé des données d'attachement...");
      const response = await getAttachmentRatesByCountry();
      
      if (Array.isArray(response) && response.length > 0) {
        console.log(`${response.length} pays chargés avec succès`);
        console.log("Premier pays:", JSON.stringify(response[0]));
        
        // Vérifier que les données sont dans le bon format
        if (response[0].country && response[0].attachment_distribution) {
          // Vérifier la structure des données d'attachement
          console.log("Structure de attachment_distribution:", 
            typeof response[0].attachment_distribution, 
            Object.keys(response[0].attachment_distribution)
          );
          
          // Traiter les données pour s'assurer que toutes les valeurs sont des nombres
          const processedData = response.map(item => {
            const processedItem = { ...item };
            
            // S'assurer que attachment_distribution est un objet
            if (typeof processedItem.attachment_distribution === 'string') {
              try {
                processedItem.attachment_distribution = JSON.parse(processedItem.attachment_distribution);
              } catch (e) {
                console.error(`Erreur lors du parsing de attachment_distribution pour ${item.country}:`, e);
                processedItem.attachment_distribution = {};
              }
            }
            
            // Convertir toutes les valeurs en nombres
            if (processedItem.attachment_distribution) {
              Object.keys(processedItem.attachment_distribution).forEach(key => {
                const value = processedItem.attachment_distribution[key];
                processedItem.attachment_distribution[key] = typeof value === 'string' ? parseFloat(value) : value;
              });
            }
            
            // Calculer le taux de succès si non présent
            if (typeof processedItem.success_rate === 'undefined' || processedItem.success_rate === null) {
              const totalTests = processedItem.total_tests || 0;
              if (totalTests > 0 && processedItem.attachment_distribution) {
                const successfulAttachments = Object.entries(processedItem.attachment_distribution)
                  .filter(([level]) => parseInt(level) > 0)
                  .reduce((sum, [_, value]) => sum + (typeof value === 'number' ? value : 0), 0);
                
                processedItem.success_rate = (successfulAttachments / totalTests) * 100;
              } else {
                processedItem.success_rate = 0;
              }
            }
            
            return processedItem;
          });
          
          // Mettre à jour les données
          setAttachmentRatesData(processedData);
          setFilteredAttachmentRatesData(processedData);
          console.log("Données d'attachement mises à jour avec succès:", processedData);
          
          // Forcer une mise à jour de l'interface
          setLastRefreshTime(new Date().toLocaleTimeString());
        } else {
          console.error("Format de données incorrect:", response[0]);
          setError("Format de données d'attachement incorrect");
        }
      } else {
        console.warn("Aucune donnée d'attachement reçue");
        setAttachmentRatesData([]);
        setFilteredAttachmentRatesData([]);
      }
    } catch (error) {
      console.error("Erreur lors du chargement forcé des données d'attachement:", error);
      setError("Erreur lors du chargement des données d'attachement");
      setAttachmentRatesData([]);
      setFilteredAttachmentRatesData([]);
    }
  };

  // Fonction pour obtenir une couleur pour n'importe quel niveau d'attachement
  const getAttachmentLevelColor = (level: number): string => {
    console.log(`Récupération de la couleur pour le niveau ${level}`);
    
    // Définir les couleurs pour les 7 niveaux d'attachement si elles ne sont pas déjà définies
    const defaultColors = [
      "#ef4444", // rouge - niveau 0 (échec)
      "#10b981", // vert - niveau 1
      "#fcd34d", // jaune - niveau 2
      "#92400e", // ambre foncé - niveau 3
      "#f97316", // orange - niveau 4
      "#3b82f6", // bleu - niveau 5
      "#1f2937"  // gris foncé - niveau 6
    ];
    
    if (level < defaultColors.length) {
      console.log(`Couleur trouvée: ${defaultColors[level]}`);
      return defaultColors[level];
    } else {
      console.log(`Niveau supérieur à 6, utilisation de la couleur par défaut: ${defaultColors[6]}`);
      return defaultColors[6];
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-4">Tableau de bord KPI Roaming</h1>
        
        {/* Filtres */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div className="w-64">
            <label className="block text-sm font-medium mb-1">Filtrer par pays</label>
            <Select value={selectedCountry} onValueChange={setSelectedCountry}>
              <SelectTrigger className="w-full">
                <SelectValue>{selectedCountry || "Sélectionner un pays"}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tous les pays</SelectItem>
                {countryList.map((country) => (
                  <SelectItem key={country.value} value={country.value}>
                    {country.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-64">
            <label className="block text-sm font-medium mb-1">Filtrer par opérateur</label>
            <Select value={selectedOperator} onValueChange={setSelectedOperator}>
              <SelectTrigger className="w-full">
                <SelectValue>{selectedOperator || "Sélectionner un opérateur"}</SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Tous les opérateurs</SelectItem>
                {operatorList.map((operator) => (
                  <SelectItem key={operator.value} value={operator.value}>
                    {operator.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-64">
            <label className="block text-sm font-medium mb-1 text-red-700">Filtrer par verdict</label>
            <Select value={selectedVerdict} onValueChange={handleVerdictFilterChange}>
              <SelectTrigger className="w-full">
                <SelectValue>
                  {selectedVerdict === 'FAIL' ? 'Échecs' : 
                   selectedVerdict === 'SUCCESS' ? 'Succès' : 
                   selectedVerdict === 'ALL' ? 'Tous les verdicts' : 
                   "Sélectionner un verdict"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Tous les verdicts</SelectItem>
                <SelectItem value="SUCCESS">Succès</SelectItem>
                <SelectItem value="FAIL">Échecs</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="w-64">
            <label className="block text-sm font-medium mb-1">Charger des données</label>
            <input 
              type="file" 
              ref={fileInputRef}
              onChange={handleFileChange} 
              accept=".csv"
              className="hidden" 
            />
            <Button 
              onClick={handleUploadClick}
              className="w-full flex items-center justify-center"
            >
              <UploadIcon />
              <span className="ml-2">Charger un CSV</span>
            </Button>
            {selectedFile && (
              <p className="text-xs text-gray-500 mt-1 truncate">
                Fichier: {selectedFile.name}
              </p>
            )}
          </div>
          
          <div className="w-64">
            <label className="block text-sm font-medium mb-1">Action spéciale</label>
            <Button 
              onClick={forceLoadAttachmentData}
              className="w-full flex items-center justify-center bg-green-600 hover:bg-green-700"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 2v6h-6"></path>
                <path d="M3 12a9 9 0 0 1 15-6.7L21 8"></path>
                <path d="M3 12a9 9 0 0 0 15 6.7L21 16"></path>
                <path d="M21 22v-6h-6"></path>
              </svg>
              <span className="ml-2">Forcer le chargement des attachements</span>
            </Button>
          </div>
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          <p className="ml-3 text-gray-600">Chargement des données pour {countryList.length} pays...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      ) : (
        <Tabs defaultValue="steering" className="w-full">
          <TabsList className="mb-4 w-full flex justify-center gap-2 p-2 bg-slate-100 rounded-lg">
            <TabsTrigger value="overview" className="text-lg p-2 font-bold">Aperçu</TabsTrigger>
            <TabsTrigger value="trends" className="text-lg p-2 font-bold">Tendances</TabsTrigger>
            <TabsTrigger value="steering" className="text-lg p-2 font-bold bg-blue-100">Steering et Attachement</TabsTrigger>
            {verdictFilter !== 'success' && <TabsTrigger value="failures" className={`text-lg p-2 font-bold ${verdictFilter === 'fail' ? 'bg-red-100' : ''}`} data-value="failures">Analyse des échecs</TabsTrigger>}
          </TabsList>
          
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Graphique du taux de succès */}
              <Card>
                <CardHeader>
                  <CardTitle>Taux de succès par pays</CardTitle>
                  <CardDescription>Pourcentage de réussite des connexions par pays</CardDescription>
                </CardHeader>
                <CardContent>
                  {successRateData.length > 0 ? (
                    <BarChart
                      data={successRateData}
                      index="country"
                      categories={['Taux de succès', 'Échecs']}
                      colors={['green', 'red']}
                      valueFormatter={percentFormatter}
                      stack={false}
                      className="h-72"
                      customTooltip={(props) => {
                        const { payload, active } = props;
                        if (!active || !payload || payload.length === 0) return null;

                        const data = payload[0];
                        const country = data.payload.country;
                        const success = data.payload['Taux de succès'];
                        const fail = data.payload['Échecs'];

                        return (
                          <div className="p-2 bg-white shadow-md rounded border text-sm">
                            <div className="font-medium">{country}</div>
                            <div>Taux de succès: {success}%</div>
                            <div>Échecs: {fail}%</div>
                          </div>
                        );
                      }}
                    />
                  ) : (
                    <div className="flex flex-col items-center justify-center h-72 text-gray-500">
                      <p>Aucune donnée disponible pour ce graphique</p>
                      <p className="text-sm mt-2">Format attendu: [{"{country: 'France', 'Taux de succès': 85.7, 'Échecs': 14.3}"}]</p>
                      <button 
                        onClick={() => loadData()}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                      >
                        Recharger les données
                      </button>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Graphique d'attachement */}
              <Card>
                <CardHeader>
                  <CardTitle>Performance des attachements</CardTitle>
                  <CardDescription>Taux des différents niveaux d'attachement</CardDescription>
                </CardHeader>
                <CardContent>
                  {weeklyTrendData.length > 0 && (
                    <BarChart
                      data={weeklyTrendData}
                      index="semaine"
                      categories={['Attache 0', 'Attache 1', 'Attache 2']}
                      colors={['blue', 'indigo', 'purple']}
                      valueFormatter={percentFormatter}
                      stack={true}
                      className="h-72"
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="trends">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Tendances hebdomadaires */}
              <Card>
                <CardHeader>
                  <CardTitle>Tendances hebdomadaires</CardTitle>
                  <CardDescription>Évolution des performances par semaine</CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart
                    data={weeklyTrendData}
                    index="semaine"
                    categories={['Taux de succès', 'Durée moyenne']}
                    colors={['emerald', 'blue']}
                    valueFormatter={percentFormatter}
                    className="h-72"
                  />
                </CardContent>
              </Card>
              
              {/* Tendances annuelles */}
              {verdictFilter !== 'fail' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Tendances annuelles</CardTitle>
                    <CardDescription>Évolution des performances sur l'année</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <AreaChart
                      data={annualTrendData}
                      index="mois"
                      categories={['Taux de succès']}
                      colors={['indigo']}
                      valueFormatter={percentFormatter}
                      className="h-72"
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="steering" className="steering-tab-content">
            <style dangerouslySetInnerHTML={{__html: customStyles}}/>
            <div className="grid grid-cols-1 gap-10">
              {/* Graphique de steering avec attachements et ligne de taux de succès */}
              <Card>
                <CardHeader className="text-center pb-0">
                  <CardTitle className="text-xl">Steering of Roaming - Tous les Pays</CardTitle>
                  <CardDescription>Distribution des niveaux d'attachement et taux de succès par pays</CardDescription>
                  
                  {/* Bouton pour rafraîchir les données avec horodatage */}
                  <div className="flex justify-end mt-2">
                    <button
                      onClick={refreshSteeringData}
                      className="flex items-center gap-1 px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md text-sm transition-colors"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12Z" stroke="currentColor" strokeWidth="2" />
                        <path d="M12 8V12L14 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                      Actualiser les données ({lastRefreshTime})
                    </button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="min-h-[1800px]" style={{
                    height: '1800px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    paddingRight: '20px'
                  }}>
                    {/* Les logs de débogage ne doivent pas être rendus comme des ReactNode */}
                    {/* Vérification plus robuste des données */}
                    {Array.isArray(attachmentRatesData) && attachmentRatesData.length > 0 ? (
                      <>
                        <div className="h-full">
                          <div className="mb-4">
                            {/* Débogage des données */}
                            <div className="bg-gray-100 p-4 mb-4 border border-gray-200 rounded">
                              <h3 className="text-sm font-bold mb-2">Données d'attachement disponibles:</h3>
                              <p className="text-xs">Nombre d'entrées: {attachmentRatesData.length}</p>
                              <div className="flex justify-between items-center mt-2 mb-2">
                                <span className="text-xs text-gray-500">Dernière mise à jour: {lastRefreshTime}</span>
                                <button 
                                  onClick={forceLoadAttachmentData}
                                  className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                                >
                                  Recharger maintenant
                                </button>
                              </div>
                              {attachmentRatesData[0] && (
                                <>
                                  <p className="text-xs">Premier pays: {attachmentRatesData[0].country}</p>
                                  <p className="text-xs">Valeurs: succès={attachmentRatesData[0].success_rate}%</p>
                                  <div className="grid grid-cols-4 gap-1 mt-1">
                                    {Object.entries(attachmentRatesData[0].attachment_distribution)
                                      .filter(([level]) => parseInt(level) >= 0 && parseInt(level) <= 6)
                                      .sort(([a], [b]) => parseInt(a) - parseInt(b))
                                      .map(([level, percentage]) => (
                                        <p key={level} className="text-xs">
                                          Niveau {level}: {typeof percentage === 'number' ? percentage.toFixed(2) : '?'}%
                                        </p>
                                      ))}
                                  </div>
                                </>
                              )}
                            </div>
                            
                            {/* Légende des niveaux d'attachement */}
                            <div className="flex flex-wrap justify-center gap-4">
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-red-500"></div>
                                <span className="text-sm">Échec</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-green-500"></div>
                                <span className="text-sm">Niveau 1</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-yellow-400"></div>
                                <span className="text-sm">Niveau 2</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-amber-800"></div>
                                <span className="text-sm">Niveau 3</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-orange-500"></div>
                                <span className="text-sm">Niveau 4</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-blue-500"></div>
                                <span className="text-sm">Niveau 5</span>
                              </div>
                              <div className="flex items-center gap-1">
                                <div className="w-4 h-4 bg-gray-800"></div>
                                <span className="text-sm">Niveau 6</span>
                              </div>
                              <div className="flex items-center gap-1 ml-6">
                                <div className="w-8 h-1 bg-black"></div>
                                <span className="text-sm font-medium">Taux de succès d'attach</span>
                              </div>
                            </div>
                            <p className="text-xs text-center text-gray-500 mt-2 mb-4">
                              Note: Seuls les niveaux d'attachement de 0 à 6 sont affichés. Les tentatives supérieures sont agrégées dans le niveau 6.
                            </p>
                          </div>
                        </div>
                        
                          {/* Graphique combiné comme dans l'image */}
                          <div className="relative pb-8">
                            <div id="steering-chart" className="w-full">
                              <svg width="100%" height={Math.max(600, attachmentRatesData.length * 30)} style={{ overflow: 'visible' }}>
                                <g id="bars-and-line">
                                  {/* Définir les variables de positionnement */}
                                  {(() => {
                                    const labelOffset = 100; // Espace pour le nom du pays
                                    const availableWidth = 800; // Largeur disponible pour les valeurs
                                    const barHeight = 20; // Hauteur d'une "ligne" de pays

                                    // Définir les couleurs pour les 7 niveaux d'attachement
                                    const colors = [
                                      "#ef4444", // rouge - niveau 0 (échec)
                                      "#10b981", // vert - niveau 1
                                      "#fcd34d", // jaune - niveau 2
                                      "#92400e", // ambre foncé - niveau 3
                                      "#f97316", // orange - niveau 4
                                      "#3b82f6", // bleu - niveau 5
                                      "#1f2937"  // gris foncé - niveau 6
                                    ];

                                    // Vérifier que attachmentRatesData est défini et non vide
                                    if (!attachmentRatesData || attachmentRatesData.length === 0) {
                                      console.warn("Aucune donnée d'attachement disponible pour le graphique");
                                      return <text x="400" y="300" textAnchor="middle" fontSize="16" fill="gray">Aucune donnée d'attachement disponible</text>;
                                    }

                                    console.log("Génération du graphique avec", attachmentRatesData.length, "pays");
                                    
                                    return attachmentRatesData.map((countryData, index) => {
                                      const y = index * 30 + 20;

                                      // S'assurer que les données de pays sont définies
                                      if (!countryData || !countryData.country || !countryData.attachment_distribution) {
                                        console.warn(`Données invalides pour l'entrée ${index}:`, countryData);
                                        return null;
                                      }

                                      console.log(`Traitement des données pour ${countryData.country}:`, 
                                        typeof countryData.attachment_distribution,
                                        Object.keys(countryData.attachment_distribution)
                                      );

                                      // Filtrer pour ne garder que les niveaux de 0 à 6
                                      const filteredDistribution: {[key: string]: number} = {};
                                      let otherLevelsValue = 0;
                                      
                                      Object.entries(countryData.attachment_distribution).forEach(([level, value]) => {
                                        // S'assurer que la valeur est un nombre
                                        const numValue = typeof value === 'string' ? parseFloat(value) : (typeof value === 'number' ? value : 0);
                                        const levelNum = parseInt(level);
                                        
                                        if (levelNum >= 0 && levelNum <= 6) {
                                          filteredDistribution[level] = numValue;
                                        } else {
                                          // Agréger les valeurs des niveaux supérieurs à 6 dans le niveau 6
                                          otherLevelsValue += numValue;
                                        }
                                      });
                                      
                                      // Ajouter les valeurs agrégées au niveau 6 s'il y en a
                                      if (otherLevelsValue > 0) {
                                        filteredDistribution['6'] = (filteredDistribution['6'] || 0) + otherLevelsValue;
                                      }

                                      console.log(`Distribution filtrée pour ${countryData.country}:`, filteredDistribution);

                                      // Calculer le taux de succès comme la somme des attachements réussis (niveaux 1-6)
                                      const successRate = Object.entries(filteredDistribution)
                                        .filter(([level]) => parseInt(level) > 0)
                                        .reduce((sum, [_, value]) => sum + (typeof value === 'number' ? value : 0), 0);

                                      console.log(`Taux de succès calculé pour ${countryData.country}: ${successRate}%`);

                                      // Position X du taux de succès (sur une échelle de 0 à 100)
                                      const successRateX = labelOffset + (successRate / 100) * availableWidth;
                                    
                                      // Créer un tableau complet pour les 7 niveaux d'attachement (0-6)
                                      const attachmentLevels = Array.from({ length: 7 }, (_, i) => ({
                                        level: i,
                                        value: filteredDistribution[i.toString()] || 0
                                      }));
                                      
                                      // Calculer la somme totale des valeurs pour normaliser
                                      const totalValue = attachmentLevels.reduce((sum, level) => sum + level.value, 0);
                                      
                                      console.log(`Total des valeurs pour ${countryData.country}: ${totalValue}`);
                                      
                                      // Position X initiale pour les barres
                                      let currentX = labelOffset;
                                    
                                      return (
                                        <g key={countryData.country}>
                                          {/* Nom du pays */}
                                          <text x="0" y={y + barHeight / 2} dominantBaseline="middle" textAnchor="start" fontSize="12" fontWeight="bold" fill="black" style={{ fill: '#000000' }}>
                                            {countryData.country}
                                          </text>
                                        
                                          {/* Ligne de référence à 100% */}
                                          <line x1={labelOffset + availableWidth} y1={y} x2={labelOffset + availableWidth + 5} y2={y} stroke="#ccc" strokeWidth="1" />

                                          {/* Ligne horizontale pour séparer les pays */}
                                          <line x1="0" y1={y + barHeight} x2={labelOffset + availableWidth + 10} y2={y + barHeight} stroke="#eee" strokeWidth="0.5" />
                                        
                                          {/* Barres d'attachement - 7 niveaux */}
                                          {attachmentLevels.map((levelData, i) => {
                                            // Calculer la largeur proportionnelle de la barre
                                            const width = totalValue > 0 ? (levelData.value / totalValue) * availableWidth : 0;
                                            
                                            console.log(`Barre niveau ${levelData.level}: valeur=${levelData.value}, largeur=${width}`);
                                            
                                            // Rendre la barre même si la largeur est très petite mais pas zéro
                                            if (levelData.value > 0) {
                                              const color = getAttachmentLevelColor(levelData.level);
                                              console.log(`Rendu de la barre niveau ${levelData.level} avec couleur ${color}`);
                                              
                                              // Assurer une largeur minimale de 1px pour la visibilité
                                              const displayWidth = Math.max(width, 1);
                                              
                                              const rect = (
                                                <rect 
                                                  key={i}
                                                  x={currentX} 
                                                  y={y} 
                                                  width={displayWidth}
                                                  height={barHeight} 
                                                  fill={color}
                                                />
                                              );
                                              
                                              // Mettre à jour la position X pour la prochaine barre
                                              // Utiliser la largeur réelle pour le positionnement
                                              currentX += width;
                                              
                                              return rect;
                                            }
                                            return null;
                                          })}
                                          
                                          {/* Ligne noire pour le taux de succès */}
                                          <line 
                                            x1={successRateX} 
                                            y1={y - 2} 
                                            x2={successRateX} 
                                            y2={y + barHeight + 2} 
                                            stroke="black" 
                                            strokeWidth="2"
                                          />
                                          
                                          {/* Valeur du taux de succès */}
                                          <text
                                            x={successRateX + 5}
                                            y={y - 5}
                                            dominantBaseline="middle"
                                            textAnchor="start"
                                            fontSize="12"
                                            fontWeight="bold"
                                            fill="black"
                                          >
                                            {successRate.toFixed(1)}%
                                          </text>

                                          {/* Tooltip avec tous les niveaux */}
                                          <title>
                                            {`Pays: ${countryData.country}
Taux de succès: ${successRate.toFixed(1)}%
${Object.entries(filteredDistribution)
  .sort(([a], [b]) => parseInt(a) - parseInt(b))
  .map(([level, value]) => `Niveau ${level}: ${value.toFixed(1)}%`)
  .join('\n')}
Total: ${countryData.total_tests} tests`}
                                          </title>
                                        </g>
                                      );
                                    });
                                  })()}
                                </g>

                                {/* Axe X (échelle des pourcentages) */}
                                <g id="x-axis">
                                  {(() => {
                                    const labelOffset = 100;
                                    const availableWidth = 800;
                                    const totalHeight = attachmentRatesData.length * 30;

                                    return [0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100].map(percent => {
                                      const x = labelOffset + (percent / 100) * availableWidth;
                                      return (
                                        <g key={percent}>
                                          <line x1={x} y1="0" x2={x} y2={totalHeight + 30} stroke="#eee" strokeWidth="0.5" />
                                          <text x={x} y={totalHeight + 45} dominantBaseline="top" textAnchor="middle" fontSize="10">
                                        {percent}%
                                      </text>
                                    </g>
                                      );
                                    });
                                  })()}
                                </g>
                              </svg>
                            </div>
                          </div>

                          {/* Afficher les données du premier pays pour débogage */}
                          {attachmentRatesData.length > 0 && attachmentRatesData[0] && (
                            <div className="bg-gray-100 p-4 rounded-lg mb-4">
                              <h3 className="text-sm font-bold mb-2">Données d'attachement disponibles:</h3>
                              <p className="text-xs">Nombre d'entrées: {attachmentRatesData.length}</p>
                              {attachmentRatesData[0] && (
                                <>
                                  <p className="text-xs">Premier pays: {attachmentRatesData[0].country}</p>
                                  <p className="text-xs">Valeurs: succès={attachmentRatesData[0].success_rate}%</p>
                                  <div className="grid grid-cols-4 gap-1 mt-1">
                                    {Object.entries(attachmentRatesData[0].attachment_distribution)
                                      .filter(([level]) => parseInt(level) >= 0 && parseInt(level) <= 6)
                                      .sort(([a], [b]) => parseInt(a) - parseInt(b))
                                      .map(([level, percentage]) => (
                                        <p key={level} className="text-xs">
                                          Niveau {level}: {typeof percentage === 'number' ? percentage.toFixed(2) : '?'}%
                                        </p>
                                      ))}
                                  </div>
                                </>
                              )}
                            </div>
                          )}
                      </>
                    ) : (
                      <div className="text-center text-gray-500 py-8">
                        Aucune donnée steering disponible pour les filtres sélectionnés.
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
              
              {/* Analyse Python complète avec tous les pays requis */}
              <SteeringAnalysisChart 
                title="Analyse complète avec tous les pays (générée par Python)"
                height="900px"
              />
              
              <Card>
                <CardHeader className="text-center pb-0">
                  <CardTitle className="text-xl">Taux de succès d'attach par Pays</CardTitle>
                  <CardDescription>Pourcentage de succès d'attachement par pays</CardDescription>
                  
                  {/* Bouton pour rafraîchir les données avec horodatage */}
                  <div className="flex justify-end mt-2">
                    <button
                      onClick={refreshSteeringData}
                      className="flex items-center gap-1 px-3 py-1.5 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-md text-sm transition-colors"
                    >
                      <svg className="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 12C4 7.58172 7.58172 4 12 4C16.4183 4 20 7.58172 20 12C20 16.4183 16.4183 20 12 20C7.58172 20 4 16.4183 4 12Z" stroke="currentColor" strokeWidth="2" />
                        <path d="M12 8V12L14 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                      </svg>
                      Actualiser les données ({lastRefreshTime})
                    </button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="min-h-[1200px]" style={{
                    height: '1200px',
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    paddingRight: '20px'
                  }}> {/* Hauteur augmentée pour afficher tous les pays */}
                    {steeringSuccessData.length > 0 ? (
                      <>
                        {/* Légende des couleurs de KPI */}
                        <div className="flex justify-center gap-4 mb-4">
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 rounded-full bg-green-500"></div>
                            <span className="text-xs">KPI &gt; 90%</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                            <span className="text-xs">75% &lt; KPI &lt; 90%</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-3 h-3 rounded-full bg-red-500"></div>
                            <span className="text-xs">KPI &lt; 75%</span>
                          </div>
                        </div>
                        
                        {/* Graphique personnalisé avec des barres colorées selon la valeur du KPI */}
                        <div className="p-4 bg-white rounded-lg mb-8">
                          <h3 className="text-lg font-semibold mb-4">Taux de succès par pays</h3>
                          <div className="overflow-y-auto" style={{ maxHeight: '1000px' }}>
                            {steeringSuccessData
                              .sort((a, b) => b.success_rate - a.success_rate) // Trier par taux de succès décroissant
                              .map((item, index) => (
                                <ColoredKpiBar 
                                  key={index}
                                  country={item.country}
                                  value={item.success_rate}
                                  maxWidth={500}
                                  data={item}
                                />
                              ))
                            }
                          </div>
                        </div>
                        
                        {/* Nouveau graphique pour Taux de succès par pays/opérateur */}
                        <div className="p-4 bg-white rounded-lg">
                          <h3 className="text-lg font-semibold mb-4">
                            Taux de succès par pays/opérateur
                            <button
                              onClick={() => {
                                // Recharger spécifiquement les données du taux de succès
                                getSteeringChartData({
                                  country: selectedCountry || undefined,
                                  operator: selectedOperator || undefined
                                })
                                  .then(data => {
                                    console.log('Rechargement des données de taux de succès:', data);
                                    if (Array.isArray(data)) {
                                      setSuccessRateData(data);
                                    } else {
                                      const formatted = Object.entries(data).map(([country, info]: [string, any]) => ({
                                        country,
                                        'Taux de succès': parseFloat((info.Success || 0).toFixed(1)),
                                        'Échecs': parseFloat((info.Fail || 0).toFixed(1)),
                                        'Entrées': info.total_entries || 0,
                                        'opérateur': info.operator || 'Tous'
                                      }));
                                      setSuccessRateData(formatted);
                                    }
                                  })
                                  .catch(err => console.error('Erreur lors du rechargement:', err));
                              }}
                              className="ml-2 px-2 py-1 text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 rounded"
                            >
                              Recharger
                            </button>
                          </h3>
                          <div className="overflow-hidden">
                            
                            {successRateData.length > 0 ? (
                              <BarChart
                                data={successRateData}
                                index="country"
                                categories={['Taux de succès', 'Échecs']}
                                colors={['emerald', 'red']}
                                valueFormatter={(value) => `${value}%`}
                                stack={false}
                                className="h-96"
                                showLegend={true}
                                showAnimation={true}
                                minValue={0}
                                maxValue={100}
                                yAxisWidth={40}
                                autoMinValue={false}
                                layout="vertical"
                                showGridLines={true}
                              />
                            ) : (
                              <div className="text-center py-8 text-gray-500">
                                Aucune donnée disponible pour le taux de succès par pays/opérateur
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="h-full flex flex-col items-center justify-center text-gray-500">
                        <div className="text-xl font-semibold mb-4">Aucun pays disponible dans les données</div>
                        <div className="text-base">
                          Veuillez vérifier que le backend renvoie des données pour <code>/api/steering_success_by_country</code>.
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          
          {verdictFilter !== 'success' && (
            <TabsContent value="failures">
                <Card>
                  <CardHeader>
                  <CardTitle>Détails des Échecs</CardTitle>
                  <CardDescription>Analyse détaillée des causes d'échec</CardDescription>
                  </CardHeader>
                  <CardContent>
                  <div className="grid grid-cols-3 gap-4 mb-4">
                    <Select value={selectedVerdict} onValueChange={handleVerdictFilterChange}>
                      <SelectTrigger>
                        <SelectValue>{selectedVerdict || "Sélectionner un verdict"}</SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="SUCCESS">Succès</SelectItem>
                        <SelectItem value="FAIL">Échecs</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={selectedCountry} onValueChange={setSelectedCountry}>
                      <SelectTrigger>
                        <SelectValue>{selectedCountry || "Sélectionner un pays"}</SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Tous les pays</SelectItem>
                        {countryList.map((item: SelectionItem) => (
                          <SelectItem key={item.value} value={item.value}>
                            {item.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Select value={selectedOperator} onValueChange={setSelectedOperator}>
                      <SelectTrigger>
                        <SelectValue>{selectedOperator || "Sélectionner un opérateur"}</SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Tous les opérateurs</SelectItem>
                        {operatorList.map((item: SelectionItem) => (
                          <SelectItem key={item.value} value={item.value}>
                            {item.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    </div>

                  <FailureDetailsTable show={true} verdict="fail" />
                    </CardContent>
                  </Card>
            </TabsContent>
          )}
        </Tabs>
      )}
    </div>
  );
};

export default RoamingKpiDashboard; 