<!DOCTYPE html>
<html>
<head>
    <title>Test Filtres Temporels</title>
</head>
<body>
    <h1>Test des Filtres Temporels</h1>
    <div id="results"></div>
    
    <script>
        async function testFilters() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test sans filtres
                console.log('🧪 Test sans filtres...');
                const response1 = await fetch('http://localhost:8000/api/steering_success_by_country_filtered');
                const data1 = await response1.json();
                console.log('Sans filtres:', data1.data.length, 'pays');
                
                // Test avec filtre mois
                console.log('🧪 Test avec filtre mois...');
                const response2 = await fetch('http://localhost:8000/api/steering_success_by_country_filtered?period=month&year=2025&month=6');
                const data2 = await response2.json();
                console.log('Avec filtre mois:', data2.data.length, 'pays');
                
                // Test avec filtre jour
                console.log('🧪 Test avec filtre jour...');
                const response3 = await fetch('http://localhost:8000/api/steering_success_by_country_filtered?period=day&year=2025&month=6&day=17');
                const data3 = await response3.json();
                console.log('Avec filtre jour:', data3.data.length, 'pays');
                
                // Afficher les résultats
                resultsDiv.innerHTML = `
                    <h2>Résultats des tests:</h2>
                    <p><strong>Sans filtres:</strong> ${data1.data.length} pays</p>
                    <p><strong>Avec filtre mois (juin 2025):</strong> ${data2.data.length} pays</p>
                    <p><strong>Avec filtre jour (17 juin 2025):</strong> ${data3.data.length} pays</p>
                    
                    <h3>Exemple de données (jour 17):</h3>
                    <pre>${JSON.stringify(data3.data.slice(0, 3), null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('Erreur:', error);
                resultsDiv.innerHTML = `<p style="color: red;">Erreur: ${error.message}</p>`;
            }
        }
        
        // Lancer le test au chargement
        testFilters();
    </script>
</body>
</html>
