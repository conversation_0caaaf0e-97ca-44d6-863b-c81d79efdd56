-- Script pour créer la table SteeringOfRoaming dans roaming_dashboard
USE roaming_dashboard;

-- Supprimer la table si elle existe déjà
DROP TABLE IF EXISTS SteeringOfRoaming;

-- Créer la table SteeringOfRoaming basée sur vos données CSV
CREATE TABLE SteeringOfRoaming (
    TestcaseId BIGINT AUTO_INCREMENT PRIMARY KEY,
    OrderId BIGINT,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User VARCHAR(50),
    UserGroup VARCHAR(50),
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry TEXT,
    errorSideId INT,
    errorSideLocation TEXT,
    errorSideNumber VARCHAR(20),
    errorSidePlmn VARCHAR(50),
    errorSideProbe TEXT,
    errorSideRxLevel INT,
    errorSideUsedPLMNName TEXT,
    errorState VARCHAR(50),
    errorStateId INT,
    Completed TINYINT(1),
    Failure TINYINT(1),
    Incomplete TINYINT(1),
    L3_Flag TINYINT(1),
    ServiceType VARCHAR(10),
    Success TINYINT(1),
    
    -- Colonnes de données de roaming (basées sur vos CSV)
    a_location_country VARCHAR(100),
    a_location TEXT,
    a_number VARCHAR(50),
    a_imsi VARCHAR(50),
    a_Usedplmnname VARCHAR(255),
    a_usedplmn VARCHAR(50),
    a_TADIG VARCHAR(50),
    a_VPLMN_registered VARCHAR(100),
    a_networkType VARCHAR(50),
    a_LupDuration FLOAT,
    a_NrOfPlmnsRejected VARCHAR(100),
    a_nrofluprequests INT,
    a_rejectCauses VARCHAR(255),
    a_rejectedPLMNs VARCHAR(255),
    
    -- Colonnes temporelles pour les analyses (importantes pour vos dashboards)
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    WeekOfYear INT,
    
    -- Colonnes d'attachement (pour vos graphiques)
    attache0 FLOAT DEFAULT 0,
    attache1 FLOAT DEFAULT 0,
    attache2 FLOAT DEFAULT 0,
    attache3 FLOAT DEFAULT 0,
    attache4 FLOAT DEFAULT 0,
    attache5 FLOAT DEFAULT 0,
    attache6 FLOAT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances (basés sur vos requêtes)
CREATE INDEX idx_steering_timestamp ON SteeringOfRoaming(Timestamp);
CREATE INDEX idx_steering_country ON SteeringOfRoaming(a_location_country);
CREATE INDEX idx_steering_verdict ON SteeringOfRoaming(Verdict);
CREATE INDEX idx_steering_tcname ON SteeringOfRoaming(TCName(100));
CREATE INDEX idx_steering_temporal ON SteeringOfRoaming(DayOfMonth, DayOfWeek, HourOfDay, MonthOfYear, WeekOfYear);

-- Insérer quelques données de test pour vérifier que tout fonctionne
INSERT INTO SteeringOfRoaming (
    TCName, Timestamp, Verdict, a_location_country, a_networkType, 
    DayOfMonth, DayOfWeek, HourOfDay, MonthOfYear, WeekOfYear,
    attache0, attache1, attache2, Success
) VALUES 
('SteeringOfRoaming', NOW(), 'PASS', 'France', '4G', 
 DAY(NOW()), DAYNAME(NOW()), HOUR(NOW()), MONTH(NOW()), WEEK(NOW()),
 0.85, 0.92, 0.78, 1),
('SteeringOfRoaming', NOW(), 'FAIL', 'Spain', '4G', 
 DAY(NOW()), DAYNAME(NOW()), HOUR(NOW()), MONTH(NOW()), WEEK(NOW()),
 0.45, 0.52, 0.38, 0),
('SteeringOfRoaming', NOW(), 'PASS', 'Italy', '5G', 
 DAY(NOW()), DAYNAME(NOW()), HOUR(NOW()), MONTH(NOW()), WEEK(NOW()),
 0.95, 0.88, 0.91, 1),
('SteeringOfRoaming', NOW(), 'PASS', 'Germany', '4G', 
 DAY(NOW()), DAYNAME(NOW()), HOUR(NOW()), MONTH(NOW()), WEEK(NOW()),
 0.78, 0.85, 0.82, 1),
('SteeringOfRoaming', NOW(), 'INCONC', 'UK', '4G', 
 DAY(NOW()), DAYNAME(NOW()), HOUR(NOW()), MONTH(NOW()), WEEK(NOW()),
 0.65, 0.72, 0.68, 0);

-- Vérifier que les données ont été insérées
SELECT 'Table SteeringOfRoaming créée avec succès!' as message;
SELECT COUNT(*) as total_records FROM SteeringOfRoaming;
SELECT DISTINCT a_location_country as countries FROM SteeringOfRoaming WHERE a_location_country IS NOT NULL;
