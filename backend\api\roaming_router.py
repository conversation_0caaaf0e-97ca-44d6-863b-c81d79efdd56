from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, extract
from typing import List, Dict, Any, Optional
from datetime import datetime
import pandas as pd
import statistics

from .models import (
    SteeringOfRoaming, 
    SteeringOfRoamingRead, 
    SuccessRateResponse, 
    FailCauseResponse, 
    FailAnalysisResponse,
    get_db
)

router = APIRouter(
    prefix="/api/roaming",
    tags=["roaming"],
    responses={404: {"description": "Not found"}},
)

# @router.get("/success-rate", response_model=List[SuccessRateResponse])
# async def get_success_rate(
#     country: Optional[str] = None,
#     week: Optional[int] = None,
#     operator: Optional[str] = None,
#     year: Optional[int] = None,
#     month: Optional[int] = None,
#     quarter: Optional[int] = None,
#     limit: int = Query(50, ge=1, le=100),
#     db: Session = Depends(get_db)
# ):
#     """
#     Récupère les taux de succès par pays.
#     
#     Paramètres:
#     - country: Filtrer par pays spécifique
#     - week: Filtrer par semaine de l'année
#     - operator: Filtrer par opérateur
#     - year: Filtrer par année
#     - month: Filtrer par mois
#     - quarter: Filtrer par trimestre
#     - limit: Nombre maximum de résultats à retourner
#     
#     Retourne la liste des pays avec leur taux de succès.
#     """
#     # Construction de la requête de base
#     query = db.query(SteeringOfRoaming)
#     
#     # Application des filtres
#     if country:
#         query = query.filter(SteeringOfRoaming.a_location_country == country)
#     if week:
#         query = query.filter(SteeringOfRoaming.weekofyear == week)
#     if operator:
#         query = query.filter(SteeringOfRoaming.a_UsedPLMNName == operator)
#     if year:
#         query = query.filter(extract('year', SteeringOfRoaming.Timestamp) == year)
#     if month:
#         query = query.filter(extract('month', SteeringOfRoaming.Timestamp) == month)
#     if quarter:
#         # Calcul du trimestre à partir du mois (1-4)
#         query = query.filter(((extract('month', SteeringOfRoaming.Timestamp)-1)//3+1) == quarter)
#     
#     # Exécution de la requête
#     results = query.all()
#     
#     # Transformation en DataFrame pour faciliter l'analyse
#     if not results:
#         return []
#     
#     # Organiser les données par pays
#     success_rates = {}
#     for record in results:
#         country = record.a_location_country
#         verdict = record.Verdict
#         
#         if country not in success_rates:
#             success_rates[country] = {"success": 0, "total": 0}
#         
#         success_rates[country]["total"] += 1
#         if verdict == "Success" or verdict == "PASS":
#             success_rates[country]["success"] += 1
#     
#     # Calculer les taux de succès
#     response = []
#     for country, data in success_rates.items():
#         if data["total"] > 0:
#             success_rate = (data["success"] / data["total"]) * 100
#             response.append({
#                 "country": country,
#                 "success_rate": round(success_rate, 2),
#                 "total_connections": data["total"]
#             })
#     
#     # Trier par taux de succès décroissant
#     response.sort(key=lambda x: x["success_rate"], reverse=True)
#     
#     # Limiter le nombre de résultats
#     return response[:limit]

@router.get("/fail-causes", response_model=FailAnalysisResponse)
async def get_fail_causes(
    country: Optional[str] = None,
    week: Optional[int] = None,
    operator: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Analyse les causes d'échec des tests de roaming.
    
    Paramètres:
    - country: Filtrer par pays spécifique
    - week: Filtrer par semaine de l'année
    - operator: Filtrer par opérateur
    
    Retourne l'analyse détaillée des causes d'échec.
    """
    # Construction de la requête de base
    query = db.query(SteeringOfRoaming).filter(
        SteeringOfRoaming.Verdict.in_(["Fail", "FAIL", "ERROR", "Error"])
    )
    
    # Application des filtres
    if country:
        query = query.filter(SteeringOfRoaming.a_location_country == country)
    if week:
        query = query.filter(SteeringOfRoaming.weekofyear == week)
    if operator:
        query = query.filter(SteeringOfRoaming.a_UsedPLMNName == operator)
    
    # Exécution de la requête
    fail_records = query.all()
    
    if not fail_records:
        return {
            "causes": [],
            "rejected_plmns": {},
            "details": []
        }
    
    # Analyser les causes d'erreur
    error_causes = {}
    rejected_plmns = {}
    details = []
    
    for record in fail_records:
        # Analyser l'errorText
        error_text = record.errorText or ""
        cause = error_text[:100] if error_text else "Unknown"
        
        if cause in error_causes:
            error_causes[cause] += 1
        else:
            error_causes[cause] = 1
            
        # Analyser les PLMNs rejetés
        rejected = record.a_RejectedPLMNs or ""
        if rejected:
            for plmn in rejected.split():
                plmn = plmn.strip()
                if plmn:
                    if plmn in rejected_plmns:
                        rejected_plmns[plmn] += 1
                    else:
                        rejected_plmns[plmn] = 1
        
        # Collecter les détails
        details.append({
            "id": record.id,
            "country": record.a_location_country,
            "operator": record.a_UsedPLMNName,
            "error_text": error_text,
            "rejected_plmns": rejected,
            "network_type": record.a_NetworkType,
            "lup_duration": record.a_LupDuration,
            "timestamp": record.Timestamp.isoformat() if record.Timestamp else None
        })
    
    # Calculer les pourcentages pour les causes
    total_fails = len(fail_records)
    causes_list = []
    
    for cause, count in sorted(error_causes.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / total_fails) * 100
        causes_list.append({
            "cause": cause,
            "count": count,
            "percentage": round(percentage, 2)
        })
    
    return {
        "causes": causes_list,
        "rejected_plmns": rejected_plmns,
        "details": details
    }

@router.get("/raw-data", response_model=List[SteeringOfRoamingRead])
async def get_raw_data(
    country: Optional[str] = None,
    week: Optional[int] = None,
    operator: Optional[str] = None,
    verdict: Optional[str] = None,
    limit: int = Query(100, ge=1, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db)
):
    """
    Récupère les données brutes des tests de roaming avec pagination.
    
    Paramètres:
    - country: Filtrer par pays spécifique
    - week: Filtrer par semaine de l'année
    - operator: Filtrer par opérateur
    - verdict: Filtrer par verdict (Success, Fail, etc.)
    - limit: Nombre maximum de résultats à retourner
    - offset: Décalage pour la pagination
    
    Retourne les données brutes avec pagination.
    """
    # Construction de la requête de base
    query = db.query(SteeringOfRoaming)
    
    # Application des filtres
    if country:
        query = query.filter(SteeringOfRoaming.a_location_country == country)
    if week:
        query = query.filter(SteeringOfRoaming.weekofyear == week)
    if operator:
        query = query.filter(SteeringOfRoaming.a_UsedPLMNName == operator)
    if verdict:
        query = query.filter(SteeringOfRoaming.Verdict == verdict)
    
    # Pagination
    total = query.count()
    records = query.order_by(SteeringOfRoaming.Timestamp.desc()).offset(offset).limit(limit).all()
    
    return records

@router.get("/operators")
async def get_operators(
    country: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Récupère la liste des opérateurs disponibles dans les données.
    
    Paramètres:
    - country: Filtrer les opérateurs par pays spécifique
    
    Retourne la liste des opérateurs avec leur nombre d'occurrences.
    """
    # Construction de la requête de base
    query = db.query(
        SteeringOfRoaming.a_UsedPLMNName,
        db.func.count(SteeringOfRoaming.id).label("count")
    )
    
    # Filtrer par pays si spécifié
    if country:
        query = query.filter(SteeringOfRoaming.a_location_country == country)
    
    # Grouper par opérateur et trier par nombre d'occurrences
    operators = query.group_by(SteeringOfRoaming.a_UsedPLMNName)\
                    .order_by(db.func.count(SteeringOfRoaming.id).desc())\
                    .all()
    
    return [{"operator": op[0], "count": op[1]} for op in operators if op[0]]

@router.get("/countries")
async def get_countries(db: Session = Depends(get_db)):
    """
    Récupère la liste des pays disponibles dans les données.
    
    Retourne la liste des pays avec leur nombre d'occurrences.
    """
    # Construction de la requête de base
    countries = db.query(
        SteeringOfRoaming.a_location_country,
        db.func.count(SteeringOfRoaming.id).label("count")
    ).group_by(SteeringOfRoaming.a_location_country)\
     .order_by(db.func.count(SteeringOfRoaming.id).desc())\
     .all()
    
    return [{"country": country[0], "count": country[1]} for country in countries if country[0]]

@router.get("/weeks")
async def get_weeks(db: Session = Depends(get_db)):
    """
    Récupère la liste des semaines disponibles dans les données.
    
    Retourne la liste des semaines avec leur nombre d'occurrences.
    """
    # Construction de la requête de base
    weeks = db.query(
        SteeringOfRoaming.weekofyear,
        func.count(SteeringOfRoaming.weekofyear).label("count")
    ).group_by(SteeringOfRoaming.weekofyear)\
     .order_by(SteeringOfRoaming.weekofyear)\
     .all()
    
    return [{"week": week[0], "count": week[1]} for week in weeks if week[0]]

@router.get("/top_data_consumption_countries")
async def get_top_data_consumption_countries(db: Session = Depends(get_db)):
    """
    Récupère les 20 pays qui consomment le plus de données en roaming.
    
    Retourne un tableau des pays avec leur consommation de données, nombre de connexions,
    et pourcentage de changement par rapport à la période précédente.
    """
    try:
        # Construction de la requête pour récupérer les données par pays
        query = db.query(
            SteeringOfRoaming.a_location_country.label("country"),
            db.func.count(SteeringOfRoaming.id).label("connections"),
            db.func.sum(SteeringOfRoaming.a_DataVolume).label("dataUsage")
        ).filter(
            SteeringOfRoaming.a_location_country.isnot(None),
            SteeringOfRoaming.a_location_country != ''
        ).group_by(
            SteeringOfRoaming.a_location_country
        ).order_by(
            db.func.sum(SteeringOfRoaming.a_DataVolume).desc()
        ).limit(20)
        
        countries_data = query.all()
        
        # Calculer le pourcentage de changement (simulé pour cet exemple)
        # Dans une implémentation réelle, vous pourriez comparer avec des données historiques
        import random
        
        result = []
        for country in countries_data:
            # Conversion en dictionnaire pour faciliter la manipulation
            country_dict = {
                "country": country.country,
                "connections": country.connections,
                "dataUsage": float(country.dataUsage) / 1024 / 1024 / 1024,  # Convertir en TB
                "percentChange": random.uniform(-20, 30)  # Simuler un changement entre -20% et +30%
            }
            result.append(country_dict)
        
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des données: {str(e)}") 