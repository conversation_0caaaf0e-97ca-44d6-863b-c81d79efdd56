// Ce fichier est temporaire et contient les modifications à appliquer
// à NetworkDashboardPage.tsx

// Fonction à ajouter pour charger les causes d'échec
const loadFailureCauses = async (country?: string, operator?: string) => {
  setLoadingFailureCauses(true);
  try {
    console.log("Chargement des causes d'échec avec filtres:", { country, operator, verdict: 'fail' });
    
    const response = await getFailureCauses(
      country && country !== '' && country !== 'all' ? country : undefined,
      operator && operator !== '' && operator !== 'all' ? operator : undefined,
      'fail'
    );
    
    if (response && response.success && response.data) {
      const failureCauses = response.data;
      
      // Préparer les données pour le graphique circulaire des causes d'échec
      const labels = failureCauses.map((item: FailureCause) => item.cause);
      const data = failureCauses.map((item: FailureCause) => item.count);
      const backgroundColor = [
        'rgba(255, 99, 132, 0.6)',   // Rouge
        'rgba(255, 159, 64, 0.6)',   // Orange
        'rgba(255, 205, 86, 0.6)',   // Jaune
        'rgba(75, 192, 192, 0.6)',   // Vert
        'rgba(54, 162, 235, 0.6)',   // Bleu
        'rgba(153, 102, 255, 0.6)',  // Violet
        'rgba(201, 203, 207, 0.6)'   // Gris
      ];
      const borderColor = backgroundColor.map(color => color.replace('0.6', '1'));
      
      const chartData = {
        labels,
        datasets: [{
          label: 'Causes d\'échec',
          data,
          backgroundColor,
          borderColor,
          borderWidth: 1
        }],
        _rawData: failureCauses  // Stocker les données brutes pour un accès ultérieur
      };
      
      setFailureCausesData(chartData);
    } else {
      console.error('Données de causes d\'échec invalides ou vides:', response);
      setFailureCausesData(null);
    }
  } catch (error) {
    console.error('Erreur lors du chargement des causes d\'échec:', error);
    setFailureCausesData(null);
  } finally {
    setLoadingFailureCauses(false);
  }
};

// Modification à apporter à handleFilterChange
// Si le verdict change vers "fail", charger les causes d'échec
if (newFilters.verdict === 'fail') {
  await loadFailureCauses(
    newFilters.country,
    newFilters.operator
  );
}

// Code JSX pour afficher les causes d'échec quand verdict=fail
{filters.verdict === 'fail' ? (
  /* Graphique des causes d'échec */
  <div className="bg-white rounded-lg shadow-md p-4">
    <h2 className="text-lg font-semibold mb-4">Causes d'Échec</h2>
    {loadingFailureCauses ? (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    ) : failureCausesData ? (
      <div className="h-64">
        <Pie 
          data={failureCausesData}
          options={{
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                position: 'right' as const,
              },
              title: {
                display: true,
                text: 'Répartition des Causes d\'Échec'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.raw as number;
                    const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                    const percentage = Math.round((value / total) * 100);
                    return `${label}: ${value} (${percentage}%)`;
                  }
                }
              }
            }
          }}
        />
      </div>
    ) : (
      <div className="flex justify-center items-center h-64 text-gray-500">
        Aucune donnée disponible pour les causes d'échec
      </div>
    )}
  </div>
) : (
  /* Affichage normal des graphiques */
)}

// Code JSX pour afficher les détails par pays des causes d'échec
{filters.verdict === 'fail' && failureCausesData && failureCausesData._rawData ? (
  /* Affichage détaillé des pays par cause d'échec */
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
    {failureCausesData._rawData.map((cause: FailureCause, index: number) => (
      <div key={index} className="bg-red-50 p-4 rounded-lg">
        <h3 className="text-sm font-medium text-red-800">Cause: {cause.cause || "Inconnu"}</h3>
        <p className="text-lg font-bold text-red-600">{cause.count} échecs ({cause.percentage.toFixed(1)}%)</p>
        {cause.countries && Object.keys(cause.countries).length > 0 && (
          <div className="mt-2">
            <p className="text-xs text-red-700 font-medium mb-1">Pays concernés:</p>
            <div className="text-xs text-red-700 max-h-24 overflow-y-auto">
              {Object.entries(cause.countries)
                .sort(([, countA], [, countB]) => (countB as number) - (countA as number))
                .map(([country, count], i) => (
                  <div key={i} className="flex justify-between">
                    <span>{country}</span>
                    <span>{count} échecs</span>
                  </div>
                ))
              }
            </div>
          </div>
        )}
      </div>
    ))}
  </div>
) : (
  /* Affichage normal */
)} 