import {
    BarElement,
    CategoryScale,
    Chart as ChartJS,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip
} from 'chart.js';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { getHttpDownloadPerformance, getHttpPeriodData, HttpDownloadPerformance } from '../services/kpiService';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

interface NetworkPerformanceStatsProps {
  filters?: {
    country?: string;
    operator?: string;
    dateRange?: {
      startDate?: string;
      endDate?: string;
    };
  };
}

type TimePeriod = 'day' | 'week' | 'month' | 'quarter' | 'year';

const NetworkPerformanceStatsComponent = forwardRef<any, NetworkPerformanceStatsProps>(({ filters }, ref) => {
  const [selectedCountry, setSelectedCountry] = useState<string>('all');
  const [selectedOperator, setSelectedOperator] = useState<string>('all');
  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>('month');
  
  const [httpData, setHttpData] = useState<HttpDownloadPerformance | null>(null);
  const [loadingHttpData, setLoadingHttpData] = useState<boolean>(true);
  
  const [periodData, setPeriodData] = useState<any[]>([]);
  const [loadingPeriodData, setLoadingPeriodData] = useState<boolean>(false);
  
  const [currentPage, setCurrentPage] = useState<number>(1);
  const itemsPerPage = 10;

  // Exposer la méthode setSelectedTimePeriod au composant parent via la ref
  useImperativeHandle(ref, () => ({
    setSelectedTimePeriod
  }));

  // Mettre à jour les filtres internes lorsque les filtres externes changent
  useEffect(() => {
    if (filters) {
      if (filters.country) {
        setSelectedCountry(filters.country);
      }
      if (filters.operator) {
        setSelectedOperator(filters.operator);
      }
    }
  }, [filters]);

  useEffect(() => {
    const fetchHttpData = async () => {
      setLoadingHttpData(true);
      try {
        // Utiliser les filtres pour l'appel API si disponibles
        const country = filters?.country || selectedCountry;
        const operator = filters?.operator || selectedOperator;
        const startDate = filters?.dateRange?.startDate;
        const endDate = filters?.dateRange?.endDate;
        
        const result = await getHttpDownloadPerformance();
        setHttpData(result);
      } catch (error) {
        console.error('Erreur lors de la récupération des données HTTP:', error);
      } finally {
        setLoadingHttpData(false);
      }
    };
    fetchHttpData();
  }, [filters, selectedCountry, selectedOperator]);

  useEffect(() => {
    // Utiliser les filtres pour déterminer le pays et l'opérateur
    const country = filters?.country || selectedCountry;
    const operator = filters?.operator || selectedOperator;
    
    if (!country || country === 'all') {
      setPeriodData([]);
      return;
    }

    const fetchPeriodData = async () => {
      setLoadingPeriodData(true);
      try {
        const startDate = filters?.dateRange?.startDate;
        const endDate = filters?.dateRange?.endDate;
        
        const response = await getHttpPeriodData(
          selectedTimePeriod, 
          country, 
          operator !== 'all' ? operator : undefined,
          undefined, // year
          undefined, // month
          undefined, // week
          undefined  // quarter
        );
        
        if (response && Array.isArray(response.data)) {
          setPeriodData(response.data);
        } else {
          setPeriodData([]);
        }
        } catch (error) {
        console.error(`Erreur lors de la récupération des données de la période :`, error);
        setPeriodData([]);
        } finally {
        setLoadingPeriodData(false);
      }
    };

    fetchPeriodData();
  }, [filters, selectedCountry, selectedOperator, selectedTimePeriod]);

  const availableOperators = httpData?.by_country_operator
    ?.filter(item => {
      const country = filters?.country || selectedCountry;
      return country === 'all' || item.country === country;
    })
    .map(item => item.operator)
    .filter((value, index, self) => self.indexOf(value) === index)
    .sort() || [];

  const prepareMainChart = () => {
    if (!httpData || !httpData.by_country_operator) {
      return { chartData: { labels: [], datasets: [] }, totalPages: 0 };
    }

    // Filtrer les données invalides (opérateur vide, pas de débit, etc.)
    let cleanData = httpData.by_country_operator.filter(
      d => d.operator && ((d.avg_data_rate_mbps ?? 0) > 0 || (d.avg_activation_time_s ?? 0) > 0)
    );
    
    // Appliquer les filtres externes si disponibles
    if (filters) {
      if (filters.country && filters.country !== 'all') {
        cleanData = cleanData.filter(d => d.country === filters.country);
      }
      if (filters.operator && filters.operator !== 'all') {
        cleanData = cleanData.filter(d => d.operator === filters.operator);
      }
    }

    const country = filters?.country || selectedCountry;
    const operator = filters?.operator || selectedOperator;

    let finalData;

    if (country === 'all') {
      // Agréger les données par pays si "Tous les pays" est sélectionné
      const groupedByCountry = cleanData.reduce((acc, curr) => {
        const country = curr.country;
        if (!acc[country]) {
          acc[country] = {
            label: country,
            total_rate: 0,
            total_time: 0,
            count: 0
          };
        }
        acc[country].total_rate += curr.avg_data_rate_mbps;
        acc[country].total_time += curr.avg_activation_time_s;
        acc[country].count++;
        return acc;
      }, {} as { [key: string]: { label: string; total_rate: number; total_time: number; count: number } });

      finalData = Object.values(groupedByCountry).map(item => ({
        label: item.label,
        avg_data_rate_mbps: item.total_rate / item.count,
        avg_activation_time_s: item.total_time / item.count
      }));
    } else {
      // Filtrer par pays (et opérateur) si un pays spécifique est sélectionné
      let countryData = cleanData.filter(d => d.country === country);
      if (operator !== 'all') {
        countryData = countryData.filter(d => d.operator === operator);
      }
      finalData = countryData.map(item => ({
        label: item.operator,
        avg_data_rate_mbps: item.avg_data_rate_mbps,
        avg_activation_time_s: item.avg_activation_time_s,
      }));
    }
    
    const totalPages = Math.ceil(finalData.length / itemsPerPage);
    const paginatedData = finalData.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);

    const labels = paginatedData.map(d => d.label);
    const dataRates = paginatedData.map(d => d.avg_data_rate_mbps);
    const activationTimes = paginatedData.map(d => d.avg_activation_time_s);

    const chartData = {
      labels,
      datasets: [
        {
          label: 'Débit moyen (Mbps)',
          data: dataRates,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          yAxisID: 'y',
        },
        {
          label: 'Temps d\'activation moyen (s)',
          data: activationTimes,
          backgroundColor: 'rgba(255, 159, 64, 0.6)',
          yAxisID: 'y1',
        }
      ]
    };

    return { chartData, totalPages };
  };

  const prepareEvolutionChart = () => {
    if (!periodData || periodData.length === 0) {
      return { labels: [], datasets: [] };
    }
    const sortedData = [...periodData].sort((a, b) => {
      // Gérer les différents noms de champ de date
      const valA = a.period_value || a.date;
      const valB = b.period_value || b.date;
      if (valA && valB) {
        return String(valA).localeCompare(String(valB));
      }
      return 0;
    });
    
    // Utiliser period_value ou date pour les labels
    const labels = sortedData.map(d => d.period_value || d.date || 'Inconnu');
    const dataRates = sortedData.map(d => d.avg_data_rate_mbps);
    const successRates = sortedData.map(d => d.success_rate);

    return {
      labels,
      datasets: [
        {
          label: 'Débit moyen (Mbps)',
          data: dataRates,
          backgroundColor: 'rgba(75, 192, 192, 0.6)',
          yAxisID: 'y',
        },
        {
          label: 'Taux de succès (%)',
          data: successRates,
          backgroundColor: 'rgba(255, 159, 64, 0.6)',
          yAxisID: 'y1',
        }
      ]
    };
  };

  const mainChart = prepareMainChart();
  const evolutionChartData = prepareEvolutionChart();

  const mainChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { 
        legend: { position: 'top' as const },
        tooltip: {
            callbacks: {
                label: function(context: any) {
                    let label = context.dataset.label || '';
                    if (label) {
                        label += ': ';
                    }
                    if (context.parsed.y !== null) {
                        // Afficher plus de décimales pour le temps d'activation
                        if (context.dataset.yAxisID === 'y1') {
                            label += context.parsed.y.toFixed(5) + ' s';
    } else {
                            label += context.parsed.y.toFixed(2) + ' Mbps';
                        }
                    }
                    return label;
                }
            }
        }
    },
    scales: {
      x: {
        type: 'category' as const,
        grid: { offset: true },
        ticks: { align: 'center' as const },
      },
      y: { type: 'linear' as const, display: true, position: 'left' as const, title: { display: true, text: 'Débit (Mbps)' }, beginAtZero: true },
      y1: { 
          type: 'linear' as const, 
          display: true, 
          position: 'right' as const, 
          title: { display: true, text: 'Temps (s)' }, 
          grid: { drawOnChartArea: false }, 
          beginAtZero: true,
          ticks: {
              callback: function(value: any) {
                  // Forcer l'affichage en format décimal
                  return Number(value).toFixed(5);
              }
          }
      }
    }
  };

  const evolutionChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { position: 'top' as const } },
    scales: {
      x: {
        type: 'category' as const,
        grid: { offset: true },
        ticks: { align: 'center' as const },
      },
      y: { type: 'linear' as const, display: true, position: 'left' as const, title: { display: true, text: 'Débit (Mbps)' }, beginAtZero: true },
      y1: { 
          type: 'linear' as const, 
          display: true, 
          position: 'right' as const, 
          title: { display: true, text: 'Taux Succès (%)' }, 
          grid: { drawOnChartArea: false }, 
          beginAtZero: true,
          suggestedMax: 100
      }
    }
  };
  
  const renderPagination = (totalPages: number) => (
    totalPages > 1 && (
      <div className="flex justify-center mt-4">
        {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
          <button key={page} onClick={() => setCurrentPage(page)} className={`px-4 py-2 mx-1 rounded-md ${currentPage === page ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
            {page}
          </button>
        ))}
            </div>
    )
  );
  
  if (loadingHttpData) {
    return <div className="p-4">Chargement des données...</div>;
  }

  // Utiliser les filtres externes ou les filtres internes
  const effectiveCountry = filters?.country || selectedCountry;
  const effectiveOperator = filters?.operator || selectedOperator;

  return (
    <div className="p-4 bg-white rounded-lg shadow-md">
      <h2 className="text-xl font-bold">Performance Test de Débit HTTP Par Pays</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
        <div>
          <label htmlFor="country-filter" className="block text-sm font-medium text-gray-700 mb-1">Filtrer par pays:</label>
          <select 
            id="country-filter" 
            value={effectiveCountry} 
            onChange={e => { 
              setSelectedCountry(e.target.value); 
              setSelectedOperator('all'); 
              setCurrentPage(1); 
            }} 
            disabled={!!filters?.country}
            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md"
          >
            <option value="all">Tous les pays</option>
            {[...new Set(httpData?.by_country_operator.map(item => item.country) || [])].sort().map(country => (
              <option key={country} value={country}>{country}</option>
            ))}
          </select>
        </div>
        <div>
          <label htmlFor="operator-filter" className="block text-sm font-medium text-gray-700 mb-1">Filtrer par opérateur:</label>
          <select 
            id="operator-filter" 
            value={effectiveOperator} 
            onChange={e => setSelectedOperator(e.target.value)} 
            disabled={effectiveCountry === 'all' || !!filters?.operator} 
            className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md"
          >
            <option value="all">Tous les opérateurs</option>
            {availableOperators.map(op => <option key={op} value={op}>{op}</option>)}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Période:</label>
          <div className="flex gap-1">
            {(['day', 'week', 'month', 'quarter', 'year'] as TimePeriod[]).map(period => (
              <button 
                key={period} 
                onClick={() => setSelectedTimePeriod(period)} 
                className={`px-3 py-1 text-sm rounded-md ${selectedTimePeriod === period ? 'bg-blue-500 text-white' : 'bg-gray-100'}`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        </div>
      </div>

      <div className="h-96">
        <Bar data={mainChart.chartData} options={mainChartOptions} />
      </div>
      {renderPagination(mainChart.totalPages)}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-700 mb-2">Explication des métriques:</h4>
        <ul className="list-disc list-inside text-sm text-gray-600">
          <li><strong>Débit moyen (Mbps)</strong>: Vitesse moyenne de téléchargement HTTP.</li>
          <li><strong>Temps de Réponse (s)</strong>: Durée moyenne d'activation du contexte PDP.</li>
        </ul>
      </div>

      {effectiveCountry !== 'all' && (
        <div className="mt-8">
          <h3 className="text-lg font-medium mb-4">Évolution pour {effectiveCountry} {effectiveOperator !== 'all' ? `(${effectiveOperator})` : ''}</h3>
          {loadingPeriodData ? <div className="text-center p-4">Chargement...</div> :
            <div className="h-96"><Bar data={evolutionChartData} options={evolutionChartOptions} /></div>
          }
        </div>
      )}
    </div>
  );
});

export default NetworkPerformanceStatsComponent; 
