import { BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
import React, { useRef } from 'react';
import NetworkPerformanceStatsComponent from '../components/NetworkPerformanceStats';

// Enregistrer les composants de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const HttpDashboardPage: React.FC = () => {
  const networkPerformanceRef = useRef<any>(null);

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Tableau de Bord - HTTP Download</h1>
      
      {/* Section du graphique de performance HTTP */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <NetworkPerformanceStatsComponent ref={networkPerformanceRef} />
      </div>
    </div>
  );
};

export default HttpDashboardPage; 