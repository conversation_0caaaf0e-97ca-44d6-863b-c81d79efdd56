-- Table principale pour les informations de test
CREATE TABLE TestRuns (
    TestRunId BIGINT AUTO_INCREMENT PRIMARY KEY,
    TestcaseId BIGINT,
    OrderId BIGINT,
    TCName VARCHAR(255),
    Timestamp DATETIME(3),
    Verdict VARCHAR(255),
    TestDefinitionPath VARCHAR(255),
    `User` VARCHAR(255),
    UserGroup VARCHAR(255),
    TCTestDefinitionId BIGINT,
    Completed TINYINT,
    Failure TINYINT,
    Incomplete TINYINT,
    Success TINYINT,
    ServiceType VARCHAR(255),
    CauseText_L3 VARCHAR(255),
    CauseValue_L3 INT,
    L3_Flag VARCHAR(255),
    TCDuration TIME(3),
    TestId BIGINT,
    TestrunId BIGINT,
    Description VARCHAR(255),
    Valid TINYINT,
    INDEX(TestcaseId)
);

-- Table pour les informations temporelles
CREATE TABLE TimeInfo (
    TimeInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    TestRunId BIGINT,
    DayO<PERSON>Month INT,
    DayOfWeek VARCHAR(255),
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone VARCHAR(255),
    WeekOfYear INT,
    FOREIGN KEY (TestRunId) REFERENCES TestRuns(TestRunId) ON DELETE CASCADE
);

-- Table pour les informations d'erreur
CREATE TABLE ErrorInfo (
    ErrorInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    TestRunId BIGINT,
    errorId INT,
    errorText VARCHAR(255),
    errorSideCountry VARCHAR(255),
    errorSideId INT,
    errorSideLocation VARCHAR(255),
    errorSideNumber VARCHAR(255),
    errorSidePlmn VARCHAR(255),
    errorSideProbe VARCHAR(255),
    errorSideRxLevel VARCHAR(255),
    errorSideUsedPLMNName VARCHAR(255),
    errorState VARCHAR(255),
    errorStateId INT,
    FOREIGN KEY (TestRunId) REFERENCES TestRuns(TestRunId) ON DELETE CASCADE
);

-- Table pour les informations d'exécution
CREATE TABLE ExecutionInfo (
    ExecutionInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    TestRunId BIGINT,
    Distance VARCHAR(255),
    ExecutionHost VARCHAR(255),
    ExecutionId BIGINT,
    ExternalNumber VARCHAR(255),
    ExternalNumberOrURI VARCHAR(255),
    unitsMaxTimeOffset DECIMAL(5,1),
    GRP INT,
    GlobalResourceCount INT,
    RecordId BIGINT,
    InsertId BIGINT,
    FOREIGN KEY (TestRunId) REFERENCES TestRuns(TestRunId) ON DELETE CASCADE
);

-- Table pour les données de sonde (a, b, c, d)
CREATE TABLE ProbeData (
    ProbeDataId BIGINT AUTO_INCREMENT PRIMARY KEY,
    TestRunId BIGINT,
    ProbeLabel CHAR(1),
    EcNO DECIMAL(5,1),
    HappyEyeBallSelectedIPVersion VARCHAR(255),
    IP_Version VARCHAR(255),
    IPv4_Used VARCHAR(255),
    IPv6_Used VARCHAR(255),
    InitialLupReqTime DATETIME(3),
    LTE_RgAttachDuration VARCHAR(255),
    LupAcceptDuration TIME(3),
    LupAcceptDuration_All_VPLMN TIME(3),
    LupDuration TIME(3),
    LupMode VARCHAR(255),
    LupRejectDuration TIME(3),
    NrOfLupRejects_All_VPLMN INT,
    NrOfLupRequests INT,
    NrOfLupRequests_All_VPLMN INT,
    NrOfPlmnsRejected INT,
    OverallNrOfLupRequests INT,
    RejectCauses VARCHAR(255),
    RejectedPLMNs VARCHAR(255),
    SimAuthenticationAfterAttach TINYINT,
    SimAuthenticationAfterLup TINYINT,
    FOREIGN KEY (TestRunId) REFERENCES TestRuns(TestRunId) ON DELETE CASCADE,
    INDEX(ProbeLabel)
);

-- Table pour les informations réseau de chaque sonde
CREATE TABLE NetworkInfo (
    NetworkInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    ProbeDataId BIGINT,
    TAC VARCHAR(255),
    UsedPLMNNameShort VARCHAR(255),
    VPLMN_registered VARCHAR(255),
    _5QI_dedicated_L3 VARCHAR(255),
    _5QI_default_L3 VARCHAR(255),
    LTE_Freq VARCHAR(255),
    _5G_NSA_availability VARCHAR(255),
    _5G_NSA_used VARCHAR(255),
    ATR_Mobile VARCHAR(255),
    ATR_SimEmu VARCHAR(255),
    CI VARCHAR(255),
    DCNR_restricted VARCHAR(255),
    EcIo DECIMAL(5,1),
    LAC VARCHAR(255),
    LTE_Band VARCHAR(255),
    LTE_Bandwidth VARCHAR(255),
    MMEName VARCHAR(255),
    NID VARCHAR(255),
    NRI_cs VARCHAR(255),
    NRI_ps VARCHAR(255),
    NR_Band VARCHAR(255),
    NR_DL_Bandwidth VARCHAR(255),
    NR_RSRP DECIMAL(5,1),
    NR_RSRQ DECIMAL(5,1),
    NR_SINR DECIMAL(5,1),
    NR_pCI VARCHAR(255),
    NSSAI VARCHAR(255),
    RAC VARCHAR(255),
    RSCP DECIMAL(5,1),
    RSRP DECIMAL(5,1),
    RxLevel DECIMAL(5,1),
    FOREIGN KEY (ProbeDataId) REFERENCES ProbeData(ProbeDataId) ON DELETE CASCADE
);

-- Table pour les informations d'authentification et d'identification
CREATE TABLE AuthenticationInfo (
    AuthenticationInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    ProbeDataId BIGINT,
    SID VARCHAR(255),
    SIM_AuthenticationEnd DATETIME(3),
    SIM_AuthenticationStart DATETIME(3),
    TADIG VARCHAR(255),
    UsedPLMN VARCHAR(255),
    UsedPLMNName VARCHAR(255),
    imsi VARCHAR(255),
    location VARCHAR(255),
    location_country VARCHAR(255),
    number VARCHAR(255),
    plmn VARCHAR(255),
    plmnShort VARCHAR(255),
    probe VARCHAR(255),
    FOREIGN KEY (ProbeDataId) REFERENCES ProbeData(ProbeDataId) ON DELETE CASCADE
);

-- Table pour les informations de qualité et de performance
CREATE TABLE QualityInfo (
    QualityInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    ProbeDataId BIGINT,
    uuCqiAverage VARCHAR(255),
    uuCqiSamples VARCHAR(255),
    uuPppHsdpaUsed VARCHAR(255),
    uuPppHsupaUsed VARCHAR(255),
    NetworkType VARCHAR(255),
    hlr VARCHAR(255),
    mobileType VARCHAR(255),
    type VARCHAR(255),
    UnitGPS VARCHAR(255),
    FOREIGN KEY (ProbeDataId) REFERENCES ProbeData(ProbeDataId) ON DELETE CASCADE
);

-- Table pour les informations de recherche PLMN
CREATE TABLE PLMNSearchInfo (
    PLMNSearchInfoId BIGINT AUTO_INCREMENT PRIMARY KEY,
    ProbeDataId BIGINT,
    SearchedPLMN VARCHAR(255),
    SearchedPLMNName VARCHAR(255),
    SearchedPLMNNameShort VARCHAR(255),
    GlobalUsedPLMN VARCHAR(255),
    FOREIGN KEY (ProbeDataId) REFERENCES ProbeData(ProbeDataId) ON DELETE CASCADE
); 