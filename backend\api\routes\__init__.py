"""
Module d'initialisation pour les routes de l'API
"""
from fastapi import APIRouter

# Import des routeurs
from .roaming_routes import router as roaming_router
from .kpi_routes import router as kpi_routes_router
from .alert_routes import router as alert_routes_router

# Création d'un routeur principal pour les routes
# Pas de préfixe ici car les routeurs ont déjà leur propre préfixe
router = APIRouter()

# Inclusion des routeurs sans préfixe supplémentaire
router.include_router(roaming_router, prefix="")
router.include_router(kpi_routes_router, prefix="")
router.include_router(alert_routes_router, prefix="") 