import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LoginForm from '../components/LoginForm';
import { useAuth } from '../contexts/AuthContext';

const LoginPage = () => {
  const { status } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (status === 'authenticated') {
      navigate('/dashboard', { replace: true });
    }
  }, [status, navigate]);

  return (
    <div
      className="min-h-screen flex items-center justify-center relative"
      style={{
        backgroundImage: 'url(/roaming.png)',
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Overlay pour assombrir l'arrière-plan */}
      <div className="absolute inset-0 bg-black/20"></div>

      {/* Texte GlobalRoamer en haut à gauche */}
      <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-20">
        <h1 className="text-6xl font-bold text-white drop-shadow-lg">
          GlobalRoamer
        </h1>
      </div>
      <div className="relative z-10 w-full max-w-4xl flex rounded-lg shadow-lg overflow-hidden">
        <div
          className="hidden md:block md:w-1/2 bg-primary-500 relative"
          style={{
            backgroundImage: 'url(/roaming.png)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          {/* Overlay pour améliorer la lisibilité du texte */}
          <div className="absolute inset-0 bg-primary-500/70"></div>

          <div className="relative z-10 flex flex-col h-full justify-center items-center text-white p-8">
            <h2 className="text-3xl font-bold mb-4 drop-shadow-lg">Dashboard Roaming</h2>

            {/* Icône Orange Maroc */}
            <div className="mb-6">
              <img
                src="/orange-maroc-logo.svg"
                alt="Orange Maroc"
                className="w-16 h-16 drop-shadow-lg"
              />
            </div>

            <p className="text-lg text-center drop-shadow-md">
              Analysez vos données de roaming en temps réel. Suivez les tendances, optimisez les coûts et améliorez la qualité de service.
            </p>
          </div>
        </div>
        <div className="w-full md:w-1/2 bg-white">
          <div className="flex flex-col items-center justify-center h-full px-4 sm:px-6 lg:px-8 py-12">
            <div className="mb-8 text-center">
              <h1 className="text-3xl font-bold text-gray-900">Bienvenue</h1>
              <p className="mt-2 text-gray-600">Connectez-vous pour accéder à votre tableau de bord</p>
            </div>
            <LoginForm />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;