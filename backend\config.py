"""
Configuration de la base de données et autres paramètres
"""

from pathlib import Path

# Configuration de la base de données MySQL (XAMPP)
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',  # Utilisateur par défaut de XAMPP
    'password': '',  # Mot de passe par défaut de XAMPP est vide
    'port': 3306,    # Port par défaut de MySQL dans XAMPP
    'database': 'roaming_dashboard'  # Nom de votre base de données
}

# Configuration de l'API
API_CONFIG = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': True
}

# Configuration des chemins
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / 'data'
DATA_DIR.mkdir(exist_ok=True) 