import React from 'react';
import { FaChartLine, FaDatabase, FaDownload, FaGlobe, FaTable } from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';

// Interface pour définir la structure d'une table
interface TableOption {
  id: string;
  name: string;
  description: string;
  route: string;
  icon: React.ReactNode;
}

const SelectTablePage: React.FC = () => {
  const navigate = useNavigate();

  // Liste des tables disponibles
  // Cette liste peut être facilement étendue en ajoutant de nouvelles entrées
  const tableOptions: TableOption[] = [
    {
      id: 'steeringofroaming',
      name: 'Steering of Roaming',
      description: 'Analyse des données de steering et performances de roaming',
      route: '/dashboard/steering',
      icon: <FaGlobe className="text-blue-500 text-2xl" />
    },
    {
      id: 'http_download_20',
      name: 'HTTP Download',
      description: 'Analyse des performances de téléchargement HTTP',
      route: '/dashboard/http',
      icon: <FaDownload className="text-green-500 text-2xl" />
    },
    {
      id: 'network_performance',
      name: 'Performance Réseau',
      description: 'Statistiques de performance du réseau',
      route: '/dashboard/network',
      icon: <FaChartLine className="text-purple-500 text-2xl" />
    },
    {
      id: 'all_tables',
      name: 'Toutes les Tables',
      description: 'Afficher toutes les tables (peut être lent)',
      route: '/dashboard',
      icon: <FaTable className="text-red-500 text-2xl" />
    }
  ];

  // Fonction pour naviguer vers la route sélectionnée
  const handleSelectTable = (route: string) => {
    navigate(route);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="text-center mb-10">
        <h1 className="text-3xl font-bold text-primary-700 mb-4">Bienvenue sur le Tableau de Bord</h1>
        <p className="text-lg text-gray-600">Veuillez choisir une table à afficher :</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {tableOptions.map((option) => (
          <div
            key={option.id}
            className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden cursor-pointer border border-gray-100"
            onClick={() => handleSelectTable(option.route)}
          >
            <div className="p-6">
              <div className="flex items-center mb-4">
                {option.icon}
                <h2 className="text-xl font-semibold ml-3 text-gray-800">{option.name}</h2>
              </div>
              <p className="text-gray-600">{option.description}</p>
              <div className="mt-6 flex justify-end">
                <button
                  className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors duration-300 flex items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectTable(option.route);
                  }}
                >
                  <FaDatabase className="mr-2" />
                  Sélectionner
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SelectTablePage; 