import React, { useEffect, useState } from 'react';
import { getFailureDetails } from '../services/kpiService';

interface FailureCause {
  cause: string;
  count: number;
  reject_causes: string;
  rejected_plmns: string;
  a_location_country?: string;
  errorText?: string;
  a_RejectCauses?: string;
  a_RejectedPLMNs?: string;
}

interface FailureDetailsTableProps {
  country?: string;
  operator?: string;
  verdict?: string;
  show: boolean;
}

const FailureDetailsTable: React.FC<FailureDetailsTableProps> = ({ country, operator, verdict, show }) => {
  const [failureCauses, setFailureCauses] = useState<FailureCause[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFailureCauses = async () => {
      if (!show || verdict !== 'fail') return;
      
      setLoading(true);
      setError(null);
      
      try {
        const resp = await getFailureDetails({ verdict, country, operator });
        let rows: FailureCause[] = [];
        if (Array.isArray(resp)) {
          rows = resp;
        } else if (resp && Array.isArray(resp.data)) {
          rows = resp.data;
        }
        console.log('Réponse /failure_details:', resp);
        setFailureCauses(rows);
      } catch (err) {
        setError('Erreur lors du chargement des données d\'échec');
        console.error('Erreur lors de la récupération des détails d\'échec:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFailureCauses();
  }, [country, operator, verdict, show]);

  if (!show || verdict !== 'fail') {
    return null;
  }

  return (
    <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
      <h3 className="text-xl font-bold mb-4">Détails des échecs</h3>
      
      {loading ? (
        <div className="flex justify-center items-center h-24">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <p className="ml-4 text-gray-600">Chargement des données d'échec...</p>
        </div>
      ) : error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      ) : failureCauses.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          Aucune donnée d'échec disponible pour les filtres sélectionnés.
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pays
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description de l'erreur
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Causes de rejet
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  PLMNs rejetés
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Occurrences
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {failureCauses.map((cause, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cause.a_location_country || 'N/A'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {cause.cause || cause.errorText || 'N/A'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {cause.reject_causes || cause.a_RejectCauses || 'N/A'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {cause.rejected_plmns || cause.a_RejectedPLMNs || 'N/A'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {cause.count}
                  </td>
                </tr>
          ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default FailureDetailsTable; 