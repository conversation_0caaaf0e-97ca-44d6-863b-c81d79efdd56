import React, { useEffect, useRef, useState } from 'react';
import { FaCalendar, Fa<PERSON>ilter, FaGlobe, FaNetworkWired, FaSearch, FaTimes } from 'react-icons/fa';

interface FilterItem {
  id: string;
  name: string;
}

interface FilterProps {
  countries: FilterItem[];
  operators: FilterItem[];
  onFilterChange: (filters: any) => void;
  initialValues?: {
    startDate: string;
    endDate: string;
  };
}

const DynamicFilter: React.FC<FilterProps> = ({
  countries,
  operators,
  onFilterChange,
  initialValues
}) => {
  // États
  const [filters, setFilters] = useState({
    country: 'all',
    operator: 'all',
    verdict: 'all',
    dateRange: {
      startDate: initialValues?.startDate || '',
      endDate: initialValues?.endDate || ''
    },
    searchQuery: ''
  });
  
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [countrySearchTerm, setCountrySearchTerm] = useState('');
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [showOperatorDropdown, setShowOperatorDropdown] = useState(false);
  
  // Références
  const startDateInputRef = useRef<HTMLInputElement>(null);
  const endDateInputRef = useRef<HTMLInputElement>(null);
  const countryDropdownRef = useRef<HTMLDivElement>(null);
  const operatorDropdownRef = useRef<HTMLDivElement>(null);
  
  // Listes filtrées
  const countryList = countries || [];
  const operatorList = operators || [];
  
  const filteredCountries = countrySearchTerm
    ? countryList.filter(country => 
        country.name.toLowerCase().includes(countrySearchTerm.toLowerCase()))
    : countryList;
  
  // Effet pour gérer les clics en dehors des dropdowns
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (countryDropdownRef.current && !countryDropdownRef.current.contains(event.target as Node)) {
        setShowCountryDropdown(false);
      }
      if (operatorDropdownRef.current && !operatorDropdownRef.current.contains(event.target as Node)) {
        setShowOperatorDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Effet pour vérifier si des filtres sont actifs
  useEffect(() => {
    const hasActiveFilters = 
      filters.country !== 'all' ||
      filters.operator !== 'all' ||
      filters.verdict !== 'all' ||
      filters.searchQuery !== '' ||
      (filters.dateRange.startDate !== '' && filters.dateRange.startDate !== initialValues?.startDate) || 
      (filters.dateRange.endDate !== '' && filters.dateRange.endDate !== initialValues?.endDate);
    
    setIsFilterActive(hasActiveFilters);
  }, [filters, initialValues]);
  
  // Fonctions de gestion des changements
  const handleChange = (field: string, value: string) => {
    console.log(`Changement de filtre: ${field} = ${value}`);
    const newFilters = {
      ...filters,
      [field]: value
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };
  
  const handleDateChange = (field: 'startDate' | 'endDate', value: string) => {
    const newFilters = {
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: value
      }
    };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };
  
  const resetFilters = () => {
    const defaultFilters = {
      country: 'all',
      operator: 'all',
      verdict: 'all',
      dateRange: {
        startDate: initialValues?.startDate || '',
        endDate: initialValues?.endDate || ''
      },
      searchQuery: ''
    };
    setFilters(defaultFilters);
    onFilterChange(defaultFilters);
    setCountrySearchTerm('');
  };
  
  // Ouvrir le sélecteur de date natif
  const openDatePicker = (inputRef: React.RefObject<HTMLInputElement>) => {
    if (inputRef.current) {
      inputRef.current.showPicker();
    }
  };
  
  // Sélection de pays
  const handleCountrySelect = (country: string) => {
    handleChange('country', country);
    setShowCountryDropdown(false);
  };
  
  // Sélection d'opérateur
  const handleOperatorSelect = (operator: string) => {
    handleChange('operator', operator);
    setShowOperatorDropdown(false);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Filtre par pays */}
        <div className="relative" ref={countryDropdownRef}>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <FaGlobe className="inline-block mr-2" />
            Filtrer par pays:
          </label>
          
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowCountryDropdown(!showCountryDropdown)}
              className="block w-full text-left px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white"
            >
              {filters.country === 'all' 
                ? `Tous les pays (${countryList.length})` 
                : countryList.find(c => c.id === filters.country)?.name || filters.country}
              <span className="absolute inset-y-0 right-0 flex items-center pr-2">
                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </button>
            
            {showCountryDropdown && (
              <div className="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-60 overflow-auto">
                <div className="p-2 border-b">
                  <input
                    type="text"
                    placeholder="Rechercher..."
                    className="w-full px-2 py-1 text-sm border rounded"
                    value={countrySearchTerm}
                    onChange={e => setCountrySearchTerm(e.target.value)}
                  />
                </div>
                <ul className="py-1">
                  <li 
                    className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                      filters.country === 'all' ? 'bg-blue-100' : ''
                    }`}
                    onClick={() => handleCountrySelect('all')}
                  >
                    Tous les pays ({countryList.length})
                  </li>
                  {filteredCountries.map(country => (
                    <li
                      key={country.id}
                      className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                        filters.country === country.id ? 'bg-blue-100' : ''
                      }`}
                      onClick={() => handleCountrySelect(country.id)}
                    >
                      {country.name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
        
        {/* Filtre par opérateur */}
        <div className="relative" ref={operatorDropdownRef}>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <FaNetworkWired className="inline-block mr-2" />
            Opérateur
          </label>
          
          <div className="relative">
            <button
              type="button"
              onClick={() => setShowOperatorDropdown(!showOperatorDropdown)}
              className="block w-full text-left px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white"
            >
              {filters.operator === 'all' 
                ? 'Tous les opérateurs' 
                : operatorList.find(o => o.id === filters.operator)?.name || filters.operator}
              <span className="absolute inset-y-0 right-0 flex items-center pr-2">
                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </span>
            </button>
            
            {showOperatorDropdown && (
              <div className="absolute z-50 mt-1 w-full bg-white rounded-md shadow-lg max-h-60 overflow-auto">
                <ul className="py-1">
                  <li 
                    className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                      filters.operator === 'all' ? 'bg-blue-100' : ''
                    }`}
                    onClick={() => handleOperatorSelect('all')}
                  >
                    Tous les opérateurs
                  </li>
                  {operatorList.map(operator => (
                    <li
                      key={operator.id}
                      className={`px-4 py-2 hover:bg-gray-100 cursor-pointer ${
                        filters.operator === operator.id ? 'bg-blue-100' : ''
                      }`}
                      onClick={() => handleOperatorSelect(operator.id)}
                    >
                      {operator.name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
        
        {/* Filtre par verdict */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <FaFilter className="inline-block mr-2" />
            Verdict
          </label>
          <select
            value={filters.verdict}
            onChange={(e) => handleChange('verdict', e.target.value)}
            className={`block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md ${
              filters.verdict === 'fail' ? 'bg-red-50 border-red-300' : 
              filters.verdict === 'success' ? 'bg-green-50 border-green-300' : ''
            }`}
          >
            <option value="all">Tous les verdicts</option>
            <option value="success">Succès</option>
            <option value="fail">Échecs</option>
          </select>
        </div>
        
        {/* Filtre par période */}
        <div className="relative">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            <FaCalendar className="inline-block mr-2" />
            Période
          </label>
          <div className="grid grid-cols-2 gap-2">
            <div className="relative">
              <input
                ref={startDateInputRef}
                type="date"
                value={filters.dateRange.startDate}
                onChange={(e) => handleDateChange('startDate', e.target.value)}
                className="block w-full pl-3 pr-8 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              />
              <button 
                type="button"
                onClick={() => openDatePicker(startDateInputRef)}
                className="absolute inset-y-0 right-0 px-2 flex items-center text-gray-500 hover:text-gray-700"
                aria-label="Ouvrir le calendrier"
              >
                <FaCalendar size={14} />
              </button>
            </div>
            <div className="relative">
              <input
                ref={endDateInputRef}
                type="date"
                value={filters.dateRange.endDate}
                onChange={(e) => handleDateChange('endDate', e.target.value)}
                className="block w-full pl-3 pr-8 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
              />
              <button 
                type="button"
                onClick={() => openDatePicker(endDateInputRef)}
                className="absolute inset-y-0 right-0 px-2 flex items-center text-gray-500 hover:text-gray-700"
                aria-label="Ouvrir le calendrier"
              >
                <FaCalendar size={14} />
              </button>
            </div>
          </div>
          {(filters.dateRange.startDate || filters.dateRange.endDate) && (
            <button
              onClick={() => {
                handleDateChange('startDate', '');
                handleDateChange('endDate', '');
              }}
              className="absolute right-2 top-8 text-gray-400 hover:text-gray-600"
              title="Effacer les dates"
            >
              <FaTimes size={14} />
            </button>
          )}
        </div>
      </div>

      {/* Filtre de recherche */}
      <div className="relative">
        <label className="block text-sm font-medium text-gray-700 mb-1">
          <FaSearch className="inline-block mr-2" />
          Recherche
        </label>
        <input
          type="text"
          value={filters.searchQuery}
          onChange={(e) => handleChange('searchQuery', e.target.value)}
          placeholder="Rechercher..."
          className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
        />
        {filters.searchQuery && (
          <button
            onClick={() => handleChange('searchQuery', '')}
            className="absolute right-2 top-8 text-gray-400 hover:text-gray-600"
            title="Effacer la recherche"
          >
            <FaTimes size={14} />
          </button>
        )}
      </div>

      {/* Bouton de réinitialisation */}
      {isFilterActive && (
        <div className="flex justify-end mt-4">
          <button
            onClick={resetFilters}
            className="inline-flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm font-medium rounded-md transition-colors duration-150"
          >
            <FaTimes className="mr-2" />
            Réinitialiser les filtres
          </button>
        </div>
      )}
    </div>
  );
};

export default DynamicFilter; 