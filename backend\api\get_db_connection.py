"""
Version corrigée de la fonction get_db_connection pour Kpi_service.py
"""
import os
from sqlalchemy import create_engine, text
import pymysql

def get_db_connection():
    """Crée une connexion à la base de données MySQL"""
    try:
        # Configuration de connexion MySQL par défaut pour XAMPP
        db_user = os.getenv('DB_USER', 'root')
        db_password = os.getenv('DB_PASSWORD', '')  # Mot de passe vide par défaut pour XAMPP
        db_host = os.getenv('DB_HOST', 'localhost')
        db_name = os.getenv('DB_NAME', 'kpi')  # Nom de la base de données
        db_port = os.getenv('DB_PORT', '3306')  # Port MySQL par défaut
        
        db_url = f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
        
        print(f"Tentative de connexion à la base de données MySQL: {db_host}:{db_port}/{db_name}")
        print(f"URL de connexion: {db_url.replace(db_password, '******')}")
        engine = create_engine(db_url)
        
        # Tester la connexion
        with engine.connect() as conn:
            print("Connexion à la base de données MySQL réussie!")
            # Vérifier si la table steeringofroaming existe
            results = conn.execute(text("SHOW TABLES LIKE 'steeringofroaming'")).fetchall()
            if results:
                print("Table steeringofroaming trouvée!")
                # Vérifier le nombre d'enregistrements
                count = conn.execute(text("SELECT COUNT(*) FROM steeringofroaming")).fetchone()[0]
                print(f"Nombre d'enregistrements dans la table: {count}")
                # Vérifier si la colonne TCName existe
                results = conn.execute(
                    text("SHOW COLUMNS FROM steeringofroaming LIKE 'TCName'")).fetchall()
                if results:
                    print("Colonne TCName trouvée!")
                    # Vérifier le nombre d'enregistrements avec TCName=SteeringOfRoaming
                    count = conn.execute(
                        text("SELECT COUNT(*) FROM steeringofroaming WHERE TCName = 'SteeringOfRoaming'")).fetchone()[0]
                    print(f"Nombre d'enregistrements avec TCName=SteeringOfRoaming: {count}")
                else:
                    print("ATTENTION: Colonne TCName non trouvée dans la table steeringofroaming!")
            else:
                print("ERREUR: Table steeringofroaming non trouvée dans la base de données!")
        
        return engine
    except Exception as e:
        print(f"ERREUR de connexion à la base de données: {str(e)}")
        print("Détails de l'erreur:", str(e))
        import traceback
        traceback.print_exc()
        return None 