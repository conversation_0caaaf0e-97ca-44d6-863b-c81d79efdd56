#!/usr/bin/env python3
"""
Module de web scraping pour GlobalRoamer
Extrait automatiquement les fichiers CSV depuis https://globalroamer.com
"""

import os
import time
import pandas as pd
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict
import tempfile
import shutil

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GlobalRoamerScraper:
    """Classe pour scraper les données de GlobalRoamer"""
    
    def __init__(self, username: str, password: str, download_dir: Optional[str] = None):
        """
        Initialiser le scraper
        
        Args:
            username: Nom d'utilisateur GlobalRoamer
            password: Mot de passe GlobalRoamer
            download_dir: Répertoire de téléchargement (optionnel)
        """
        self.username = username
        self.password = password
        self.base_url = "https://globalroamer.com"
        self.login_url = "https://globalroamer.com/webui/faces/login.xhtml"
        
        # Configuration du répertoire de téléchargement
        if download_dir:
            self.download_dir = download_dir
        else:
            self.download_dir = os.path.join(os.getcwd(), "downloads", "globalroamer")
        
        # Créer le répertoire s'il n'existe pas
        os.makedirs(self.download_dir, exist_ok=True)
        
        self.driver = None
        
    def setup_driver(self) -> webdriver.Chrome:
        """Configurer le driver Chrome avec les bonnes options"""
        try:
            chrome_options = Options()
            
            # Options pour le téléchargement
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # Options pour la stabilité
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            
            # Mode headless optionnel (décommentez pour mode invisible)
            # chrome_options.add_argument("--headless")
            
            # Installer et configurer ChromeDriver automatiquement
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Timeout par défaut
            driver.implicitly_wait(10)
            
            logger.info("✅ Driver Chrome configuré avec succès")
            return driver
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la configuration du driver: {e}")
            raise
    
    def login(self) -> bool:
        """Se connecter à GlobalRoamer"""
        try:
            logger.info("🔐 Tentative de connexion à GlobalRoamer...")
            
            # Aller à la page de login
            self.driver.get(self.login_url)
            time.sleep(3)
            
            # Attendre que la page soit chargée
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.ID, "loginForm"))
            )
            
            # Trouver les champs de connexion
            username_field = self.driver.find_element(By.NAME, "loginForm:username")
            password_field = self.driver.find_element(By.NAME, "loginForm:password")
            login_button = self.driver.find_element(By.NAME, "loginForm:loginButton")
            
            # Saisir les identifiants
            username_field.clear()
            username_field.send_keys(self.username)
            
            password_field.clear()
            password_field.send_keys(self.password)
            
            # Cliquer sur le bouton de connexion
            login_button.click()
            
            # Attendre la redirection après connexion
            time.sleep(5)
            
            # Vérifier si la connexion a réussi
            if "dashboard" in self.driver.current_url.lower() or "main" in self.driver.current_url.lower():
                logger.info("✅ Connexion réussie à GlobalRoamer")
                return True
            else:
                logger.error("❌ Échec de la connexion - URL actuelle: " + self.driver.current_url)
                return False
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la connexion: {e}")
            return False
    
    def navigate_to_reports(self) -> bool:
        """Naviguer vers la section des rapports"""
        try:
            logger.info("📊 Navigation vers la section des rapports...")
            
            # Chercher le menu des rapports
            reports_menu = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Reports') or contains(text(), 'Rapports')]"))
            )
            reports_menu.click()
            time.sleep(3)
            
            # Chercher le sous-menu pour les données de roaming
            roaming_reports = WebDriverWait(self.driver, 15).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Roaming') or contains(text(), 'Steering')]"))
            )
            roaming_reports.click()
            time.sleep(3)
            
            logger.info("✅ Navigation vers les rapports réussie")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de la navigation: {e}")
            return False
    
    def download_csv_data(self, start_date: str, end_date: str) -> List[str]:
        """
        Télécharger les données CSV pour une période donnée
        
        Args:
            start_date: Date de début (format YYYY-MM-DD)
            end_date: Date de fin (format YYYY-MM-DD)
            
        Returns:
            Liste des fichiers téléchargés
        """
        try:
            logger.info(f"📥 Téléchargement des données du {start_date} au {end_date}")
            
            # Configurer les filtres de date
            start_date_field = self.driver.find_element(By.NAME, "reportForm:startDate")
            end_date_field = self.driver.find_element(By.NAME, "reportForm:endDate")
            
            start_date_field.clear()
            start_date_field.send_keys(start_date)
            
            end_date_field.clear()
            end_date_field.send_keys(end_date)
            
            # Sélectionner le type de rapport (SteeringOfRoaming)
            report_type_dropdown = self.driver.find_element(By.NAME, "reportForm:reportType")
            report_type_dropdown.click()
            
            steering_option = self.driver.find_element(By.XPATH, "//option[contains(text(), 'SteeringOfRoaming')]")
            steering_option.click()
            
            # Cliquer sur le bouton de génération de rapport
            generate_button = self.driver.find_element(By.NAME, "reportForm:generateButton")
            generate_button.click()
            
            # Attendre que le rapport soit généré
            WebDriverWait(self.driver, 60).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), 'Download') or contains(@href, '.csv')]"))
            )
            
            # Télécharger le fichier CSV
            download_link = self.driver.find_element(By.XPATH, "//a[contains(@href, '.csv')]")
            download_link.click()
            
            # Attendre que le téléchargement soit terminé
            time.sleep(10)
            
            # Lister les fichiers téléchargés
            downloaded_files = []
            for file in os.listdir(self.download_dir):
                if file.endswith('.csv') and start_date.replace('-', '') in file:
                    downloaded_files.append(os.path.join(self.download_dir, file))
            
            logger.info(f"✅ {len(downloaded_files)} fichier(s) téléchargé(s)")
            return downloaded_files
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du téléchargement: {e}")
            return []
    
    def scrape_data(self, days_back: int = 7) -> List[str]:
        """
        Scraper principal - télécharge les données des derniers jours
        
        Args:
            days_back: Nombre de jours à récupérer (défaut: 7)
            
        Returns:
            Liste des fichiers CSV téléchargés
        """
        downloaded_files = []
        
        try:
            # Configurer le driver
            self.driver = self.setup_driver()
            
            # Se connecter
            if not self.login():
                raise Exception("Échec de la connexion")
            
            # Naviguer vers les rapports
            if not self.navigate_to_reports():
                raise Exception("Échec de la navigation vers les rapports")
            
            # Calculer les dates
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # Télécharger les données
            downloaded_files = self.download_csv_data(start_date_str, end_date_str)
            
            logger.info(f"🎉 Scraping terminé avec succès - {len(downloaded_files)} fichier(s)")
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du scraping: {e}")
            
        finally:
            # Fermer le driver
            if self.driver:
                self.driver.quit()
                logger.info("🔒 Driver fermé")
        
        return downloaded_files
    
    def cleanup_old_files(self, days_to_keep: int = 30):
        """Nettoyer les anciens fichiers téléchargés"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            for file in os.listdir(self.download_dir):
                file_path = os.path.join(self.download_dir, file)
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        logger.info(f"🗑️ Fichier supprimé: {file}")
                        
        except Exception as e:
            logger.error(f"❌ Erreur lors du nettoyage: {e}")


# Fonction utilitaire pour tester le scraper
def test_scraper():
    """Fonction de test du scraper"""
    # ATTENTION: Remplacez par vos vrais identifiants
    username = "votre_username"
    password = "votre_password"
    
    scraper = GlobalRoamerScraper(username, password)
    files = scraper.scrape_data(days_back=1)  # Récupérer les données d'hier
    
    print(f"Fichiers téléchargés: {files}")
    return files

if __name__ == "__main__":
    test_scraper()
