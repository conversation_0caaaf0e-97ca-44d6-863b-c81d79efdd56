{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-04-22T08:11:13.415Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-04-29T10:21:55.395Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-04-29T10:24:33.732Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-05-09T08:29:07.841Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-05-14T14:28:36.179Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-05-15T13:33:54.086Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:01:08.472Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:01:08.493Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:17:31.459Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:17:31.513Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:44:53.252Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:44:53.263Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:47:14.350Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:51:58.120Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED ::1:8000","timestamp":"2025-05-15T15:52:19.917Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-16T10:26:50.697Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-16T10:26:50.750Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-16T10:41:03.814Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-16T10:41:03.906Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-16T13:06:03.247Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-16T13:06:04.385Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-16T13:54:14.684Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-16T13:54:15.572Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:10:26.550Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:10:28.059Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:10:40.584Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:10:42.656Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T13:20:20.175Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T13:20:20.211Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:30:19.964Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:30:20.542Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T13:40:04.284Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T13:40:04.434Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:51:32.310Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:51:33.417Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:52:28.949Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:52:29.466Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:52:37.516Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-18T13:52:38.413Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T14:47:21.130Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T14:47:21.240Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T15:35:53.167Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-18T15:35:53.217Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T15:37:45.450Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T15:37:46.143Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:07:37.628Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:07:37.727Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:08:56.903Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:08:56.933Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:09:46.163Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:09:46.321Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:16:01.160Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:16:01.359Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:16:12.141Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T16:16:12.171Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-18T16:24:33.645Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-18T17:09:51.659Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-18T17:09:51.724Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-18T17:09:55.061Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:28.579Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:28.646Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:37.133Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:37.160Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:39.335Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:23:39.380Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:45:03.294Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:45:03.613Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:45:22.372Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:45:23.065Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:48:11.854Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:48:11.877Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:48:40.319Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-18T17:48:40.340Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T12:08:25.003Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T12:08:25.123Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T12:08:25.195Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T12:45:04.272Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T12:45:04.327Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T14:20:05.854Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T14:20:28.078Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T14:28:47.803Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-19T14:49:37.612Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T15:10:02.154Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-19T15:12:11.387Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-19T15:25:17.063Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T15:46:15.334Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T15:55:36.735Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-19T16:03:42.695Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T08:20:31.100Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T08:26:09.283Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T08:33:18.046Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T08:34:19.783Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T08:34:21.013Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T08:45:16.452Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:04:30.380Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:07:52.257Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:21:47.104Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T09:22:32.700Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T09:22:33.243Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T09:22:37.096Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:27:56.238Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:34:10.405Z"}
{"level":"error","message":"Erreur lors du proxy KPI: read ECONNRESET","timestamp":"2025-05-20T09:34:19.220Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T09:37:15.309Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T09:38:57.276Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:41:58.293Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:43:47.394Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T09:45:14.714Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T10:06:20.366Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T10:10:20.341Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-20T10:17:21.545Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T13:57:23.347Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-20T13:58:20.109Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:05:16.395Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:06:15.601Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:06:18.114Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:06:49.639Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:06:50.328Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:06:52.408Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:08:52.713Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:08:54.873Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:09:57.870Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 404","timestamp":"2025-05-20T14:10:57.813Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T14:11:53.357Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T14:13:50.179Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T14:18:01.225Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-20T14:24:11.674Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-21T14:08:50.626Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-21T14:56:45.207Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-21T15:02:30.841Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-21T15:04:51.893Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-22T11:33:13.711Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-22T11:40:14.527Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 10000ms exceeded","timestamp":"2025-05-22T12:05:58.872Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T12:10:58.928Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T12:14:03.380Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T12:40:41.108Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T12:41:03.845Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T13:10:15.012Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T13:41:06.355Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T14:46:00.085Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-22T15:37:13.765Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-22T15:46:59.827Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-23T10:28:30.646Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T10:29:03.876Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T10:29:49.560Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T10:35:23.351Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:03:01.734Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:04:59.331Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:07:55.381Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:08:11.831Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:14:29.556Z"}
{"level":"error","message":"Erreur lors du proxy KPI: Request failed with status code 500","timestamp":"2025-05-23T11:14:38.479Z"}
{"level":"error","message":"Erreur lors du proxy KPI: timeout of 30000ms exceeded","timestamp":"2025-05-23T12:09:07.966Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-23T15:41:29.084Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-23T15:41:29.125Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-23T15:41:29.173Z"}
{"level":"error","message":"Erreur lors du proxy KPI: connect ECONNREFUSED 127.0.0.1:8000","timestamp":"2025-05-23T15:41:29.305Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-06T11:29:51.651Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-10T11:40:24.303Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-10T12:38:29.954Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-10T13:08:19.786Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-12T16:55:29.146Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-14T15:18:58.600Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-14T15:50:25.011Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-15T13:01:40.345Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-16T11:00:39.569Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-16T14:11:30.222Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-19T12:48:33.703Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-19T13:53:05.278Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-23T13:49:20.668Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-06-25T12:24:38.147Z"}
{"level":"error","message":"Erreur lors de l'initialisation de la base de données:","timestamp":"2025-07-01T15:33:22.219Z"}
