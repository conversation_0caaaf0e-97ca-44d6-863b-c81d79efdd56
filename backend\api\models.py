from pydantic import BaseModel, Field, EmailStr, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from enum import Enum
from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, Text, ForeignKey, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.dialects.mysql import LONGTEXT
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

# Configuration de base de données
DB_USER = os.getenv('DB_USER', 'root')
DB_PASSWORD = os.getenv('DB_PASSWORD', '')
DB_HOST = os.getenv('DB_HOST', 'localhost')
DB_NAME = os.getenv('DB_NAME', 'roaming_dashboard')
DB_PORT = os.getenv('DB_PORT', '3306')

# URL de connexion avec pooling
DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?charset=utf8mb4"

# Création du moteur SQLAlchemy avec pool_size et max_overflow pour le pooling
engine = create_engine(
    DATABASE_URL,
    pool_size=10,  # Nombre de connexions maintenues en permanence
    max_overflow=20,  # Nombre de connexions supplémentaires en cas de surcharge
    pool_pre_ping=True,  # Vérifie si la connexion est valide avant de l'utiliser
    pool_recycle=3600  # Recycle les connexions après 1 heure
)

# Base pour les modèles
Base = declarative_base()

# Création de la session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Fonction pour obtenir une session DB
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Modèle SQLAlchemy pour la table SteeringOfRoaming
class SteeringOfRoaming(Base):
    """Modèle pour les données de Steering of Roaming"""
    __tablename__ = "steeringofroaming"
    
    # Identifiants uniques
    id = Column(Integer, primary_key=True, autoincrement=True)
    TestcaseId = Column(String(255), index=True)
    OrderId = Column(String(255))
    TCName = Column(String(255), index=True)
    
    # Informations de verdict et de test
    Timestamp = Column(DateTime, index=True)
    Verdict = Column(String(50), index=True)
    TestDefinitionPath = Column(String(255))
    User = Column(String(100))
    UserGroup = Column(String(100))
    
    # Information d'erreur
    errorId = Column(Integer)
    errorText = Column(Text)
    TCTestDefinitionId = Column(String(100))
    errorSideCountry = Column(String(100))
    errorSideId = Column(String(100))
    errorSideLocation = Column(String(255))
    errorSideNumber = Column(String(100))
    errorSidePlmn = Column(String(100))
    errorSideProbe = Column(String(100))
    errorSideRxLevel = Column(String(50))
    errorSideUsedPLMNName = Column(String(100))
    errorState = Column(String(100))
    errorStateId = Column(String(100))
    
    # Statuts
    Completed = Column(Boolean)
    Failure = Column(Boolean)
    Incomplete = Column(Boolean)
    L3_Flag = Column(Boolean)
    ServiceType = Column(String(50))
    Success = Column(Boolean)
    
    # Données techniques A
    a_EcNO = Column(Float)
    a_HappyEyeBallSelectedIPVersion = Column(String(10))
    a_IP_Version = Column(String(10))
    a_IPv4_Used = Column(Boolean)
    a_IPv6_Used = Column(Boolean)
    a_InitialLupReqTime = Column(DateTime)
    a_LTE_RgAttachDuration = Column(String(50))
    a_LupAcceptDuration = Column(String(50))
    a_LupAcceptDuration_All_VPLMN = Column(String(50))
    a_LupDuration = Column(String(50))
    a_LupMode = Column(String(50))
    a_LupRejectDuration = Column(String(50))
    a_NrOfLupRejects_All_VPLMN = Column(Integer)
    a_NrOfLupRequests = Column(Integer)
    a_NrOfLupRequests_All_VPLMN = Column(Integer)
    a_NrOfPlmnsRejected = Column(Integer)
    a_OverallNrOfLupRequests = Column(Integer)
    a_RejectCauses = Column(String(255))
    a_RejectedPLMNs = Column(String(255))
    a_SimAuthenticationAfterAttach = Column(Integer)
    a_SimAuthenticationAfterLup = Column(Integer)
    a_TAC = Column(String(50))
    a_UsedPLMNNameShort = Column(String(100))
    a_VPLMN_registered = Column(String(100))
    
    # Données relatives au réseau et à la localisation
    a_NetworkType = Column(String(100), index=True)
    a_location = Column(String(255))
    a_location_country = Column(String(100), index=True)
    a_number = Column(String(100))
    a_imsi = Column(String(100))
    a_UsedPLMN = Column(String(100))
    a_UsedPLMNName = Column(String(100))
    a_TADIG = Column(String(100))
    
    # Données temporelles
    dayofweek = Column(Integer)
    weekofyear = Column(Integer, index=True)
    
    # Méta-informations
    InsertId = Column(String(100))
    RecordId = Column(String(100))
    
    def __repr__(self):
        return f"<SteeringOfRoaming(id={self.id}, Verdict={self.Verdict}, Country={self.a_location_country})>"


# Définition des schémas Pydantic pour l'API
class SteeringOfRoamingBase(BaseModel):
    """Schéma de base pour les données SteeringOfRoaming"""
    TCName: str
    Verdict: str
    a_location_country: Optional[str] = None
    weekofyear: Optional[int] = None
    a_UsedPLMNName: Optional[str] = None
    a_LupDuration: Optional[str] = None
    a_NrOfPlmnsRejected: Optional[int] = None
    a_NrOfLupRequests: Optional[int] = None
    a_NetworkType: Optional[str] = None


class SteeringOfRoamingCreate(SteeringOfRoamingBase):
    """Schéma pour la création d'entrées SteeringOfRoaming"""
    pass


class SteeringOfRoamingRead(SteeringOfRoamingBase):
    """Schéma pour la lecture d'entrées SteeringOfRoaming"""
    id: int
    Timestamp: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class SteeringOfRoamingUpdate(BaseModel):
    """Schéma pour la mise à jour d'entrées SteeringOfRoaming"""
    Verdict: Optional[str] = None
    a_location_country: Optional[str] = None
    a_UsedPLMNName: Optional[str] = None
    a_LupDuration: Optional[str] = None


class SuccessRateResponse(BaseModel):
    """Schéma pour la réponse du taux de succès par pays"""
    country: str
    success_rate: float
    total_connections: int


class FailCauseResponse(BaseModel):
    """Schéma pour la réponse des causes d'échec"""
    cause: str
    count: int
    percentage: float


class FailAnalysisResponse(BaseModel):
    """Schéma pour l'analyse complète des échecs"""
    causes: List[FailCauseResponse]
    rejected_plmns: Dict[str, int]
    details: List[Dict[str, Any]]

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"

class TokenData(BaseModel):
    username: Optional[str] = None
    scopes: List[str] = []

class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    is_active: bool = True
    firebase_uid: Optional[str] = None

class UserCreate(UserBase):
    password: str

class User(BaseModel):
    """Modèle pour les utilisateurs"""
    id: str
    username: Optional[str] = None  # Rendu optionnel
    email: Optional[EmailStr] = None  # Rendu optionnel
    full_name: Optional[str] = None
    is_active: bool = True
    role: Optional[str] = "user"
    firebase_uid: Optional[str] = None
    created_at: Optional[datetime] = None

class UserInDB(UserBase):
    """Modèle pour les utilisateurs en base de données"""
    hashed_password: str

class RoamingData(BaseModel):
    id: str
    timestamp: datetime
    country: str
    operator: str
    data_usage: float  # en Mo
    voice_minutes: float
    sms_count: int
    user_id: str

class FileUpload(BaseModel):
    id: str
    filename: str
    upload_date: datetime
    status: str
    user: str
    record_count: Optional[int] = None

class CountryUsage(BaseModel):
    name: str
    usage: float

class OperatorUsage(BaseModel):
    name: str
    usage: float

class RoamingAnalysis(BaseModel):
    id: str
    original_filename: str
    processed_date: datetime
    total_records: int
    countries: List[str]
    operators: List[str]
    total_data_usage: float  # en Go
    total_voice_minutes: float
    total_sms: int
    anomalies_detected: int
    top_countries: List[CountryUsage]
    top_operators: List[OperatorUsage]

class RoamingAgreement(BaseModel):
    id: str
    country: str
    region: str
    operator: str
    status: str
    agreement_type: str
    start_date: str
    end_date: str
    services: List[str]
    coverage_percentage: int

class RoamingStatistics(BaseModel):
    total_agreements: int
    active_agreements: int
    coverage_by_region: Dict[str, int]
    total_data_usage: float
    total_voice_minutes: int
    total_sms: int
    trend: Dict[str, List[Any]]

class ErrorResponse(BaseModel):
    detail: str

class FilterParams(BaseModel):
    """Paramètres de filtrage pour les analyses"""
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    countries: Optional[List[str]] = None
    operators: Optional[List[str]] = None
    services: Optional[List[str]] = None
    data_source: Optional[str] = None
    min_volume: Optional[float] = None
    max_volume: Optional[float] = None
    min_cost: Optional[float] = None
    max_cost: Optional[float] = None
    client_types: Optional[List[str]] = None
    
    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        if 'start_date' in values and values['start_date'] and v:
            if v < values['start_date']:
                raise ValueError('La date de fin doit être postérieure à la date de début')
        return v

class ReportType(str, Enum):
    """Types de rapports disponibles"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"

class FileUploadResponse(BaseModel):
    """Réponse après l'upload d'un fichier"""
    file_id: str
    filename: str
    rows_processed: int
    stats: Dict[str, Any]
    anomalies_detected: int

class KPI(BaseModel):
    """Indicateur de performance clé"""
    name: str
    value: Union[float, int, str]
    unit: Optional[str] = None
    change: Optional[float] = None  # Évolution en pourcentage par rapport à la période précédente
    status: Optional[str] = None  # "positive", "negative", "neutral"

class ChartData(BaseModel):
    """Données pour les graphiques"""
    labels: List[str]
    datasets: List[Dict[str, Any]]
    title: Optional[str] = None
    description: Optional[str] = None

class AnalysisResponse(BaseModel):
    """Réponse pour les analyses statistiques"""
    file_id: str
    filename: str
    stats: Dict[str, Any]
    charts: List[ChartData]
    kpis: List[KPI]

class ReportRequest(BaseModel):
    """Demande de génération de rapport"""
    report_type: ReportType
    filters: FilterParams
    include_charts: bool = True
    include_tables: bool = True
    include_anomalies: bool = True
    title: Optional[str] = None
    notes: Optional[str] = None

class ReportResponse(BaseModel):
    """Réponse après génération de rapport"""
    report_url: str
    report_type: ReportType
    date_generated: datetime = Field(default_factory=datetime.now)
    file_size: Optional[int] = None
    message: str = "Rapport généré avec succès"

class PredictionRequest(BaseModel):
    """Demande de prédiction"""
    target_field: str  # Champ à prédire (volume, coût, etc.)
    time_period: str  # "daily", "weekly", "monthly"
    forecast_periods: int = 5  # Nombre de périodes à prédire
    historical_periods: Optional[int] = None  # Périodes historiques à utiliser
    filters: Optional[FilterParams] = None
    advanced_options: Optional[Dict[str, Any]] = None

class PredictionPoint(BaseModel):
    """Point de prédiction avec intervalle de confiance"""
    date: Union[date, str]
    value: float
    lower_bound: Optional[float] = None
    upper_bound: Optional[float] = None

class PredictionResponse(BaseModel):
    """Réponse de prédiction"""
    target_field: str
    time_period: str
    predictions: List[PredictionPoint]
    model_accuracy: Optional[float] = None
    confidence_level: Optional[float] = None
    chart_data: ChartData
    message: str = "Prédiction générée avec succès"

class AnomalyType(str, Enum):
    """Types d'anomalies détectables"""
    COST = "cost"
    VOLUME = "volume"
    ACTIVITY = "activity"
    NEGATIVE = "negative_value"
    INCONSISTENCY = "inconsistency"

class Anomaly(BaseModel):
    """Modèle pour une anomalie détectée"""
    type: AnomalyType
    severity: str  # "low", "medium", "high"
    description: str
    value: float
    expected_range: Optional[List[float]] = None
    date: date
    country: Optional[str] = None
    operator: Optional[str] = None
    service: Optional[str] = None

class ProcessedFile(BaseModel):
    """Modèle pour les fichiers traités"""
    id: str
    original_filename: str
    upload_date: str
    uploader: str
    rows_count: int
    columns: List[str]
    stats: Dict[str, Any]
    path: str
    anomalies_count: int

class FirebaseToken(BaseModel):
    id_token: str

class FirebaseUser(BaseModel):
    uid: str
    email: Optional[str] = None
    display_name: Optional[str] = None
    photo_url: Optional[str] = None

# Modèles pour les fichiers
class FileInfo(BaseModel):
    id: str
    filename: str
    path: str
    size: int
    description: Optional[str] = None
    upload_date: datetime
    user_id: str
    
    class Config:
        from_attributes = True

# Modèles pour l'analyse
class AnalysisResult(BaseModel):
    file_id: str
    analysis_type: str
    timestamp: datetime
    results: Dict[str, Any]
    
class TrendResult(BaseModel):
    file_id: str
    timestamp: datetime
    trends: List[Dict[str, Any]]
    
class AnomalyResult(BaseModel):
    file_id: str
    timestamp: datetime
    anomalies: List[Dict[str, Any]]
    
class AnalysisSummary(BaseModel):
    file_id: str
    timestamp: datetime
    summary: Dict[str, Any]

# Modèles pour les rapports
class ReportInfo(BaseModel):
    id: str
    file_id: str
    report_type: str
    path: str
    created_at: datetime
    size: int
    user_id: str
    
    class Config:
        from_attributes = True

# Modèles pour le roaming
class RoamingFile(BaseModel):
    id: str
    filename: str
    path: str
    description: Optional[str] = None
    upload_date: datetime
    user_id: str
    size: int
    status: str
    
    class Config:
        from_attributes = True
        
class RoamingAgreement(BaseModel):
    id: str
    partner: str
    start_date: str
    end_date: str
    services: List[str]
    rates: Dict[str, float]
    status: str
    
    class Config:
        from_attributes = True
        
class RoamingAnalysisResult(BaseModel):
    file_id: str
    analysis_date: datetime
    stats: Dict[str, Any]
    trends: List[Dict[str, Any]]
    anomalies: List[Dict[str, Any]]
    
    class Config:
        from_attributes = True
        
class PartnerTraffic(BaseModel):
    partner: str
    traffic: Dict[str, int]
    revenue: float
    
class MonthlyTrend(BaseModel):
    month: str
    voice: int
    data: int
    sms: int
    
class RoamingStatistics(BaseModel):
    timestamp: datetime
    total_traffic: Dict[str, int]
    by_partner: List[PartnerTraffic]
    monthly_trend: List[MonthlyTrend]
    revenue: Dict[str, Any]
    
    class Config:
        from_attributes = True

# Modèles pour les KPI
class KpiWeek(BaseModel):
    id: str
    name: str
    start_date: str
    end_date: str
    
    class Config:
        from_attributes = True
        
class KpiData(BaseModel):
    id: str
    week_id: str
    timestamp: datetime
    kpis: Dict[str, Dict[str, Any]]
    top_partners: List[Dict[str, Any]]
    service_quality: Dict[str, float]
    
    class Config:
        from_attributes = True