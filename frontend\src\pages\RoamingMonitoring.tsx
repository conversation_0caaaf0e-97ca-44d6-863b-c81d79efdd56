import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSearch } from 'react-icons/fa';
import AlertsDisplay from '../components/AlertsDisplay';

const RoamingMonitoring = () => {
  const [activeTab, setActiveTab] = useState<string>('alerts');
  const [alertFilter, setAlertFilter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [costThresholdAlert, setCostThresholdAlert] = useState<boolean>(true);
  const [costPerCountryAlert, setCostPerCountryAlert] = useState<boolean>(true);
  const [usageSpikeAlert, setUsageSpikeAlert] = useState<boolean>(true);
  const [usageThresholdAlert, setUsageThreshold<PERSON><PERSON>t] = useState<boolean>(true);
  const [fraudDetectionAlert, setFraudDetectionAlert] = useState<boolean>(true);
  const [fraudSmsAlert, setFraudSmsAlert] = useState<boolean>(true);
  const [fraudVoiceAlert, setFraudVoiceAlert] = useState<boolean>(true);
  const [fraudMultiAlert, setFraudMultiAlert] = useState<boolean>(true);

  const handleSaveSettings = async () => {
    try {
      const settings = {
        costThresholdAlert,
        costPerCountryAlert,
        usageSpikeAlert,
        usageThresholdAlert,
        fraudDetectionAlert,
        fraudSmsAlert,
        fraudVoiceAlert,
        fraudMultiAlert,
        thresholds: {
          costGlobal: 5000,
          costPerCountry: 1000,
          usageSpike: 40,
          usageData: 500
        }
      };

      // En situation réelle, vous feriez un appel API ici
      // await axios.post('/api/monitoring/settings', settings);
      
      // Simuler un délai de sauvegarde
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Afficher une notification de succès
      alert('Paramètres enregistrés avec succès');
      
      // Retourner à l'onglet des alertes
      setActiveTab('alerts');
    } catch (error) {
      console.error('Erreur lors de la sauvegarde des paramètres:', error);
      alert('Erreur lors de la sauvegarde des paramètres');
    }
  };

  // Données simulées pour les alertes (utilisées uniquement pour l'onglet historique)
  const alerts = [
    {
      id: 1,
      type: 'cost',
      severity: 'high',
      title: 'Dépassement de coût en Italie',
      description: 'Le coût de roaming en Italie a dépassé le seuil de 5000€ pour le mois en cours.',
      date: '14/07/2023 15:32',
      status: 'pending'
    },
    {
      id: 2,
      type: 'usage',
      severity: 'medium',
      title: 'Pic d\'utilisation de données en Espagne',
      description: 'Augmentation soudaine de 45% de l\'utilisation des données en Espagne.',
      date: '13/07/2023 09:15',
      status: 'acknowledged'
    },
    {
      id: 3,
      type: 'fraud',
      severity: 'high',
      title: 'Activité suspecte détectée',
      description: 'Activité anormale détectée pour 3 utilisateurs en Royaume-Uni avec plus de 500 SMS envoyés en 1 heure.',
      date: '12/07/2023 22:47',
      status: 'resolved'
    },
    {
      id: 4,
      type: 'technical',
      severity: 'low',
      title: 'Problème technique avec l\'opérateur Orange France',
      description: 'Taux d\'échec de connexion élevé pour l\'opérateur Orange France.',
      date: '11/07/2023 11:23',
      status: 'resolved'
    },
    {
      id: 5,
      type: 'cost',
      severity: 'medium',
      title: 'Augmentation des tarifs en Allemagne',
      description: 'Deutsche Telekom a augmenté ses tarifs de roaming de 12% à partir du 10/07/2023.',
      date: '10/07/2023 08:05',
      status: 'pending'
    },
  ];

  // Données simulées pour l'historique
  const activityHistory = [
    {
      id: 1,
      type: 'alert',
      action: 'Alerte générée',
      details: 'Dépassement de coût en Italie',
      user: 'Système',
      date: '14/07/2023 15:32'
    },
    {
      id: 2,
      type: 'user',
      action: 'Alerte confirmée',
      details: 'Pic d\'utilisation de données en Espagne',
      user: 'Jean Dupont',
      date: '13/07/2023 10:22'
    },
    {
      id: 3,
      type: 'user',
      action: 'Alerte résolue',
      details: 'Activité suspecte détectée',
      user: 'Marie Martin',
      date: '13/07/2023 08:15'
    },
    {
      id: 4,
      type: 'system',
      action: 'Rapport généré',
      details: 'Rapport mensuel de coût de roaming',
      user: 'Système',
      date: '10/07/2023 00:05'
    },
    {
      id: 5,
      type: 'user',
      action: 'Paramètres modifiés',
      details: 'Modification des seuils d\'alerte',
      user: 'Admin Système',
      date: '09/07/2023 14:30'
    },
  ];

  // Filtrer les alertes
  const getFilteredAlerts = () => {
    let filtered = [...alerts];
    
    if (alertFilter !== 'all') {
      filtered = filtered.filter(alert => alert.status === alertFilter);
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(alert => 
        alert.title.toLowerCase().includes(query) || 
        alert.description.toLowerCase().includes(query)
      );
    }
    
    return filtered;
  };

  // Obtenir la classe de couleur en fonction de la sévérité
  const getSeverityClass = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Obtenir la classe de couleur en fonction du statut
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-red-100 text-red-800';
      case 'acknowledged':
        return 'bg-yellow-100 text-yellow-800';
      case 'resolved':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'alerts':
        return <AlertsDisplay refreshInterval={5 * 60 * 1000} />; // 5 minutes
      
      case 'history':
        return (
          <div>
            <div className="flex flex-wrap justify-between items-center mb-6">
              <div className="flex items-center space-x-4 mb-4 md:mb-0">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <FaSearch className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    placeholder="Rechercher dans l'historique"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex items-center">
                  <FaFilter className="text-gray-500 mr-2" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="form-select border-gray-300 rounded-md shadow-sm focus:border-primary-500 focus:ring focus:ring-primary-500 focus:ring-opacity-50"
                  >
                    <option value="all">Toutes les activités</option>
                    <option value="alert">Alertes</option>
                    <option value="user">Utilisateurs</option>
                    <option value="system">Système</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Détails</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Utilisateur</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {activityHistory
                      .filter(activity => statusFilter === 'all' || activity.type === statusFilter)
                      .filter(activity => !searchQuery || activity.details.toLowerCase().includes(searchQuery.toLowerCase()))
                      .map((activity) => (
                        <tr key={activity.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{activity.date}</td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${activity.type === 'alert' ? 'bg-red-100 text-red-800' : activity.type === 'user' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                              {activity.action}
                            </span>
                        </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{activity.details}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{activity.user}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        );
      
      case 'settings':
        return (
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-6">Configuration des alertes</h3>
            
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                <li>
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">Alertes de coût</h4>
                        <p className="mt-1 text-sm text-gray-500">Alertes liées aux dépassements de coûts de roaming</p>
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        <button
                          type="button"
                          onClick={() => setCostThresholdAlert(!costThresholdAlert)}
                          className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${costThresholdAlert ? 'bg-primary-600' : 'bg-gray-200'}`}
                          role="switch"
                          aria-checked={costThresholdAlert}
                        >
                          <span className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${costThresholdAlert ? 'translate-x-5' : 'translate-x-0'}`}></span>
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <div className="flex items-center mb-2">
                        <input
                          id="costThresholdAlert"
                          name="costThresholdAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={costThresholdAlert}
                          onChange={() => setCostThresholdAlert(!costThresholdAlert)}
                        />
                        <label htmlFor="costThresholdAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de seuil global de coût
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="costPerCountryAlert"
                          name="costPerCountryAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={costPerCountryAlert}
                          onChange={() => setCostPerCountryAlert(!costPerCountryAlert)}
                        />
                        <label htmlFor="costPerCountryAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de coût par pays
                        </label>
                      </div>
                    </div>
                  </div>
                </li>
                
                <li>
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900">Alertes d'utilisation</h4>
                        <p className="mt-1 text-sm text-gray-500">Alertes liées à l'utilisation des données en roaming</p>
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        <button
                          type="button"
                          onClick={() => setUsageSpikeAlert(!usageSpikeAlert)}
                          className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${usageSpikeAlert ? 'bg-primary-600' : 'bg-gray-200'}`}
                          role="switch"
                          aria-checked={usageSpikeAlert}
                        >
                          <span className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${usageSpikeAlert ? 'translate-x-5' : 'translate-x-0'}`}></span>
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <div className="flex items-center mb-2">
                        <input
                          id="usageSpikeAlert"
                          name="usageSpikeAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={usageSpikeAlert}
                          onChange={() => setUsageSpikeAlert(!usageSpikeAlert)}
                        />
                        <label htmlFor="usageSpikeAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de pic d'utilisation
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="usageThresholdAlert"
                          name="usageThresholdAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={usageThresholdAlert}
                          onChange={() => setUsageThresholdAlert(!usageThresholdAlert)}
                        />
                        <label htmlFor="usageThresholdAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de seuil d'utilisation
                        </label>
                      </div>
                    </div>
                  </div>
                </li>
                
                <li>
                  <div className="px-4 py-5 sm:p-6">
                    <div className="flex items-center justify-between">
                <div>
                        <h4 className="text-lg font-medium text-gray-900">Alertes de fraude</h4>
                        <p className="mt-1 text-sm text-gray-500">Alertes liées à la détection de fraude en roaming</p>
                      </div>
                      <div className="ml-4 flex-shrink-0">
                        <button
                          type="button"
                          onClick={() => setFraudDetectionAlert(!fraudDetectionAlert)}
                          className={`relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${fraudDetectionAlert ? 'bg-primary-600' : 'bg-gray-200'}`}
                          role="switch"
                          aria-checked={fraudDetectionAlert}
                        >
                          <span className={`pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200 ${fraudDetectionAlert ? 'translate-x-5' : 'translate-x-0'}`}></span>
                        </button>
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <div className="flex items-center mb-2">
                        <input
                          id="fraudSmsAlert"
                          name="fraudSmsAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={fraudSmsAlert}
                          onChange={() => setFraudSmsAlert(!fraudSmsAlert)}
                        />
                        <label htmlFor="fraudSmsAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de fraude SMS
                        </label>
                      </div>
                      <div className="flex items-center mb-2">
                        <input
                          id="fraudVoiceAlert"
                          name="fraudVoiceAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={fraudVoiceAlert}
                          onChange={() => setFraudVoiceAlert(!fraudVoiceAlert)}
                        />
                        <label htmlFor="fraudVoiceAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de fraude voix
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="fraudMultiAlert"
                          name="fraudMultiAlert"
                          type="checkbox"
                          className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          checked={fraudMultiAlert}
                          onChange={() => setFraudMultiAlert(!fraudMultiAlert)}
                        />
                        <label htmlFor="fraudMultiAlert" className="ml-2 block text-sm text-gray-900">
                          Alerte de fraude multi-services
                        </label>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
              </div>
              
              <div className="mt-6 flex justify-end">
                <button 
                type="button"
                className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                onClick={() => setActiveTab('alerts')}
              >
                Annuler
              </button>
              <button
                type="button"
                className="ml-3 inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  onClick={handleSaveSettings}
                >
                Enregistrer
                </button>
            </div>
          </div>
        );
      
      default:
        return <div>Contenu non disponible</div>;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Monitoring du Roaming</h1>
        <p className="mt-2 text-sm text-gray-600">
          Surveillance en temps réel des activités de roaming et alertes
        </p>
      </div>
      
      <div className="mb-6 border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
        <button
            onClick={() => setActiveTab('alerts')}
            className={`${
            activeTab === 'alerts'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            <div className="flex items-center">
              <FaBell className="mr-2" />
          Alertes
            </div>
        </button>
        <button
            onClick={() => setActiveTab('history')}
            className={`${
            activeTab === 'history'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            <div className="flex items-center">
              <FaEye className="mr-2" />
          Historique
            </div>
        </button>
        <button
            onClick={() => setActiveTab('settings')}
            className={`${
            activeTab === 'settings'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
          >
            <div className="flex items-center">
              <FaCog className="mr-2" />
              Paramètres
            </div>
        </button>
        </nav>
      </div>
      
      {renderContent()}
    </div>
  );
};

export default RoamingMonitoring;

