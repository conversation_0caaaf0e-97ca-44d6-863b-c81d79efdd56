{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@tremor/react": "^3.12.1", "@types/chart.js": "^2.9.41", "autoprefixer": "^10.4.17", "axios": "^1.6.7", "chart.js": "^4.4.1", "clsx": "^2.1.1", "firebase": "^10.8.0", "path": "^0.12.7", "postcss": "^8.4.35", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-router-dom": "^6.22.1", "recharts": "^2.12.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.23.4", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "tailwindcss": "^3.4.1", "typescript": "^5.8.3", "vite": "^5.1.3"}}