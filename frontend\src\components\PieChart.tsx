import Chart from 'chart.js/auto';
import React, { useEffect, useRef } from 'react';

interface PieChartProps {
  data: {
    labels: string[];
    values: number[];
    colors?: string[];
  };
  width: number;
  height: number;
  donut?: boolean;
  className?: string;
  onSliceClick?: (label: string) => void;
}

const PieChart: React.FC<PieChartProps> = ({ 
  data, 
  width, 
  height, 
  donut = false,
  className,
  onSliceClick
}) => {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);

  const defaultColors = [
    'rgba(59, 130, 246, 0.8)',  // Bleu
    'rgba(16, 185, 129, 0.8)',  // Vert
    'rgba(245, 158, 11, 0.8)',  // Jaune
    'rgba(239, 68, 68, 0.8)',   // Rouge
    'rgba(139, 92, 246, 0.8)',  // Violet
    'rgba(236, 72, 153, 0.8)'   // Rose
  ];

  useEffect(() => {
    if (chartRef.current) {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        chartInstance.current = new Chart(ctx, {
          type: donut ? 'doughnut' : 'pie',
          data: {
            labels: data.labels,
            datasets: [{
              data: data.values,
              backgroundColor: data.colors || defaultColors,
              borderColor: 'white',
              borderWidth: 2,
              hoverBackgroundColor: (data.colors || defaultColors).map(color => 
                color.replace('0.8', '1')
              ),
              hoverBorderColor: 'white',
              hoverBorderWidth: 3,
              spacing: 2
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            onClick: (e, elements) => {
              if (elements.length > 0 && onSliceClick) {
                const index = elements[0].index;
                const labelClicked = data.labels[index];
                onSliceClick(labelClicked);
              }
            },
            plugins: {
              legend: {
                position: 'right',
                labels: {
                  padding: 20,
                  usePointStyle: true,
                  font: {
                    size: 12,
                    weight: 500
                  },
                  color: '#4B5563'
                }
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                titleColor: '#1F2937',
                bodyColor: '#4B5563',
                borderColor: '#E5E7EB',
                borderWidth: 1,
                padding: 12,
                bodyFont: {
                  size: 12
                },
                titleFont: {
                  size: 14,
                  weight: 'bold'
                },
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.raw as number;
                    const percentage = ((value / data.values.reduce((a, b) => a + b, 0)) * 100).toFixed(1);
                    return `${label}: ${percentage}%`;
                  }
                }
              }
            },
            cutout: donut ? '60%' : 0,
            animation: {
              animateRotate: true,
              animateScale: true,
              duration: 750,
              easing: 'easeInOutQuart'
            }
          }
        });
      }
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [data, donut, onSliceClick]);

  return (
    <canvas
      ref={chartRef}
      width={width}
      height={height}
      className={className}
    />
  );
};

export default PieChart;

