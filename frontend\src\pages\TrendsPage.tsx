import React, { useEffect, useMemo, useState } from 'react';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaGlobe } from 'react-icons/fa';
import BarChart from '../components/BarChart';
import DynamicFilter from '../components/DynamicFilter';
import Pie<PERSON>hart from '../components/PieChart';
import { getTopCountriesConsumption } from '../services/kpiService';
import { countryToContinent } from '../utils/countryToContinent';

// Types
interface TrendItem {
  name: string;
  current: number;
  previous: number;
  change: number;
}

interface TrendData {
  dataUsage: TrendItem[];
  voice: TrendItem[];
  sms: TrendItem[];
}

export interface LocalCountryData {
  country: string;
  total_tests: number;
  total_data_gb: number;
  success_rate: number;
  avg_duration: number;
  unique_operators: number;
  continent?: string;
}

interface RegionData {
  labels: string[];
  values: number[];
  colors: string[];
}

interface CountriesData {
  [key: string]: {
  labels: string[];
  values: number[];
  };
}

interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor: string | string[];
  borderColor?: string;
  borderWidth?: number;
  type?: string;
}

interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

interface OperatorData {
  weekly: ChartData;
  monthly: ChartData;
  quarterly: ChartData;
  yearly: ChartData;
}

interface OperatorPeriodicData {
  [key: string]: OperatorData;
}

// Types pour les périodes
type PeriodType = 'weekly' | 'monthly' | 'quarterly' | 'yearly';

interface PeriodConfig {
  labels: string[];
  dateFormat: string;
  dataMultiplier: number;
}

const periodConfigs: Record<PeriodType, PeriodConfig> = {
  weekly: {
    labels: ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'],
    dateFormat: 'dd/MM',
    dataMultiplier: 1
  },
  monthly: {
    labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
    dateFormat: 'MM/yyyy',
    dataMultiplier: 4
  },
  quarterly: {
    labels: ['T1', 'T2', 'T3', 'T4'],
    dateFormat: 'QQ yyyy',
    dataMultiplier: 12
  },
  yearly: {
    labels: ['2021', '2022', '2023', '2024', '2025'],
    dateFormat: 'yyyy',
    dataMultiplier: 48
  }
};

// Données initiales
const monthlyTrendData: ChartData = {
  labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
  datasets: [
    {
      label: 'Données (TB)',
      data: [245, 278, 302, 298, 332, 345, 376, 410, 388, 425, 445, 470],
      backgroundColor: ['#3b82f6']
    },
    {
      label: 'Voix (K minutes)',
      data: [120, 125, 118, 130, 142, 152, 138, 145, 160, 155, 170, 175],
      backgroundColor: ['#10b981']
    },
    {
      label: 'SMS (K)',
      data: [30, 28, 25, 22, 20, 18, 22, 24, 22, 20, 18, 15],
      backgroundColor: ['#f59e0b']
    }
  ]
};

const regionData: RegionData = {
  labels: ['Europe', 'Amérique', 'Asie', 'Afrique', 'Océanie'],
  values: [45, 30, 15, 5, 5],
  colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6']
};

const countries = [
  { id: 'fr', name: 'France' },
  { id: 'es', name: 'Espagne' },
  { id: 'it', name: 'Italie' },
  { id: 'de', name: 'Allemagne' },
  { id: 'uk', name: 'Royaume-Uni' }
];

const operators = [
  { id: 'orange', name: 'Orange' },
  { id: 'vodafone', name: 'Vodafone' },
  { id: 'telefonica', name: 'Telefonica' },
  { id: 'tim', name: 'TIM' },
  { id: 'deutsche_telekom', name: 'Deutsche Telekom' }
];

// Nouveau type pour les données de consommation par pays
interface CountryDataConsumption {
  country: string;
  dataUsage: number; // En TB ou GB
  connections: number;
  percentChange: number; // Changement en pourcentage par rapport à la période précédente
}

const TrendsPage: React.FC = () => {
  const [timeRange, setTimeRange] = useState<PeriodType>('monthly');
  const [barChartData, setBarChartData] = useState<ChartData>(monthlyTrendData);
  const [pieChartData, setPieChartData] = useState<RegionData>(regionData);
  const [trendData, setTrendData] = useState<TrendData>({
    dataUsage: [
      {
        name: 'Croissance de l\'utilisation des données',
        current: 345.78,
        previous: 298.45,
        change: 15.8
      },
      {
        name: 'Coût moyen par GB',
        current: 4.2,
        previous: 5.1,
        change: -17.6
      }
    ],
    
    voice: [
      {
        name: 'Minutes d\'appel',
        current: 154.32,
        previous: 142.87,
        change: 8.0
      },
      {
        name: 'Coût moyen par minute',
        current: 0.08,
        previous: 0.11,
        change: -27.3
      }
    ],
    sms: [
      {
        name: 'Nombre de SMS',
        current: 25431,
        previous: 28745,
        change: -11.5
      }
    ]

  });

  const countriesData: CountriesData = {
    fr: {
      labels: ['Orange', 'SFR', 'Bouygues', 'Free'],
      values: [40, 25, 20, 15],
    },
    es: {
      labels: ['Telefonica', 'Vodafone', 'Orange'],
      values: [45, 35, 20],
    },
    it: {
      labels: ['TIM', 'Vodafone', 'Wind Tre'],
      values: [40, 35, 25],
    },
    de: {
      labels: ['Deutsche Telekom', 'Vodafone', 'O2'],
      values: [45, 30, 25],
    },
    uk: {
      labels: ['EE', 'Vodafone', 'O2', 'Three'],
      values: [35, 25, 25, 15],
    },
  };

  const operatorPeriodicData: OperatorPeriodicData = {
    orange: {
      weekly: generateWeeklyData(0.8, 1.2),
      monthly: generateMonthlyData(0.7, 1.3),
      quarterly: generateQuarterlyData(0.9, 1.1),
      yearly: generateYearlyData(0.85, 1.15)
    },
    vodafone: {
      weekly: generateWeeklyData(0.75, 1.25),
      monthly: generateMonthlyData(0.8, 1.2),
      quarterly: generateQuarterlyData(0.85, 1.15),
      yearly: generateYearlyData(0.9, 1.1)
    },
    telefonica: {
      weekly: generateWeeklyData(0.7, 1.3),
      monthly: generateMonthlyData(0.75, 1.25),
      quarterly: generateQuarterlyData(0.8, 1.2),
      yearly: generateYearlyData(0.85, 1.15)
    },
    tim: {
      weekly: generateWeeklyData(0.65, 1.35),
      monthly: generateMonthlyData(0.7, 1.3),
      quarterly: generateQuarterlyData(0.75, 1.25),
      yearly: generateYearlyData(0.8, 1.2)
    },
    deutsche_telekom: {
      weekly: generateWeeklyData(0.85, 1.15),
      monthly: generateMonthlyData(0.9, 1.1),
      quarterly: generateQuarterlyData(0.95, 1.05),
      yearly: generateYearlyData(0.9, 1.1)
    }
  };

  // État pour les données des pays
  const [topCountriesData, setTopCountriesData] = useState<LocalCountryData[]>([]);
  const [loadingTopCountries, setLoadingTopCountries] = useState<boolean>(false);
  // État pour le graphique de consommation des pays
  const [countryConsumptionChartData, setCountryConsumptionChartData] = useState<ChartData>({ labels: [], datasets: [] });

  // ADD after existing state declarations
  const [selectedContinent, setSelectedContinent] = useState<string>('all');

  // add state for donut
  const [donutData,setDonutData]=useState<RegionData>({labels:[],values:[],colors:[]});

  // Fonction utilitaire pour fabriquer les données du bar-chart à partir d'une liste de pays
  const createChartData = (countries: LocalCountryData[]): ChartData => {
    const total = countries.reduce((s, c) => s + c.total_tests, 0) || 1;
    return {
      labels: countries.map(c => c.country),
      datasets: [
        {
          label: 'Pourcentage de consommation (%)',
          data: countries.map(c => parseFloat(((c.total_tests / total) * 100).toFixed(2))),
          backgroundColor: countries.map((_, idx) => `rgba(${59 + idx * 2}, ${130 - idx * 1.5}, ${246 - idx * 2.5}, 0.7)`),
          borderWidth: 1
        }
      ]
    };
  };

  // Met à jour le bar-chart lorsqu'un continent est sélectionné ou quand les données arrivent
  const normalize = (s:string)=>s.toLowerCase();
  const filteredCountryChart = useMemo(()=>{
    if(selectedContinent==='all'){
      return countryConsumptionChartData;
    }
    const filtered = topCountriesData.filter(c=>c.continent===selectedContinent);
    if(filtered.length===0) return countryConsumptionChartData;
    const total=filtered.reduce((s,c)=>s+c.total_tests,0)||1;
    return {
      labels: filtered.map(f=>f.country),
      datasets:[{
        label:'Pourcentage de consommation (%)',
        data: filtered.map(f=>parseFloat(((f.total_tests/total)*100).toFixed(2))),
        backgroundColor: filtered.map((_,idx)=>`rgba(${59+idx*3},${130-idx*2},${246-idx*4},0.7)`),
        borderWidth:1
      }]
    };
  },[selectedContinent,topCountriesData,countryConsumptionChartData]);

  useEffect(()=>{ setCountryConsumptionChartData(filteredCountryChart);},[filteredCountryChart]);

  // Handler pour le clic sur un continent
  const handleContinentClick=(label:string)=>{ setSelectedContinent(prev=>prev===label?'all':label);};

  useEffect(() => {
    const fetchTopCountriesData = async () => {
      setLoadingTopCountries(true);
      try {
        const raw: LocalCountryData[] = await getTopCountriesConsumption();
        const data = raw.map(item=>({
          ...item,
          continent: countryToContinent[item.country] || 'Autres'
        }));
        setTopCountriesData(data);
        
        if (data && data.length > 0) {
          const totalTests = data.reduce((sum, country) => sum + country.total_tests, 0);
          
          const chartData: ChartData = {
            labels: data.map(country => country.country),
            datasets: [
              {
                label: 'Pourcentage de consommation (%)',
                data: data.map(country => parseFloat(((country.total_tests / totalTests) * 100).toFixed(2))),
                backgroundColor: data.map((_, index) => 
                  `rgba(${59 + index * 2}, ${130 - index * 1.5}, ${246 - index * 2.5}, 0.7)`
                ),
                borderWidth: 1
              }
            ]
          };
          setCountryConsumptionChartData(chartData);
          setBarChartData(chartData);
        }

        // build donut aggregated by continent (using total_tests)
        const agg: Record<string,number> = {};
        data.forEach(c=>{
          agg[c.continent] = (agg[c.continent]||0)+c.total_tests;
        });
        const totalAgg = Object.values(agg).reduce((s,v)=>s+v,0)||1;
        const donutLabels = Object.keys(agg);
        const donutValues = donutLabels.map(l=>parseFloat(((agg[l]/totalAgg)*100).toFixed(2)));
        const defaultColors = [
          '#3b82f6','#10b981','#f59e0b','#ef4444','#8b5cf6','#ec4899','#14b8a6','#f97316'
        ];
        setDonutData({labels:donutLabels,values:donutValues,colors:donutLabels.map((_,i)=>defaultColors[i%defaultColors.length])});
      } catch (error) {
        console.error('Erreur lors du chargement des données de consommation par pays:', error);
      } finally {
        setLoadingTopCountries(false);
      }
    };
    
    fetchTopCountriesData();
  }, []); // S'exécute seulement au montage du composant

  // Fonctions utilitaires pour générer des données
  function generateWeeklyData(min: number, max: number): ChartData {
    return {
      labels: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
      datasets: [
        {
          label: 'Données (TB)',
          data: Array(7).fill(0).map(() => Math.random() * (max - min) + min * 100),
          backgroundColor: ['#3b82f6']
        },
        {
          label: 'Voix (K minutes)',
          data: Array(7).fill(0).map(() => Math.random() * (max - min) + min * 50),
          backgroundColor: ['#10b981']
        },
        {
          label: 'SMS (K)',
          data: Array(7).fill(0).map(() => Math.random() * (max - min) + min * 25),
          backgroundColor: ['#f59e0b']
        }
      ]
    };
  }

  function generateMonthlyData(min: number, max: number): ChartData {
    return {
      labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'],
      datasets: [
        {
          label: 'Données (TB)',
          data: Array(12).fill(0).map(() => Math.random() * (max - min) + min * 300),
          backgroundColor: ['#3b82f6']
        },
        {
          label: 'Voix (K minutes)',
          data: Array(12).fill(0).map(() => Math.random() * (max - min) + min * 150),
          backgroundColor: ['#10b981']
        },
        {
          label: 'SMS (K)',
          data: Array(12).fill(0).map(() => Math.random() * (max - min) + min * 75),
          backgroundColor: ['#f59e0b']
        }
      ]
    };
  }

  function generateQuarterlyData(min: number, max: number): ChartData {
    return {
      labels: ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024'],
      datasets: [
        {
          label: 'Données (TB)',
          data: Array(4).fill(0).map(() => Math.random() * (max - min) + min * 900),
          backgroundColor: ['#3b82f6']
        },
        {
          label: 'Voix (K minutes)',
          data: Array(4).fill(0).map(() => Math.random() * (max - min) + min * 450),
          backgroundColor: ['#10b981']
        },
        {
          label: 'SMS (K)',
          data: Array(4).fill(0).map(() => Math.random() * (max - min) + min * 225),
          backgroundColor: ['#f59e0b']
        }
      ]
    };
  }

  function generateYearlyData(min: number, max: number): ChartData {
    return {
      labels: ['2021', '2022', '2023', '2024', '2025'],
      datasets: [
        {
          label: 'Données (TB)',
          data: Array(5).fill(0).map(() => Math.random() * (max - min) + min * 3600),
          backgroundColor: ['#3b82f6']
        },
        {
          label: 'Voix (K minutes)',
          data: Array(5).fill(0).map(() => Math.random() * (max - min) + min * 1800),
          backgroundColor: ['#10b981']
        },
        {
          label: 'SMS (K)',
          data: Array(5).fill(0).map(() => Math.random() * (max - min) + min * 900),
          backgroundColor: ['#f59e0b']
        }
      ]
    };
  }

  // Fonction pour obtenir les données spécifiques à la période
  const getPeriodData = (period: PeriodType, baseData: ChartData): ChartData => {
    // Si nous avons des données de consommation par pays, les utiliser directement
    if (countryConsumptionChartData.labels.length > 0 && period === 'monthly') {
      return countryConsumptionChartData;
    }
    
    const config = periodConfigs[period];
    
    // Calculer la plage de dates pour la période actuelle
    const now = new Date();
    let startDate: Date;
    let dataPoints: number;
    
    switch (period) {
      case 'weekly':
        startDate = new Date(now.setDate(now.getDate() - now.getDay()));
        dataPoints = 7;
        break;
      case 'monthly':
        startDate = new Date(now.getFullYear(), 0, 1);
        dataPoints = 12;
        break;
      case 'quarterly':
        startDate = new Date(now.getFullYear(), 0, 1);
        dataPoints = 4;
        break;
      case 'yearly':
        startDate = new Date(now.getFullYear() - 4, 0, 1);
        dataPoints = 5;
        break;
    }

    return {
      labels: config.labels,
      datasets: baseData.datasets.map(dataset => ({
        ...dataset,
        data: Array(dataPoints).fill(0).map((_, index) => {
          const baseValue = dataset.data[index % dataset.data.length];
          return baseValue * config.dataMultiplier * (0.8 + Math.random() * 0.4);
        }),
        backgroundColor: dataset.backgroundColor
      }))
    };
  };

  const handleFilterChange = (filters: {
    country: string;
    operator: string;
    dateRange: { startDate: string; endDate: string };
    searchQuery: string;
  }) => {
    // Appliquer les filtres
    let filteredOperatorData = monthlyTrendData;
    let filteredRegionData = regionData;

    // Filtrer par période
    const operatorData = filters.operator !== 'all' ? operatorPeriodicData[filters.operator] : null;
    if (operatorData) {
      switch (timeRange) {
        case 'weekly':
          filteredOperatorData = operatorData.weekly;
          break;
        case 'monthly':
          filteredOperatorData = operatorData.monthly;
          break;
        case 'quarterly':
          filteredOperatorData = operatorData.quarterly;
          break;
        case 'yearly':
          filteredOperatorData = operatorData.yearly;
          break;
      }
    }

    // Filtrer par pays
    if (filters.country !== 'all' && filters.country in countriesData) {
      const countryData = countriesData[filters.country];
      filteredRegionData = {
        labels: countryData.labels,
        values: countryData.values,
        colors: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444'].slice(0, countryData.labels.length)
      };
    }

    // Appliquer le filtre de date si nécessaire
    if (filters.dateRange.startDate && filters.dateRange.endDate) {
      const start = new Date(filters.dateRange.startDate);
      const end = new Date(filters.dateRange.endDate);
      
      // Ajuster les données en fonction de la période sélectionnée
      filteredOperatorData = {
        ...filteredOperatorData,
        datasets: filteredOperatorData.datasets.map(dataset => ({
          ...dataset,
          data: dataset.data.map(val => adjustValueForDateRange(val, start, end))
        }))
      };
    }

    // Mettre à jour les données des graphiques
    setBarChartData(filteredOperatorData);
    setPieChartData(filteredRegionData);

    // Mettre à jour les tendances
    updateTrendData(filters);
  };

  function adjustValueForDateRange(value: number, start: Date, end: Date): number {
    // Calculer le nombre de jours dans la plage
    const days = (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24);
    // Ajuster la valeur en fonction de la durée
    return value * (days / 30); // Base mensuelle
  }

  const updateTrendData = (filters: any) => {
    // Simuler la mise à jour des données de tendance en fonction des filtres
    const newTrendData: TrendData = {
      dataUsage: trendData.dataUsage.map(item => ({
        ...item,
        current: item.current * (Math.random() * 0.4 + 0.8),
        previous: item.previous * (Math.random() * 0.4 + 0.8),
        change: (Math.random() * 30 - 15)
      })),
      voice: trendData.voice.map(item => ({
        ...item,
        current: item.current * (Math.random() * 0.4 + 0.8),
        previous: item.previous * (Math.random() * 0.4 + 0.8),
        change: (Math.random() * 20 - 10)
      })),
      sms: trendData.sms.map(item => ({
        ...item,
        current: item.current * (Math.random() * 0.4 + 0.8),
        previous: item.previous * (Math.random() * 0.4 + 0.8),
        change: (Math.random() * 15 - 7.5)
      }))
    };

    // Mettre à jour les données
    setTrendData(newTrendData);
  };

  // Fonction pour gérer le changement de période
  const handlePeriodChange = (newPeriod: PeriodType) => {
    setTimeRange(newPeriod);
    
    // Mettre à jour les données du graphique en fonction de la période
    const newData = getPeriodData(newPeriod, monthlyTrendData);
    
    // Utiliser une conversion explicite pour s'assurer que le type est correct
    const typedChartData: {
      labels: string[];
      datasets: {
        label: string;
        data: number[];
        backgroundColor: string | string[];
        borderColor?: string;
        borderWidth?: number;
        type?: string;
      }[];
    } = {
      labels: newData.labels,
      datasets: newData.datasets.map(dataset => ({
        ...dataset,
        borderColor: typeof dataset.borderColor === 'string' ? dataset.borderColor : undefined
      }))
    };
    
    setBarChartData(typedChartData);
    
    // Mettre à jour les données de tendance
    updateTrendData({});
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* En-tête */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Tendances Roaming</h1>
          <p className="mt-2 text-sm text-gray-600">
            Analyse des tendances et évolution du roaming
          </p>
        </div>

        {/* Section Filtres */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-medium text-gray-900 mb-4">Filtres et Contrôles</h2>
          <DynamicFilter 
            countries={countries}
            operators={operators}
            onFilterChange={handleFilterChange}
            initialValues={{
              startDate: '2025-01-01',
              endDate: '2025-12-31'
            }}
          />
          
          {/* Sélecteur de période */}
          <div className="mt-6 flex flex-wrap items-center gap-4">
            <span className="text-sm font-medium text-gray-700">Période d'analyse:</span>
            <div className="flex flex-wrap gap-2">
              {(Object.keys(periodConfigs) as PeriodType[]).map((period) => (
                <button
                  key={period}
                  onClick={() => handlePeriodChange(period)}
                  className={`px-4 py-2 text-sm rounded-md transition-colors duration-200 ${
                    timeRange === period
                      ? 'bg-primary-500 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {period === 'weekly' ? 'Semaine' : 
                   period === 'monthly' ? 'Mois' : 
                   period === 'quarterly' ? 'Trimestre' : 'Année'}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Métriques de tendance */}
        {/* Section supprimée suite à la demande -- Tendances de Données, Voix, SMS */}

        {/* Graphiques d'évolution et répartition */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Volume Data pour les 30 Top Pays</h3>
              <span className="text-sm text-gray-500">
                Pourcentage de la consommation totale
              </span>
              {selectedContinent!=='all' && (
                <button onClick={()=>setSelectedContinent('all')} className="text-sm text-primary-600 underline">Réinitialiser la vue</button>
              )}
            </div>
            <div className="relative" style={{ height: '400px', overflowX: 'auto', overflowY: 'hidden' }}>
              {loadingTopCountries ? (
                <div className="flex justify-center items-center h-full">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                  <p className="ml-4 text-gray-600">Chargement des données...</p>
                </div>
              ) : countryConsumptionChartData.labels.length > 0 ? (
                <div style={{ minWidth: Math.max(700, countryConsumptionChartData.labels.length * 40), height: '100%' }}>
              <BarChart 
                    data={countryConsumptionChartData} 
                    width={Math.max(700, countryConsumptionChartData.labels.length * 40)} 
                height={350}
                className="mx-auto"
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      scales: {
                        y: {
                          beginAtZero: true,
                          title: {
                            display: true,
                            text: 'Pourcentage de consommation (%)'
                          }
                        },
                        x: {
                          title: {
                            display: true,
                            text: 'Pays'
                          },
                          ticks: {
                            autoSkip: false,
                            maxRotation: 45,
                            minRotation: 45
                          }
                        }
                      }
                    }}
                  />
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-gray-500">
                  Aucune donnée disponible pour le graphique.
                </div>
              )}
            </div>
            <div className="mt-4 text-sm text-gray-600">
              <p className="font-medium">À propos de ce graphique:</p>
              <ul className="list-disc pl-5 mt-2 space-y-1">
                <li>Ce graphique montre la répartition en pourcentage de la consommation totale des données entre les 30 pays les plus actifs.</li>
                <li>Le pourcentage est calculé en divisant le nombre de tests de chaque pays par le nombre total de tests effectués.</li>
                <li>Plus le pourcentage est élevé, plus le pays consomme une part importante des ressources de connexion.</li>
              </ul>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900 flex items-center">
                <FaGlobe className="mr-2 text-primary-500" />
                Répartition par région
              </h3>
            </div>
            <div className="relative flex justify-center items-center" style={{ height: '300px' }}>
              <PieChart 
                data={donutData}
                width={300}
                height={300}
                donut={true}
                className="mx-auto"
                onSliceClick={handleContinentClick}
              />
            </div>
          </div>
        </div>

        {/* Analyses clés */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center">
            <FaFilter className="mr-2 text-primary-500" />
            Points clés et analyses
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="border-l-4 border-primary-500 pl-4 py-2 bg-blue-50 rounded-r-lg">
              <h4 className="font-medium text-gray-900">Croissance globale</h4>
              <p className="text-gray-600 text-sm mt-1">L'utilisation des données continue d'augmenter (+15.8%) tandis que les SMS déclinent (-11.5%).</p>
            </div>
            
            <div className="border-l-4 border-green-500 pl-4 py-2 bg-green-50 rounded-r-lg">
              <h4 className="font-medium text-gray-900">Économies réalisées</h4>
              <p className="text-gray-600 text-sm mt-1">Baisse significative des coûts unitaires (données: -17.6%, voix: -27.3%).</p>
            </div>
            
            <div className="border-l-4 border-yellow-500 pl-4 py-2 bg-yellow-50 rounded-r-lg">
              <h4 className="font-medium text-gray-900">Répartition géographique</h4>
              <p className="text-gray-600 text-sm mt-1">L'Europe domine (45%), suivie par l'Amérique du Nord (25%).</p>
            </div>
            
            <div className="border-l-4 border-purple-500 pl-4 py-2 bg-purple-50 rounded-r-lg">
              <h4 className="font-medium text-gray-900">Prévisions</h4>
              <p className="text-gray-600 text-sm mt-1">Croissance prévue de 12-15% pour l'utilisation des données au prochain trimestre.</p>
            </div>
          </div>
        </div>

        {/* Nouvelle section: Top 30 des pays consommateurs de données */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-gray-900 flex items-center">
              <FaGlobe className="mr-2 text-primary-500" />
              Top 30 des pays consommateurs de données
            </h3>
          </div>
          
          {loadingTopCountries ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p className="ml-4 text-gray-600">Chargement des données...</p>
            </div>
          ) : topCountriesData.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rang</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre de tests</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Taux de succès</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Durée moyenne</th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opérateurs uniques</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {topCountriesData.map((country, index) => (
                    <tr key={country.country} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{index + 1}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{country.country}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{(country.total_tests || 0).toLocaleString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {typeof country.success_rate === 'number' ? `${country.success_rate.toFixed(2)}%` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {typeof country.avg_duration === 'number' ? `${country.avg_duration.toFixed(2)} ms` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{country.unique_operators || 'N/A'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              Aucune donnée disponible pour le top des pays.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrendsPage;

