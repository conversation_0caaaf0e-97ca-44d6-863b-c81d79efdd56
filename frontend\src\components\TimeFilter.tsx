import React, { useEffect, useState } from 'react';
import { getAvailableTimeFilters } from '../services/kpiService';

interface TimeFilterProps {
  onFilterChange: (filters: TimeFilters) => void;
  initialFilters?: TimeFilters;
}

export interface TimeFilters {
  period?: string;
  year?: number;
  month?: number;
  week?: number;
  day?: number;
  hour?: number;
}

interface AvailableFilters {
  years: number[];
  months: number[];
  weeks: number[];
  days: number[];
  hours: number[];
  periods: string[];
}

const TimeFilter: React.FC<TimeFilterProps> = ({ onFilterChange, initialFilters = {} }) => {
  const [filters, setFilters] = useState<TimeFilters>(initialFilters);
  const [availableFilters, setAvailableFilters] = useState<AvailableFilters>({
    years: [],
    months: [],
    weeks: [],
    days: [],
    hours: [],
    periods: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadAvailableFilters();
  }, []);

  const loadAvailableFilters = async () => {
    setLoading(true);
    try {
      const response = await getAvailableTimeFilters();
      if (response.success) {
        console.log('🔧 Filtres temporels chargés:', response.data);
        console.log('🔧 Semaines disponibles:', response.data.weeks);
        setAvailableFilters(response.data);
      } else {
        console.error('Erreur lors du chargement des filtres:', response.error);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des filtres:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof TimeFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    
    // Réinitialiser les filtres dépendants
    if (key === 'period') {
      if (value !== 'day') delete newFilters.day;
      if (value !== 'week') delete newFilters.week;
      if (value !== 'month') delete newFilters.month;
      if (value !== 'year') delete newFilters.year;
    }
    
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    const emptyFilters = {};
    setFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  if (loading) {
    return (
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-4 rounded-lg shadow">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Filtres Temporels</h3>
        <button
          onClick={clearFilters}
          className="text-sm text-blue-600 hover:text-blue-800"
        >
          Effacer
        </button>
      </div>
      
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        {/* Période */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Période
          </label>
          <select
            value={filters.period || ''}
            onChange={(e) => handleFilterChange('period', e.target.value || undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Toutes</option>
            {availableFilters.periods.map(period => (
              <option key={period} value={period}>
                {period === 'day' ? 'Jour' : 
                 period === 'week' ? 'Semaine' : 
                 period === 'month' ? 'Mois' : 
                 period === 'year' ? 'Année' : period}
              </option>
            ))}
          </select>
        </div>

        {/* Année */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Année
          </label>
          <select
            value={filters.year || ''}
            onChange={(e) => handleFilterChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Toutes</option>
            {availableFilters.years.map(year => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>

        {/* Mois */}
        {filters.period === 'month' || filters.period === 'day' ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mois
            </label>
            <select
              value={filters.month || ''}
              onChange={(e) => handleFilterChange('month', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Tous</option>
              {availableFilters.months.map(month => (
                <option key={month} value={month}>
                  {new Date(2000, month - 1).toLocaleDateString('fr-FR', { month: 'long' })}
                </option>
              ))}
            </select>
          </div>
        ) : null}

        {/* Semaine */}
        {filters.period === 'week' ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Semaine
            </label>
            <select
              value={filters.week || ''}
              onChange={(e) => handleFilterChange('week', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Toutes</option>
              {availableFilters.weeks.map(week => (
                <option key={week} value={week}>Semaine {week}</option>
              ))}
            </select>
          </div>
        ) : null}

        {/* Jour */}
        {filters.period === 'day' ? (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Jour
            </label>
            <select
              value={filters.day || ''}
              onChange={(e) => handleFilterChange('day', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Tous</option>
              {availableFilters.days.map(day => (
                <option key={day} value={day}>{day}</option>
              ))}
            </select>
          </div>
        ) : null}

        {/* Heure */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Heure
          </label>
          <select
            value={filters.hour !== undefined ? filters.hour : ''}
            onChange={(e) => handleFilterChange('hour', e.target.value !== '' ? parseInt(e.target.value) : undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Toutes</option>
            {availableFilters.hours.map(hour => (
              <option key={hour} value={hour}>{hour}h</option>
            ))}
          </select>
        </div>
      </div>

      {/* Résumé des filtres actifs */}
      {Object.keys(filters).length > 0 && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>Filtres actifs:</strong> {' '}
            {filters.period && `Période: ${filters.period}`}
            {filters.year && `, Année: ${filters.year}`}
            {filters.month && `, Mois: ${filters.month}`}
            {filters.week && `, Semaine: ${filters.week}`}
            {filters.day && `, Jour: ${filters.day}`}
            {filters.hour !== undefined && `, Heure: ${filters.hour}h`}
          </p>
        </div>
      )}
    </div>
  );
};

export default TimeFilter;
