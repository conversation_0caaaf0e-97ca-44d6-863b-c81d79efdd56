from fastapi import FastAP<PERSON>, Query, Request, Response, status, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, FileResponse
from pathlib import Path
import os
from dotenv import load_dotenv
import sys
from datetime import datetime
import uvicorn
import pandas as pd
from sqlalchemy import text
from typing import Optional, List, Dict, Any
import re
import sqlite3

# Charger les variables d'environnement
load_dotenv()

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les routes
from api.routes import roaming_routes
from api.routes import kpi_routes
from api.Kpi_routes import router as kpi_router
from api.routers.reports import router as reports_router
from api.routes.alert_routes import router as alert_router
from api.Kpi_service_clean import (
    get_weekly_attach_trend, 
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_failure_details,
    get_failure_details_extended,
    get_network_performance_stats,
    get_http_download_performance,
    get_country_overview
)
from api.get_db_connection import get_db_connection
from network_type_endpoint import get_network_type_distribution as get_network_type_distribution_func

# Créer l'application FastAPI
app = FastAPI(
    title="Roaming Dashboard API",
    description="API pour l'analyse des données de roaming",
    version="1.0.0"
)

# Configurer CORS
origins = [
    "http://localhost:5173",  # URL de développement React/Vite
    "http://localhost:3000",  # URL alternative de développement React
    "http://127.0.0.1:5173",
    "http://127.0.0.1:3000",
    "http://localhost:8000"   # URL de l'API FastAPI
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Créer les répertoires nécessaires
charts_dir = Path("charts")
if not charts_dir.exists():
    charts_dir.mkdir(parents=True)

# Route racine pour vérifier que l'API fonctionne
@app.get("/")
async def root():
    return {"status": "API is running", "version": "1.0.0"}

# Route de santé
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "Le serveur fonctionne correctement"}

# Route pour vérifier l'état de la base de données
@app.get("/api/db_status")
async def db_status():
    """Vérifie l'état de la connexion à la base de données et des tables principales"""
    try:
        engine = get_db_connection()
        if not engine:
            return {
                "status": "error",
                "message": "Impossible de se connecter à la base de données",
                "details": {
                    "connection": False,
                    "tables": {}
                }
            }
        
        tables_status = {}
        
        with engine.connect() as conn:
            # Vérifier les tables principales
            for table_name in ["steeringofroaming", "users", "kpi_data"]:
                try:
                    # Vérifier si la table existe
                    result = conn.execute(text(f"SHOW TABLES LIKE '{table_name}'")).fetchall()
                    table_exists = len(result) > 0
                    
                    # Si la table existe, compter les enregistrements
                    count = 0
                    if table_exists:
                        count = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}")).fetchone()[0]
                    
                    tables_status[table_name] = {
                        "exists": table_exists,
                        "count": count
                    }
                except Exception as e:
                    tables_status[table_name] = {
                        "exists": False,
                        "error": str(e)
                    }
        
        return {
            "status": "ok",
            "message": "Connexion à la base de données établie",
            "details": {
                "connection": True,
                "tables": tables_status
            }
        }
    except Exception as e:
        import traceback
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"Erreur lors de la vérification de la base de données: {str(e)}",
            "details": {
                "connection": False,
                "error": str(e)
            }
        }

@app.get("/api/failure_details")
async def failure_details_endpoint(
    verdict: str = Query("FAIL", description="Verdict du test (FAIL, SUCCESS, ÉCHECS, ECHECS)"),
    country: str = Query("all", description="Pays à filtrer"),
    operator: str = Query("all", description="Opérateur à filtrer")
):
    """Retourne les détails des échecs avec les colonnes spécifiques demandées"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/failure_details")
        print(f"Paramètres reçus: verdict={verdict}, country={country}, operator={operator}")

        # Normaliser le verdict
        verdict = verdict.upper()
        if verdict in ['ÉCHECS', 'ECHECS']:
            verdict = 'FAIL'
            print(f"Verdict normalisé de '{verdict}' à 'FAIL'")
        elif verdict not in ['FAIL', 'SUCCESS']:
            print(f"Verdict invalide reçu: {verdict}")
            return {
                "success": False,
                "message": "Verdict invalide. Utilisez FAIL, SUCCESS, ÉCHECS ou ECHECS.",
                "data": []
            }

        # Appeler la fonction de service
        print(f"Appel à get_failure_details_extended avec verdict={verdict}, country={country}, operator={operator}")
        result = get_failure_details_extended(verdict, country, operator)
        
        if not result:
            print("Aucune donnée trouvée dans get_failure_details_extended")
            return {
                "success": True,
                "message": "Aucune donnée trouvée",
                "data": []
            }
        
        # S'assurer que le format de réponse est cohérent
        if isinstance(result, dict) and 'data' in result:
            print(f"Données trouvées: {len(result['data'])} enregistrements")
            return {
                "success": True,
                "message": f"{len(result['data'])} enregistrements trouvés",
                "data": result['data']
            }
        else:
            print(f"Format de résultat inattendu: {type(result)}")
            return {
                "success": True,
                "message": "Données récupérées avec succès",
                "data": result if isinstance(result, list) else []
            }
        
    except Exception as e:
        print(f"Erreur dans failure_details_endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": []
        }

@app.get("/api/top_countries")
async def top_countries():
    """Retourne le top 30 des pays qui consomment le plus la connexion"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/top_countries")
        
        # Récupérer les données de tous les pays
        countries_data = get_country_overview()
        
        if not countries_data:
            print("Aucune donnée trouvée dans get_country_overview")
            return {
                "success": True,
                "message": "Aucune donnée trouvée",
                "data": []
            }
        
        # Trier les pays par nombre total de tests (consommation) et prendre les 30 premiers
        sorted_data = sorted(countries_data, key=lambda x: x['total_tests'], reverse=True)[:30]
        
        print(f"Top 30 des pays récupérés: {len(sorted_data)} pays")
        return {
            "success": True,
            "message": f"{len(sorted_data)} pays récupérés",
            "data": sorted_data
        }
        
    except Exception as e:
        print(f"Erreur dans top_countries: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": []
        }

@app.get("/api/success_rate")
async def success_rate_endpoint(country: str = None, operator: str = None):
    """Retourne le taux de succès par pays avec filtrage optionnel"""
    try:
        results = get_steering_success_by_country(country=country, operator=operator)
        return {
            "success": True,
            "data": results,
            "message": None
        }
    except Exception as e:
        print(f"Erreur dans success_rate_endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Erreur lors de la récupération des données: {str(e)}",
            "data": []
        }

@app.get("/api/network_performance_stats")
async def network_performance_stats():
    """Retourne les statistiques de performance réseau"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/network_performance_stats")
        
        # Récupérer les statistiques de performance réseau
        stats = get_network_performance_stats()
        print(f"Type de stats: {type(stats)}")
        
        if stats:
            print(f"Clés dans stats: {list(stats.keys())}")
            if 'by_country_operator' in stats:
                print(f"Nombre d'éléments dans by_country_operator: {len(stats['by_country_operator'])}")
            if 'by_date' in stats:
                print(f"Nombre d'éléments dans by_date: {len(stats['by_date'])}")
            if 'by_week' in stats:
                print(f"Nombre d'éléments dans by_week: {len(stats['by_week'])}")
            if 'by_month' in stats:
                print(f"Nombre d'éléments dans by_month: {len(stats['by_month'])}")
            if 'by_year' in stats:
                print(f"Nombre d'éléments dans by_year: {len(stats['by_year'])}")
        
        # Vérifier que toutes les clés nécessaires sont présentes
        required_keys = ['summary', 'by_country_operator', 'by_date', 'by_week', 'by_month', 'by_year', 'available_countries']
        
        # Si stats est None ou vide, créer une structure complète
        if not stats:
            print("Aucune donnée trouvée dans get_network_performance_stats")
            stats = {
                'summary': {
                    'avg_duration_sec': 0.0,
                    'success_rate_in': 0.0,
                    'success_rate_out': 0.0,
                    'total_tests': 0,
                    'total_incomplete': 0,
                    'total_countries': 0
                },
                'available_countries': [],
                'by_country_operator': [],
                'by_date': [],
                'by_week': [],
                'by_month': [],
                'by_year': []
            }
        else:
            # S'assurer que toutes les clés requises sont présentes
            for key in required_keys:
                if key not in stats:
                    print(f"Clé manquante dans les statistiques: {key}")
                    stats[key] = [] if key != 'summary' else {
                        'avg_duration_sec': 0.0,
                        'success_rate_in': 0.0,
                        'success_rate_out': 0.0,
                        'total_tests': 0,
                        'total_incomplete': 0,
                        'total_countries': 0
                    }
        
        print("Statistiques de performance réseau récupérées avec succès")
        # Retourner directement les données sans les encapsuler
        return stats
        
    except Exception as e:
        print(f"Erreur dans network_performance_stats: {str(e)}")
        import traceback
        traceback.print_exc()
        # En cas d'erreur, retourner une structure complète mais vide
        return {
            'summary': {
                'avg_duration_sec': 0.0,
                'success_rate_in': 0.0,
                'success_rate_out': 0.0,
                'total_tests': 0,
                'total_incomplete': 0,
                'total_countries': 0
            },
            'available_countries': [],
            'by_country_operator': [],
            'by_date': [],
            'by_week': [],
            'by_month': [],
            'by_year': []
        }

@app.get("/api/attachment_rates_by_country")
async def get_attachment_rates_by_country():
    """Retourne les taux d'attachement par pays"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/attachment_rates_by_country")
        
        engine = get_db_connection()
        
        with engine.connect() as conn:
            # Requête principale avec CTE pour assurer tous les niveaux
            query = """
                WITH RECURSIVE 
                Numbers AS (
                    SELECT 0 as n
                    UNION ALL
                    SELECT n + 1 FROM Numbers WHERE n < 7
                ),
                CountryTotals AS (
                    SELECT 
                        a_location_country,
                        COUNT(*) as total_tests
                    FROM steeringofroaming
                    WHERE TCName = 'SteeringOfRoaming'
                        AND a_location_country IS NOT NULL
                    GROUP BY a_location_country
                ),
                RejectionCounts AS (
                    SELECT 
                        a_location_country,
                        COALESCE(a_NrOfPlmnsRejected, 0) as rejection_level,
                        COUNT(*) as count
                    FROM steeringofroaming
                    WHERE TCName = 'SteeringOfRoaming'
                        AND a_location_country IS NOT NULL
                    GROUP BY a_location_country, a_NrOfPlmnsRejected
                ),
                AllCombinations AS (
                    SELECT 
                        ct.a_location_country,
                        n.n as rejection_level
                    FROM CountryTotals ct
                    CROSS JOIN Numbers n
                ),
                FullData AS (
                    SELECT 
                        ac.a_location_country,
                        ac.rejection_level,
                        COALESCE(rc.count, 0) as count,
                        ct.total_tests
                    FROM AllCombinations ac
                    LEFT JOIN RejectionCounts rc 
                        ON ac.a_location_country = rc.a_location_country 
                        AND ac.rejection_level = rc.rejection_level
                    JOIN CountryTotals ct 
                        ON ac.a_location_country = ct.a_location_country
                ),
                SuccessRates AS (
                    SELECT 
                        a_location_country,
                        ROUND(SUM(CASE WHEN a_NrOfPlmnsRejected > 0 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
                    FROM steeringofroaming
                    WHERE TCName = 'SteeringOfRoaming'
                        AND a_location_country IS NOT NULL
                    GROUP BY a_location_country
                )
                SELECT 
                    fd.a_location_country as country,
                    ct.total_tests,
                    sr.success_rate,
                    JSON_OBJECT(
                        '0', ROUND(SUM(CASE WHEN fd.rejection_level = 0 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '1', ROUND(SUM(CASE WHEN fd.rejection_level = 1 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '2', ROUND(SUM(CASE WHEN fd.rejection_level = 2 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '3', ROUND(SUM(CASE WHEN fd.rejection_level = 3 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '4', ROUND(SUM(CASE WHEN fd.rejection_level = 4 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '5', ROUND(SUM(CASE WHEN fd.rejection_level = 5 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '6', ROUND(SUM(CASE WHEN fd.rejection_level = 6 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2),
                        '7', ROUND(SUM(CASE WHEN fd.rejection_level = 7 THEN fd.count ELSE 0 END) * 100.0 / ct.total_tests, 2)
                    ) as attachment_distribution
                FROM FullData fd
                JOIN CountryTotals ct ON fd.a_location_country = ct.a_location_country
                JOIN SuccessRates sr ON fd.a_location_country = sr.a_location_country
                GROUP BY fd.a_location_country, ct.total_tests, sr.success_rate
                ORDER BY sr.success_rate DESC;
            """
            
            print("Exécution de la requête principale...")
            result = conn.execute(text(query))
            rows = result.fetchall()
            
            # Formater les résultats
            formatted_data = []
            for row in rows:
                country_data = {
                    "country": row[0],
                    "total_tests": row[1],
                    "success_rate": row[2],
                    "attachment_distribution": row[3]
                }
                formatted_data.append(country_data)
            
            print(f"Données récupérées: {len(formatted_data)} pays")
            if formatted_data:
                print("Exemple de données pour le premier pays:", formatted_data[0])
            
            # Calculer les KPI globaux
            total_countries = len(formatted_data)
            total_tests = sum(item["total_tests"] for item in formatted_data)
            avg_success_rate = sum(item["success_rate"] for item in formatted_data) / total_countries if total_countries > 0 else 0
            
            # Ajouter les KPI au résultat
            kpi_data = {
                "total_countries": total_countries,
                "total_tests": total_tests,
                "avg_success_rate": round(avg_success_rate, 2)
            }
            
            return {
                "success": True,
                "message": f"{len(formatted_data)} pays trouvés",
                "data": formatted_data,
                "kpi": kpi_data
            }
            
    except Exception as e:
        print(f"Erreur dans get_attachment_rates_by_country: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": [],
            "kpi": {
                "total_countries": 0,
                "total_tests": 0,
                "avg_success_rate": 0
            }
        }

@app.get("/api/http_download_performance")
async def http_download_performance(
    country: str = Query("all", description="Pays à filtrer"),
    operator: str = Query("all", description="Opérateur à filtrer")
):
    """Retourne les statistiques de débit HTTP à partir de la table HTTP_Download_20"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/http_download_performance")
        print(f"Paramètres reçus: country={country}, operator={operator}")
        
        # Récupérer les statistiques de débit HTTP avec les filtres
        stats = get_http_download_performance(country, operator)
        print(f"Type de stats: {type(stats)}")
        
        if stats:
            print(f"Clés dans stats: {list(stats.keys())}")
            if 'country_stats' in stats:
                print(f"Nombre d'éléments dans country_stats: {len(stats['country_stats'])}")
            if 'operator_stats' in stats:
                print(f"Nombre d'éléments dans operator_stats: {len(stats['operator_stats'])}")
            if 'country_operator_stats' in stats:
                print(f"Nombre d'éléments dans country_operator_stats: {len(stats['country_operator_stats'])}")
        
        # Vérifier que toutes les clés nécessaires sont présentes
        required_keys = ['summary', 'country_stats', 'operator_stats', 'country_operator_stats']
        
        # Si stats est None ou vide, créer une structure complète
        if not stats:
            print("Aucune donnée trouvée dans get_http_download_performance")
            stats = {
                'summary': {
                    'total_tests': 0,
                    'avg_data_rate_kbps': 0,
                    'avg_data_rate_mbps': 0,
                    'avg_activation_time_sec': 0,
                    'total_countries': 0,
                    'total_operators': 0
                },
                'country_stats': [],
                'operator_stats': [],
                'country_operator_stats': []
            }
        else:
            # S'assurer que toutes les clés requises sont présentes
            for key in required_keys:
                if key not in stats:
                    print(f"Clé manquante dans les statistiques: {key}")
                    if key == 'summary':
                        stats[key] = {
                            'total_tests': 0,
                            'avg_data_rate_kbps': 0,
                            'avg_data_rate_mbps': 0,
                            'avg_activation_time_sec': 0,
                            'total_countries': 0,
                            'total_operators': 0
                        }
                    else:
                        stats[key] = []
        
        # Adapter les noms de clés pour maintenir la compatibilité avec le frontend
        adapted_stats = {
            'summary': stats['summary'],
            'by_country': stats['country_stats'],
            'by_operator': stats['operator_stats'],
            'by_country_operator': stats['country_operator_stats'],
            'available_countries': stats.get('available_countries', []),
            'available_operators': stats.get('available_operators', [])
        }
        
        print("Statistiques de débit HTTP récupérées avec succès")
        return adapted_stats
        
    except Exception as e:
        print(f"Erreur dans http_download_performance: {str(e)}")
        import traceback
        traceback.print_exc()
        # En cas d'erreur, retourner une structure complète mais vide
        return {
            'summary': {
                'total_tests': 0,
                'avg_data_rate_kbps': 0,
                'avg_data_rate_mbps': 0,
                'avg_activation_time_sec': 0,
                'total_countries': 0,
                'total_operators': 0
            },
            'by_country': [],
            'by_operator': [],
            'by_country_operator': []
        }

@app.get("/api/operators")
async def get_operators(country: str = Query(None, description="Filtrer les opérateurs par pays")):
    """Retourne la liste des opérateurs disponibles dans la table steeringofroaming, filtrés par pays si précisé"""
    try:
        engine = get_db_connection()
        if not engine:
            raise Exception("Impossible de se connecter à la base de données")
        with engine.connect() as conn:
            if country and country != 'all':
                result = conn.execute(
                    text("""
                        SELECT DISTINCT a_UsedPLMNName
                        FROM steeringofroaming
                        WHERE a_UsedPLMNName IS NOT NULL
                        AND a_UsedPLMNName != ''
                        AND a_UsedPLMNName != 'a_UsedPLMNName'
                        AND TCName = 'SteeringOfRoaming'
                        AND a_location_country = :country
                        ORDER BY a_UsedPLMNName
                    """), {"country": country}
                )
            else:
                result = conn.execute(
                    text("""
                        SELECT DISTINCT a_UsedPLMNName
                        FROM steeringofroaming
                        WHERE a_UsedPLMNName IS NOT NULL
                        AND a_UsedPLMNName != ''
                        AND a_UsedPLMNName != 'a_UsedPLMNName'
                        AND TCName = 'SteeringOfRoaming'
                        ORDER BY a_UsedPLMNName
                    """)
                )
            operators = [{"value": row[0], "label": row[0]} for row in result.fetchall()]
        return {"success": True, "data": operators}
    except Exception as e:
        print(f"Erreur lors de la récupération des opérateurs: {e}")
        return {"success": False, "message": str(e), "data": []}

@app.get("/api/kpi/weeks")
async def get_weeks(
    DayOfMonth: Optional[List[int]] = Query(None, description="Liste des jours du mois à filtrer (1-31)", example=[14, 21]),
    DayOfWeek: Optional[List[str]] = Query(None, description="Liste des jours de la semaine à filtrer. Exemples: '(1)Mon', '1', '(7)Sun' (1=Lundi, 7=Dimanche)", example=["(1)Mon", "3"]),
    HourOfDay: Optional[List[int]] = Query(None, description="Liste des heures à filtrer (0-23)", example=[9, 14]),
    MonthOfYear: Optional[List[int]] = Query(None, description="Liste des mois à filtrer (1-12)", example=[5, 6])
):
    """
    Retourne la liste des semaines disponibles, avec filtrage avancé sur DayOfMonth, DayOfWeek, HourOfDay, MonthOfYear.
    - DayOfWeek accepte les formats '(1)Mon', '1', etc. (1=Lundi, 7=Dimanche)
    - Combine les filtres avec une logique ET.
    - Retourne les champs normalisés dans la réponse.
    """
    import logging
    engine = get_db_connection()
    if not engine:
        raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
    where_clauses = []
    params = {}

    # Validation et construction des filtres
    if DayOfMonth:
        for d in DayOfMonth:
            if not (1 <= d <= 31):
                raise HTTPException(status_code=422, detail="DayOfMonth doit être entre 1 et 31")
        where_clauses.append(f"DayOfMonth IN :day_of_month")
        params["day_of_month"] = tuple(DayOfMonth)

    normalized_days = []
    if DayOfWeek:
        for val in DayOfWeek:
            if isinstance(val, int):
                day = val
            else:
                match = re.match(r"\((\d+)\)", val)
                if match:
                    day = int(match.group(1))
                elif val.isdigit():
                    day = int(val)
                else:
                    logging.warning(f"Entrée DayOfWeek mal formatée: {val}")
                    raise HTTPException(status_code=422, detail=f"Format invalide pour DayOfWeek: {val}")
            if not (1 <= day <= 7):
                logging.warning(f"DayOfWeek hors limites: {val}")
                raise HTTPException(status_code=422, detail="DayOfWeek doit être entre 1 (Lundi) et 7 (Dimanche)")
            normalized_days.append(day)
        where_clauses.append(f"DayOfWeek IN :day_of_week")
        params["day_of_week"] = tuple(normalized_days)

    if HourOfDay:
        for h in HourOfDay:
            if not (0 <= h <= 23):
                raise HTTPException(status_code=422, detail="HourOfDay doit être entre 0 et 23")
        where_clauses.append(f"HourOfDay IN :hour_of_day")
        params["hour_of_day"] = tuple(HourOfDay)

    if MonthOfYear:
        for m in MonthOfYear:
            if not (1 <= m <= 12):
                raise HTTPException(status_code=422, detail="MonthOfYear doit être entre 1 et 12")
        where_clauses.append(f"MonthOfYear IN :month_of_year")
        params["month_of_year"] = tuple(MonthOfYear)

    # Construction de la requête SQL dynamique
    sql = "SELECT DISTINCT WeekOfYear, YEAR(Timestamp) as Year FROM steeringofroaming WHERE WeekOfYear IS NOT NULL AND WeekOfYear != 0 AND YEAR(Timestamp) IS NOT NULL AND YEAR(Timestamp) != 0"
    if where_clauses:
        sql += " AND " + " AND ".join(where_clauses)
    sql += " ORDER BY Year DESC, WeekOfYear DESC"

    # Exécution de la requête
    with engine.connect() as conn:
        result = conn.execute(text(sql), params)
        rows = result.fetchall()
        response = []
        for row in rows:
            week_of_year = row[0]
            year = row[1]
            response.append({"week": week_of_year, "year": year})
    return response

@app.get("/api/test/success_rate_data")
async def test_success_rate_data(country: str = None, operator: str = None):
    """Endpoint de test pour vérifier le format des données de taux de succès"""
    try:
        # Simuler des données de test au format exact attendu par le frontend
        test_data = [
            {
                "country": "France", 
                "Taux de succès": 85.75, 
                "Échecs": 14.25,
                "Entrées": 100
            },
            {
                "country": "Germany", 
                "Taux de succès": 92.30, 
                "Échecs": 7.70,
                "Entrées": 80
            },
            {
                "country": "Italy", 
                "Taux de succès": 78.50, 
                "Échecs": 21.50,
                "Entrées": 120
            }
        ]
        
        print(f"Données de test envoyées: {test_data}")
        return test_data
    except Exception as e:
        print(f"Erreur dans test_success_rate_data: {str(e)}")
        return []

@app.get("/api/country_overview")
async def get_countries_overview():
    """Retourne la liste des pays disponibles dans la colonne a_location_country."""
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            query = text(
                """
                SELECT DISTINCT a_location_country
                FROM steeringofroaming
                WHERE a_location_country IS NOT NULL
                  AND a_location_country != ''
                  AND TCName = 'SteeringOfRoaming'
                ORDER BY a_location_country
                """
            )
            print("Exécution de la requête SQL pour country_overview")
            result = conn.execute(query)
            countries = [
                {"value": row[0], "label": row[0]} for row in result if row[0]
            ]
            print(f"Pays formatés pour country_overview: {countries}")
            return countries
    except Exception as e:
        print(f"Erreur lors de la récupération des pays pour l'aperçu: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Erreur interne du serveur: {str(e)}")

@app.get("/api/network_type_distribution")
async def get_network_type_distribution(country: str = None, operator: str = None):
    """
    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming
    """
    return await get_network_type_distribution_func(country, operator)

# Monter les répertoires statiques
app.mount("/charts", StaticFiles(directory=str(charts_dir)), name="charts")

# Inclure les routes - Notez que le préfixe /api est retiré car il est déjà dans les routers
app.include_router(roaming_routes.router)
app.include_router(kpi_router)
app.include_router(reports_router, prefix="/api")
app.include_router(alert_router)  # Inclure le router d'alertes

# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    print(f"Exception globale: {str(exc)}")
    import traceback
    traceback.print_exc()
    return JSONResponse(
        status_code=500,
        content={"message": f"Une erreur interne s'est produite: {str(exc)}"}
    )

@app.middleware("http")
async def log_requests(request, call_next):
    """Log toutes les requêtes entrantes"""
    print(f"\n[{datetime.now()}] ⚠️⚠️⚠️ REQUÊTE REÇUE: {request.method} {request.url.path} avec query params: {request.query_params}")
    
    # Gérer le cas spécifique de success_rate
    if 'success_rate' in request.url.path:
        print(f"⭐⭐⭐ DÉTECTION DE REQUÊTE success_rate: {request.url}")
    
    # Continuer le traitement normal
    response = await call_next(request)
    print(f"[{datetime.now()}] ✅✅✅ RÉPONSE ENVOYÉE: {request.method} {request.url.path} avec status: {response.status_code}")
    
    return response

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True) 