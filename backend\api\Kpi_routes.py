from fastapi import APIRouter, HTTPException, Query, UploadFile, File
from typing import Optional, List, Dict, Any, Union
import os
import shutil
import pandas as pd
from .Kpi_service_clean import (
    get_steering_success_by_country,
    get_fail_causes, 
    get_weekly_attach_trend,
    get_annual_trend,
    get_failure_analysis_by_filter,
    get_country_overview,
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_current_csv_path,
    get_failure_details_extended,
    get_failure_details,
    get_db_connection,
    get_http_download_performance,
    get_http_period_data,
    logger
)
from datetime import datetime
import calendar
import logging
from pydantic import BaseModel, Field, validator

logger = logging.getLogger(__name__)

# Modèles Pydantic simplifiés
class BaseResponse(BaseModel):
    success: bool = True
    message: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True

class SteeringSuccessResponse(BaseResponse):
    data: List[Dict[str, Any]] = []

class AttachmentRateResponse(BaseResponse):
    data: List[Dict[str, Any]] = []

class PeriodDataResponse(BaseResponse):
    data: List[Dict[str, Any]] = []
    period_type: str

class HttpPerformanceResponse(BaseResponse):
    by_country_operator: List[Dict[str, Any]]

# Création du router avec le préfixe /api
router = APIRouter(prefix="/api")

@router.get("/kpi/success_rate")
async def success_rate(country: Optional[str] = None):
    """Retourne le taux de réussite par pays avec filtrage optionnel"""
    try:
        results = get_steering_success_by_country(country=country)
        return {
            "success": True,
            "data": results,
            "message": None
        }
    except Exception as e:
        logger.error(f"Erreur dans success_rate: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/kpi/fail_causes")
async def fail_causes(country: Optional[str] = None, operator: Optional[str] = None, verdict: Optional[str] = None):
    """Retourne les causes d'échec les plus fréquentes avec filtrage optionnel"""
    try:
        data = get_fail_causes(country=country, operator=operator)

        # Calculer les pourcentages
        total_count = sum(item['count'] for item in data)

        # Ajouter les pourcentages aux données
        for item in data:
            item['percentage'] = (item['count'] / total_count * 100) if total_count > 0 else 0

        return {
            "success": True,
            "data": data,
            "total_count": total_count,
            "message": f"Trouvé {len(data)} causes d'échec"
        }
    except Exception as e:
        logger.error(f"Erreur dans fail_causes: {str(e)}")
        return {
            "success": False,
            "data": [],
            "error": str(e),
            "message": "Erreur lors de la récupération des causes d'échec"
        }

@router.get("/kpi/weekly_attach_trend")
async def weekly_attach_trend(country: Optional[str] = None, operator: Optional[str] = None):
    """Retourne les tendances hebdomadaires avec filtrage optionnel"""
    try:
        logger.info(f"Appel à /api/kpi/weekly_attach_trend avec country={country}, operator={operator}")
        result = get_weekly_attach_trend(country=country, operator=operator)
        logger.info(f"Résultat obtenu: {len(result.get('data', []))} entrées")
        
        # Vérification des données reçues
        if not result or not result.get('data'):
            logger.warning(f"Aucune donnée trouvée pour weekly_attach_trend avec country={country}, operator={operator}")
            return {
                "success": True,
                "data": [],
                "stats": {
                    "total_records": 0,
                    "countries": 0,
                    "operators": 0,
                    "avg_success_rate": 0
                },
                "message": "Aucune donnée disponible pour les critères sélectionnés"
            }
        
        return {
            "success": True,
            "data": result.get('data', []),
            "stats": result.get('stats', {
                "total_records": 0,
                "countries": 0,
                "operators": 0,
                "avg_success_rate": 0
            }),
            "message": None
        }
    except Exception as e:
        logger.error(f"Erreur dans weekly_attach_trend: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # Retourner une structure vide mais valide au lieu de lever une exception
        return {
            "success": False,
            "data": [],
            "stats": {
                "total_records": 0,
                "countries": 0,
                "operators": 0,
                "avg_success_rate": 0
            },
            "message": f"Erreur lors de la récupération des données: {str(e)}"
        }

@router.get("/kpi/annual_trend")
async def annual_trend(country: Optional[str] = None):
    """Retourne les tendances annuelles avec filtrage optionnel"""
    try:
        return get_annual_trend(country=country)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/kpi/failure_analysis")
async def failure_analysis(country: Optional[str] = None):
    """Retourne l'analyse détaillée des échecs avec filtrage optionnel"""
    try:
        return get_failure_analysis_by_filter(country=country)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/kpi/country_overview")
async def country_overview():
    """Retourne un aperçu des performances par pays"""
    try:
        return get_country_overview()
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/steering_success_by_country")
async def steering_success_by_country_route(country: Optional[str] = None, operator: Optional[str] = None):
    """Route pour les données de taux de succès par pays"""
    try:
        logger.info(f"Appel à /api/steering_success_by_country avec country={country}, operator={operator}")
        data = get_steering_success_by_country(country=country, operator=operator)
        logger.info(f"Données reçues pour {len(data)} pays")
        
        return {
            "success": True,
            "data": data if data else [],
            "message": None
        }
    except Exception as e:
        logger.error(f"Erreur dans steering_success_by_country: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachment_rates_by_country")
async def attachment_rates_route():
    """Route pour les données de taux d'attachement par pays"""
    try:
        logger.info("Appel à /api/attachment_rates_by_country")
        data = get_attachment_rates_by_country()
        logger.info(f"Données reçues pour {len(data)} pays")
        
        # Vérifier que les données sont présentes et dans le bon format
        if not data:
            logger.warning("Aucune donnée trouvée pour attachment_rates_by_country")
            return {
                "success": True,
                "data": [],
                "message": "Aucune donnée disponible"
            }
        
        # Format de réponse standardisé
        response = {
            "success": True,
            "data": data,
            "message": None
        }
        
        logger.info(f"Réponse formatée avec succès: {len(data)} entrées")
        
        # Log du premier élément pour débogage
        if data and len(data) > 0:
            logger.info(f"Premier élément: {data[0]}")
            
        return response
    except Exception as e:
        logger.error(f"Erreur dans attachment_rates_by_country: {str(e)}")
        logger.exception("Détail de l'erreur:")
        return {
            "success": False,
            "data": [],
            "message": f"Erreur lors de la récupération des données: {str(e)}"
        }

@router.get("/weeks")
async def get_available_weeks():
    """Récupère la liste des semaines disponibles"""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données.")

        cursor = connection.cursor(dictionary=True)
        
        # Requête pour récupérer les semaines disponibles
        query = """
        SELECT DISTINCT 
            YEAR(Timestamp) as year,
            WEEK(Timestamp) as week
        FROM steeringofroaming
        WHERE TCName = 'SteeringOfRoaming'
        AND YEAR(Timestamp) > 0
        AND WEEK(Timestamp) > 0
        ORDER BY year DESC, week DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        # Formater les résultats
        weeks = [{"week": row['week'], "year": row['year']} for row in results]
        
        return weeks

    except Exception as e:
        logger.error(f"Erreur lors de la récupération des semaines: {str(e)}")
        if 'connection' in locals() and connection.is_connected():
            connection.close()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des semaines: {str(e)}")

@router.get("/kpi/period_data")
async def period_data_route(period: str = Query("month", description="Période à analyser: week, month, quarter ou year")):
    """Route pour les données par période"""
    try:
        logger.info(f"Appel à /api/kpi/period_data avec period={period}")
        
        if period.lower() == "month":
            data = get_annual_trend()
            logger.info(f"Données mensuelles reçues pour {len(data)} mois")
        else:
            data = get_weekly_attach_trend()
            logger.info(f"Données hebdomadaires reçues pour {len(data)} semaines")
        
        return {
            "success": True,
            "data": data if data else [],
            "period_type": period,
            "message": None
        }
    except Exception as e:
        logger.error(f"Erreur dans period_data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/kpi/http_period_data")
async def http_period_data_route(
    period: str = Query("month", description="Période à analyser: day, week, month, quarter ou year"),
    country: Optional[str] = Query(None, description="Pays à filtrer"),
    operator: Optional[str] = Query(None, description="Opérateur à filtrer"),
    year: Optional[int] = Query(None, description="Année à filtrer"),
    month: Optional[int] = Query(None, description="Mois à filtrer"),
    week: Optional[int] = Query(None, description="Semaine à filtrer"),
    quarter: Optional[int] = Query(None, description="Trimestre à filtrer")
):
    """Route pour les données HTTP par période"""
    try:
        logger.info(f"Appel à /api/kpi/http_period_data avec period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}")
        
        data = get_http_period_data(
            period=period, 
            country=country, 
            operator=operator, 
            year=year, 
            month=month, 
            week=week, 
            quarter=quarter
        )
        
        logger.info(f"Données HTTP par période récupérées: {len(data)} entrées")
        
        return {
            "success": True,
            "data": data,
            "period_type": period,
            "message": None
        }
    except Exception as e:
        logger.error(f"Erreur dans http_period_data_route: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {
            "success": False,
            "data": [],
            "period_type": period,
            "message": f"Erreur lors de la récupération des données: {str(e)}"
        }

@router.get("/kpi/http_download_performance", response_model=HttpPerformanceResponse)
async def http_download_performance_route():
    """Endpoint pour obtenir les statistiques de performance de téléchargement HTTP."""
    try:
        data = get_http_download_performance()
        if not data or not data.get("by_country_operator"):
            # Renvoyer une réponse valide mais vide si aucune donnée n'est trouvée
            return HttpPerformanceResponse(by_country_operator=[])
        return HttpPerformanceResponse(**data)
    except Exception as e:
        logger.error(f"Erreur dans http_download_performance_route: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Chemin du dossier pour les uploads
# Il est recommandé d'utiliser un chemin absolu ou relatif stable
UPLOAD_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'uploads')
os.makedirs(UPLOAD_DIR, exist_ok=True)

@router.post("/upload/csv")
async def upload_csv_file(file: UploadFile = File(...)):
    """Permet de lire les données directement depuis la base de données kpi"""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données.")

        cursor = connection.cursor(dictionary=True)
        
        # Requête pour récupérer les données de la table steeringofroaming
        query = """
        SELECT 
            TCName,
            Timestamp,
            Verdict,
            a_location_country,
            a_UsedPLMNName,
            Year,
            MonthOfYear,
            DayOfMonth,
            HourOfDay,
            WeekOfYear,
            DayOfWeek,
            a_LupDuration,
            errorText,
            a_NrOfPlmnsRejected
        FROM steeringofroaming
        WHERE TCName = 'SteeringOfRoaming'
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        if not results:
            return {
                "status": "success",
                "message": "Aucune donnée trouvée dans la base de données",
                "rows_processed": 0
            }
            
        logger.info(f"{len(results)} lignes récupérées depuis la base de données")
        
        cursor.close()
        connection.close()
        
        return {
            "status": "success",
            "message": f"{len(results)} lignes récupérées avec succès depuis la base de données",
            "rows_processed": len(results)
        }

    except Exception as e:
        logger.error(f"Erreur lors de la lecture des données: {str(e)}")
        if 'connection' in locals() and connection.is_connected():
            connection.close()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la lecture des données: {str(e)}")

@router.get("/debug/csv_analysis")
async def debug_csv_analysis():
    """Analyse détaillée du contenu du CSV"""
    try:
        # Obtenir le chemin du fichier CSV
        csv_path = get_current_csv_path()
        
        if not os.path.exists(csv_path):
            return {"status": "error", "message": "Fichier CSV non trouvé", "path": csv_path}
        
        # Charger le CSV avec détection automatique du séparateur
        try:
            df = pd.read_csv(csv_path, sep=';')
        except:
            try:
                df = pd.read_csv(csv_path, sep=',')
            except:
                return {"status": "error", "message": "Impossible de lire le fichier CSV, format non reconnu"}
        
        # Colonnes importantes à analyser spécifiquement
        important_columns = [
            'a_LupDuration', 'verdict', 'errortext', 'a_plmnrejected', 
            'a_nrofluprequests', 'a_rejectCause', 'a_rejectedPLMNs',
            'a_networkType', 'a_locationContry', 'a_Usedplmnname', 
            'a_usedplmn', 'a_TADIG', 'a_VPLMN_registered'
        ]
        
        # Vérifier quelles colonnes importantes sont présentes
        present_columns = [col for col in important_columns if col in df.columns]
        missing_columns = [col for col in important_columns if col not in df.columns]
        
        # Vérifier si des colonnes similaires existent pour celles qui manquent
        similar_columns = {}
        for missing in missing_columns:
            lower_missing = missing.lower()
            matches = [col for col in df.columns if lower_missing in col.lower()]
            if matches:
                similar_columns[missing] = matches
        
        # Calculer des statistiques de base pour les colonnes numériques
        numeric_stats = {}
        for col in df.select_dtypes(include=['number']).columns:
            numeric_stats[col] = {
                "min": float(df[col].min()),
                "max": float(df[col].max()),
                "mean": float(df[col].mean()),
                "missing": int(df[col].isna().sum())
            }
        
        # Statistiques par pays
        country_stats = {}
        country_col = None
        
        # Trouver la colonne de pays
        potential_country_cols = ['a_locationContry', 'a_location_country', 'country', 'pays']
        for col in potential_country_cols:
            if col in df.columns:
                country_col = col
                break
        
        if country_col:
            countries = df[country_col].unique().tolist()
            for country in countries:
                if pd.notna(country):
                    country_df = df[df[country_col] == country]
                    country_stats[country] = {
                        "count": len(country_df),
                        "percent": round(len(country_df) / len(df) * 100, 2)
                    }
        
        # Statistiques par opérateur
        operator_stats = {}
        operator_col = None
        
        # Trouver la colonne d'opérateur
        potential_operator_cols = ['a_Usedplmnname', 'a_usedplmn', 'operator', 'operateur']
        for col in potential_operator_cols:
            if col in df.columns:
                operator_col = col
                break
        
        if operator_col:
            operators = df[operator_col].unique().tolist()
            for operator in operators:
                if pd.notna(operator) and operator:
                    operator_df = df[df[operator_col] == operator]
                    operator_stats[operator] = {
                        "count": len(operator_df),
                        "percent": round(len(operator_df) / len(df) * 100, 2)
                    }
        
        # Résultat
        result = {
            "file_path": csv_path,
            "total_rows": len(df),
            "total_columns": len(df.columns),
            "columns": df.columns.tolist(),
            "present_important_columns": present_columns,
            "missing_important_columns": missing_columns,
            "similar_columns": similar_columns,
            "numeric_stats": numeric_stats,
            "country_stats": country_stats,
            "operator_stats": operator_stats,
            "preview": df.head(3).to_dict('records')
        }
        
        return result
    except Exception as e:
        return {"status": "error", "message": str(e)}

# Routes avec le préfixe /kpi pour correspondre aux routes demandées par le frontend
@router.get("/kpi/weeks", response_model=List[int])
async def get_weeks_prefixed():
    """Récupère la liste des semaines disponibles avec le préfixe kpi"""
    return await get_available_weeks()

@router.get("/debug/weeks")
async def debug_weeks():
    """Endpoint de debug pour examiner les données de semaines dans la base de données"""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données.")

        cursor = connection.cursor(dictionary=True)
        
        # Requête pour voir toutes les semaines disponibles
        query = """
        SELECT 
            DISTINCT 
            YEAR(Timestamp) as year,
            WEEK(Timestamp) as week,
            MIN(Timestamp) as first_date,
            MAX(Timestamp) as last_date,
            COUNT(*) as total_records
        FROM steeringofroaming
        WHERE TCName = 'SteeringOfRoaming'
        GROUP BY YEAR(Timestamp), WEEK(Timestamp)
        ORDER BY year DESC, week DESC
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        # Requête pour voir le nombre total d'enregistrements
        cursor.execute("SELECT COUNT(*) as total FROM steeringofroaming WHERE TCName = 'SteeringOfRoaming'")
        total_count = cursor.fetchone()['total']
        
        # Requête pour récupérer tous les pays distincts
        cursor.execute("""
        SELECT DISTINCT a_location_country as country, COUNT(*) as records_count 
        FROM steeringofroaming 
        WHERE TCName = 'SteeringOfRoaming' AND a_location_country IS NOT NULL AND a_location_country != ''
        GROUP BY a_location_country
        ORDER BY records_count DESC
        """)
        countries = cursor.fetchall()
        
        # Requête pour récupérer tous les opérateurs distincts
        cursor.execute("""
        SELECT DISTINCT a_Usedplmnname as operator, COUNT(*) as records_count 
        FROM steeringofroaming 
        WHERE TCName = 'SteeringOfRoaming' AND a_Usedplmnname IS NOT NULL AND a_Usedplmnname != ''
        GROUP BY a_Usedplmnname
        ORDER BY records_count DESC
        """)
        operators = cursor.fetchall()
        
        cursor.close()
        connection.close()
        
        return {
            "total_records": total_count,
            "weeks_data": results,
            "countries_data": countries,
            "operators_data": operators
        }

    except Exception as e:
        logger.error(f"Erreur lors de l'examen des données: {str(e)}")
        if 'connection' in locals() and connection.is_connected():
            connection.close()
        raise HTTPException(status_code=500, detail=f"Erreur lors de l'examen des données: {str(e)}")

@router.get("/top_countries_consumption")
def get_top_countries_consumption_route():
    return kpi_service.get_top_countries_consumption()