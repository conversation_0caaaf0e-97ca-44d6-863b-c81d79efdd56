import { ChartDataset, ChartTypeRegistry } from 'chart.js';
import Chart from 'chart.js/auto';
import React, { useEffect, useRef } from 'react';

interface StandardBarChartProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor: string | string[];
      type?: string;
      borderColor?: string;
      borderWidth?: number;
      lineTension?: number;
      pointBackgroundColor?: string;
      pointBorderColor?: string;
      pointRadius?: number;
      yAxisID?: string;
      order?: number;
    }[];
  };
  index?: never;
  categories?: never;
  colors?: never;
  customTooltip?: never;
  valueFormatter?: never;
  width?: number;
  height?: number;
  className?: string;
  options?: any;
  maxValue?: number;
  minValue?: number;
  stack?: boolean;
  showLegend?: boolean;
}

interface SimplifiedBarChartProps {
  data: Array<Record<string, any>>;
  index: string;
  categories: string[];
  colors?: string[];
  customTooltip?: (props: any) => React.ReactNode;
  valueFormatter?: (value: number) => string;
  width?: number;
  height?: number;
  className?: string;
  options?: any;
  maxValue?: number;
  minValue?: number;
  stack?: boolean;
  showLegend?: boolean;
}

type BarChartProps = StandardBarChartProps | SimplifiedBarChartProps;

const BarChart: React.FC<BarChartProps> = (props) => {
  const chartRef = useRef<HTMLCanvasElement | null>(null);
  const chartInstance = useRef<Chart | null>(null);

  const defaultColors = [
    'rgba(59, 130, 246, 0.8)', // Bleu
    'rgba(16, 185, 129, 0.8)', // Vert
    'rgba(245, 158, 11, 0.8)', // Jaune
    'rgba(239, 68, 68, 0.8)',  // Rouge
    'rgba(139, 92, 246, 0.8)', // Violet
    'rgba(236, 72, 153, 0.8)'  // Rose
  ];

  const {
    width,
    height,
    className,
    options,
    maxValue,
    minValue,
    stack = false,
    showLegend = true
  } = props;

  useEffect(() => {
    if (chartRef.current) {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }

      const ctx = chartRef.current.getContext('2d');
      if (ctx) {
        let formattedData: {
          labels: string[];
          datasets: Array<{
            label: string;
            data: number[];
            backgroundColor: string | string[];
            type?: string;
            borderColor?: string;
            borderWidth?: number;
            lineTension?: number;
            pointBackgroundColor?: string;
            pointBorderColor?: string;
            pointRadius?: number;
            yAxisID?: string;
            order?: number;
          }>;
        };

        // Déterminer quel format de données est utilisé
        if ('index' in props && props.index && 'categories' in props && props.categories) {
          // Format simplifié (index, categories)
          const { data, index, categories, colors = defaultColors } = props;
          
          // Convertir le format simplifié au format standard attendu par Chart.js
          const labels = data.map(item => item[index]);
          const datasets = categories.map((category, i) => ({
            label: category,
            data: data.map(item => typeof item[category] === 'number' ? item[category] : 0),
            backgroundColor: colors[i % colors.length],
            borderColor: colors[i % colors.length],
            borderWidth: 1,
            type: 'bar',
            order: 1,
            pointRadius: 0,
            lineTension: 0,
            yAxisID: 'y'
          }));

          formattedData = {
            labels,
            datasets
          };
        } else {
          // Format standard
          formattedData = (props as StandardBarChartProps).data;
        }

        const formattedDatasets = formattedData.datasets.map((dataset, index) => {
          // Pour les datasets de type 'line', conserver leurs propriétés spéciales
          if (dataset.type === 'line') {
            return {
              ...dataset,
              borderWidth: dataset.borderWidth || 2,
              pointRadius: dataset.pointRadius || 3,
              tension: dataset.lineTension || 0.1,
              fill: false,
              order: dataset.order || 0 // Un ordre plus petit place l'élément au-dessus
            };
          }
          
          // Pour les barres
          return {
            ...dataset,
            type: dataset.type || 'bar',
            borderColor: dataset.borderColor || defaultColors[index % defaultColors.length],
            borderWidth: dataset.borderWidth || 1,
            borderRadius: 4,
            barThickness: 'flex',
            order: dataset.order || 1, // Les barres ont un ordre plus élevé par défaut
            stack: stack ? 'stack0' : undefined,
            hoverBackgroundColor: typeof dataset.backgroundColor === 'string' 
              ? dataset.backgroundColor.replace('0.8', '1') 
              : defaultColors[index % defaultColors.length].replace('0.8', '1'),
            hoverBorderColor: typeof dataset.backgroundColor === 'string'
              ? dataset.backgroundColor.replace('0.8', '1')
              : defaultColors[index % defaultColors.length].replace('0.8', '1')
          };
        });

        const defaultOptions = {
          responsive: true,
          maintainAspectRatio: false,
          interaction: {
            intersect: false,
            mode: 'index'
          },
          animation: {
            duration: 750,
            easing: 'easeInOutQuart'
          },
          plugins: {
            legend: {
              display: showLegend,
              position: 'top',
              labels: {
                padding: 20,
                usePointStyle: true,
                font: {
                  size: 12,
                  weight: 500
                }
              }
            },
            tooltip: {
              enabled: true,
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              titleColor: '#1f2937',
              bodyColor: '#4b5563',
              borderColor: '#e5e7eb',
              borderWidth: 1,
              padding: 12,
              bodyFont: {
                size: 12
              },
              titleFont: {
                size: 14,
                weight: 'bold'
              },
              callbacks: {
                label: function(context: any) {
                  let label = context.dataset.label || '';
                  if (label) {
                    label += ': ';
                  }
                  if (context.parsed.y !== null) {
                    // Utiliser le formatteur personnalisé si disponible
                    if ('valueFormatter' in props && props.valueFormatter) {
                      label += props.valueFormatter(context.parsed.y);
                    } else {
                      // Ajouter le symbole % pour les taux en pourcentage
                      const suffix = label.toLowerCase().includes('taux') || 
                                    label.toLowerCase().includes('success') ? 
                                    '%' : '';
                      label += context.parsed.y.toFixed(1) + suffix;
                    }
                  }
                  return label;
                }
              }
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                padding: 10,
                font: {
                  size: 10
                },
                color: '#6b7280',
                autoSkip: true,
                maxRotation: 90,
                minRotation: 45,
                callback: function(val: any, index: number) {
                  const labels = formattedData.labels as string[];
                  return index % 3 === 0 ? labels[index] : '';
                }
              }
            },
            y: {
              beginAtZero: true,
              min: minValue !== undefined ? minValue : undefined, 
              max: maxValue !== undefined ? maxValue : undefined,
              grid: {
                color: 'rgba(0, 0, 0, 0.1)',
                drawTicks: false
              },
              ticks: {
                padding: 10,
                font: {
                  size: 12
                },
                color: '#6b7280',
                callback: function(value: any) {
                  // Utiliser le formatteur personnalisé si disponible
                  if ('valueFormatter' in props && props.valueFormatter) {
                    return props.valueFormatter(value);
                  }
                  // Formater les étiquettes de l'axe Y avec un % si nécessaire
                  return value + '%';
                },
                // Ajouter plus de graduations pour l'échelle de 0 à 100%
                stepSize: 20
              }
            },
            y1: {
              position: 'right',
              beginAtZero: true,
              min: 0,
              max: 100,
              grid: {
                display: false
              },
              ticks: {
                padding: 10,
                font: {
                  size: 12
                },
                color: '#000000',
                callback: function(value: any) {
                  if ('valueFormatter' in props && props.valueFormatter) {
                    return props.valueFormatter(value);
                  }
                  return value + '%';
                },
                // Ajouter plus de graduations pour l'échelle de 0 à 100%
                stepSize: 20
              }
            }
          }
        };

        const mergedOptions = options ? { ...defaultOptions, ...options } : defaultOptions;

        chartInstance.current = new Chart(ctx, {
          type: 'bar',
          data: {
            labels: formattedData.labels,
            datasets: formattedDatasets as ChartDataset<keyof ChartTypeRegistry, number[]>[]
          },
          options: mergedOptions
        });
      }
    }

    return () => {
      if (chartInstance.current) {
        chartInstance.current.destroy();
      }
    };
  }, [props]);

  return (
    <canvas
      ref={chartRef}
      width={width}
      height={height}
      className={className}
    />
  );
};

export default BarChart;

