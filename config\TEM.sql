-- Création de la base de données
CREATE DATABASE IF NOT EXISTS Kpi;
USE Kpi;

-- Création des tables

CREATE TEMPORARY TABLE IF NOT EXISTS Bc_voice_Mt (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User TEXT,
    User_Group TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId TEXT,
    CallConnect TEXT,
    CallDuration TEXT,
    CallRelease TEXT,
    Completed INT,
    Failure int,
    Incomplete int,
    ReceivedCLI VARCHAR(20),
    ServiceType text,
    Success INT,
    a_Ecno REAL,
    a_HappyEyeBAllSelectedIPVersion TEXT,
    a_IP_version text,
    a_IPv4_Used TEXT,
    a_IPv6_Used TEXT,
    a_LTE_RgAttachDuration text,
    a_LupDuration TEXT,
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_SpeechCodec TEXT,
    a_TAC TEXT,
    a_UsedPLMNNameShort TEXT,
    b_EcNo TEXT,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IPv4_Used TEXT,
    b_IP_version TEXT,
    b_IPv6_Used TEXT,
    b_LTE_RgAttachDuration TEXT,
    b_LupDuration TEXT,
    b_SimAuthenticationAfterAttach int,
    b_SimAuthenticationAfterLup int,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNo text,
    c_LTE_RgAttachDuration text,
    c_LupDuration TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNo text,
    d_LTE_RgAttachDuration text,
    d_LupDuration TEXT,
    d_UsedPLMNNameShort TEXT,
    ClIValidity_L3 TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 text,
    ReceivedClLi_L3 TEXT,
    TCDuration_L3 TEXT,
    TestId int,
    TestrunId int,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq REAL,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq text,
    c_LTE_Freq text,
    d_LTE_Freq text,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay int,
    MonthOfYear int,
    TimeZone text,
    WeekOfYear int,
    Distance text,
    ExecutionHost TEXT,
    ExecutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_CI INT,
    a_DCNR_restricted TEXT,
    a_Eclo REAL,
    a_LAC INT,
    a_LTE_Band text,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCl TEXT,
    a_NSSAI TEXT,
    a_RAC text,
    a_RSCP INT,
    a_RSRP text,
    a_RxLevel INT,
    a_SID TEXT,
    a_SIM_AuthenticationEnd TEXT,
    a_SIM_AuthenticationStart TEXT,
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi INT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples text,
    a_uuPppHsdpaUsed text,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl TEXT,
    b_DCNR_restricted TEXT,
    b_Eclo text,
    b_LAC text,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP text,
    b_RSRP TEXT,
    b_RxLevel text,
    b_SID TEXT,
    b_SIM_AuthenticationEnd TEXT,
    b_SIM_AuthenticationStart TEXT,
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi text,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed TEXT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo text,
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel text,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo text,
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel TEXT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset REAL,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid int,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId text


);





CREATE TEMPORARY TABLE IF NOT EXISTS Bc_volte_mo_mt (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User TEXT,
    User_Group TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId int,
    CallDropRate FLOAT,
    CallDuration DATETIME(3),
    Completed INT,
    Failure int,
    Incomplete int,
    ServiceType text,
    Success INT,
    a_EcNo FLOAT,
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_version TINYINT,
    a_IPv4_Used TINYINT,
    a_IPv6_Used TINYINT,
    a_LTE_RgAttachDuration DATETIME(3),
    a_LupDuration DATETIME(3),
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_TAC INT,
    a_UsedPLMNNameShort TEXT,
    avBchannelCheck TEXT,
    b_EcNo FLOAT,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_version TINYINT,
    b_IPv4_Used TINYINT,
    b_IPv6_Used TINYINT,
    b_LTE_RgAttachDuration DATETIME(3),
    b_LupDuration DATETIME(3),
    b_SimAuthenticationAfterAttach int,
    b_SimAuthenticationAfterLup int,
    b_TAC INT,
    b_UsedPLMNNameShort TEXT,
    c_EcNo FLOAT,
    c_LTE_RgAttachDuration DATETIME(3),
    c_LupDuration DATETIME(3),
    c_UsedPLMNNameShort TEXT,
    d_EcNo FLOAT,
    d_LTE_RgAttachDuration DATETIME(3),
    d_LupDuration DATETIME(3),
    d_UsedPLMNNameShort TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    TCDuration DATETIME(3),
    TestId INT,
    TestrunId INT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq FLOAT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq float,
    c_LTE_Freq float,
    d_LTE_Freq float,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone FLOAT,
    WeekOfYear INT,
    Distance FLOAT,
    ExecutionHost TEXT,
    ExecutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_Cl INT,
    a_DCNR_restricted TEXT,
    a_Eclo FLOAT,
    a_LAC INT,
    a_LTE_Band INT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC INT,
    a_RSCP INT,
    a_RSRP INT,
    a_RxLevel INT,
    a_SID TEXT,
    a_SIM_AuthenticationEnd datetime(3),
    a_SIM_AuthenticationStart datetime(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi BIGINT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples INT,
    a_uuPppHsdpaUsed INT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl TEXT,
    b_DCNR_restricted TEXT,
    b_Eclo FLOAT,
    b_LAC INT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP INT,
    b_RSRP TEXT,
    b_RxLevel INT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd datetime(3),
    b_SIM_AuthenticationStart datetime(3),
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi BIGINT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed INT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo FLOAT,
    c_LAC INT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel INT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi BIGINT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo FLOAT,
    d_LAC INT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel INT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi BIGINT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset FLOAT,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid INT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId int
);

CREATE TEMPORARY TABLE IF NOT EXISTS HTTP_ps (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User TEXT,
    User_Group TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId int NULL,
    Completed INT,
    DataDownloadDuration text ,
    DnsDuration time(3),
    DownloadContentSize INT,
    DownloadRate real,
    DownloadURL TEXT,
    DownloadedContentSize INT,
    DownloadingDuration time(3),
    DownloadingRate real,
    Failure int,
    Incomplete int,
    LocallpAddress TEXT,
    ServiceType text,
    Success INT,
    TimeToFirstByte TIME(3),
    TotalKBPSecDownload real,
    TotalLoadTime TIME(3),
    a_AttachSuccessRatio Int,
    a_Cellld2 TEXT,
    a_Dialup_Success text,
    a_EcNO real,
    a_FirstData_Success INT,
    a_GPRSAttachDuration time(3),
    a_GPRS_APN TEXT,
    a_GPRSattach_Success INT,
    a_HappyEyeBAllSelectedIPVersion TEXT,
    a_IP_version text,
    a_IPv4_Used INT,
    a_IPv6_Used INT,
    a_LTE_RgAttachDuration time(3) NULL,
    a_LastData_Success INT,
    a_LupDuration TIME(3),
    a_PDPContextActivationDuration TIME(3),
    a_PDPIpUpDuration TIME(3),
    a_PDPcontACT_Success INT,
    a_Session_Success INT,
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_TAC INT null,
    a_TcpHandshakeDuration TIME(3),
    a_UsedPLMNNameShort TEXT,
    a_acessType TEXT,
    b_EcNO TEXT,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_version TEXT,
    b_IPv4_Used TEXT,
    b_IPv6_Used TEXT,
    b_LTE_RgAttachDuration  TEXT,
    b_LupDuration TEXT,
    b_SimAuthenticationAfterAttach int,
    b_SimAuthenticationAfterLup int,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNO TEXT,
    c_LTE_RgAttachDuration TEXT,
    c_LupDuration TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNO TEXT,
    d_LTE_RgAttachDuration TEXT,
    d_LupDuration TEXT,
    d_UsedPLMNNameShort TEXT,
    totalBytes INT,
    CA_RRC_Configured_L3 INT,
    CA_SSC_Band_1_L3 INT NULL,
    CA_SSC_Band_2_L3 TEXT ,
    CA_SSC_BW_1_L3 real NULL,
    CA_SSC_BW_2_L3 real NULL,
    CA_SSC_Freq_1_L3 real NULL,
    CA_SSC_Freq_2_L3 real NULL,
    CA_SSC_RSRP_1 INT,
    CA_SSC_RSRP_2 INT,
    CauseText_L3 TEXT,
    CauseValue_L3 TEXT, 
    TCDuration TIME(3),
    TestId INT,
    TestrunId INT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_AttachDuration_L3 TIME(3),
    a_AttachSuccessRatio_L3 INT,
    a_LTE_Freq REAL NULL,
    a_PDPCADuration_L3 Time(3),
    a_PDPCASuccessRatio_L3 INT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq REAL NULL,
    c_LTE_Freq REAL NULL,
    d_LTE_Freq REAL NULL,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance TEXT,
    ExecutionHost TEXT,
    ExecutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_Cl INT,
    a_DCNR_restricted TEXT,
    a_Eclo real NULL,
    a_LAC INT NULL,
    a_LTE_Band INT NULL ,
    a_LTE_Bandwidth real NULL,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC INT NULL,
    a_RSCP INT NULL ,
    a_RSRP INT NULL,
    a_RxLevel INT NULL ,
    a_SID TEXT,
    a_SIM_Authentication datetime(3),
    a_SIM_AuthenticationStart datetime(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples TEXT,
    a_uuPppHsdpaUsed TEXT,
    a_uuPppHsupaUsed text,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl TEXT,
    b_DCNR_restricted TEXT,
    b_Eclo TEXT,
    b_LAC TEXT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP TEXT,
    b_RSRP TEXT,
    b_RxLevel TEXT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd TEXT,
    b_SIM_AuthenticationStart TEXT,
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed TEXT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo FLOAT,
    c_LAC INT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel TEXT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo TEXT,
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel TEXT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset REAL NULL,
    uuCqiAverage TEXT ,
    uuCqiSamples TEXT,
    uuPppHsdpaUsed TEXT,
    uuPppHsupaUsed TEXT,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid INT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId TEXT

);

CREATE TEMPORARY TABLE IF NOT EXISTS http_download_20 (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User TEXT,
    User_Group TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId int,
    Completed INT,
    DataDownloadDuration time(3),
    DataDownloadDurationApp TIME(3),
    DnsDuration TIME(3),
    DnsDurationApp TIME(3),
    DnsOverHttpsUsed  INT,
    DownloadDuration TIME(3),
    DownloadDurationApp TIME(3),
    DownloadedContentSize INT,
    DownloadedContentSizeApp INT,
    DownloadingRate real NULL,
    DownloadingRateApp text,
    Failure int,
    HTTPMeanDataRate real NULL,
    HTTPMeanDataRateApp TEXT,
    HTTP_RTT TIME(3),
    HTTP_RTT_App TIME(3),
    HappyEyeBalls_Delay INT,
    Incomplete int,
    LocallpAddress TEXT,
    NumberOfTcpConnections INT NULL,
    PageContent INT NULL,
    PageContentApp INT NULL,
    ServiceType text,
    Success INT,
    SumOfBytes INT NULL,
    SumOfBytesReceived INT NULL,
    SumOfBytesSent INT NULL,
    TotalKBPerSecDownload REAL NULL,
    TotalKBPerSecDownloadApp REAL NULL,
    TotalLoadTime TIME(3) NULL,
    URL TEXT,
    a_AttachSuccessRatio INT NULL,
    a_Cellld2 TEXT,
    a_EcNO REAL NULL,
    a_GPRSAttachDuration TIME(3),
    a_GPRS_APN TEXT,
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_version Text,
    a_IPv4_Used INT NULL,
    a_IPv6_Used INT NULL,
    a_LTE_RgAttachDuration TIME(3) NULL,
    a_LupDuration text,
    a_PDPContextActivationDuration TIME(3) NULL,
    a_PDPIpUpDuration TIME(3) NULL,
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_TAC INT,
    a_TcpHandshakeDuration TIME(3) NULL,
    a_UsedPLMNNameShort TEXT,
    a_acessType TEXT,
    b_EcNO INT NULL,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_version TEXT,
    b_IPv4_Used TEXT,
    b_IPv6_Used TEXT,
    b_LTE_RgAttachDuration TEXT,
    b_LupDuration text,
    b_SimAuthenticationAfterAttach INT ,
    b_SimAuthenticationAfterLup INT ,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNO TEXT,
    c_LTE_RgAttachDuration TEXT,
    c_LupDuration TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNO TEXT,
    d_LTE_RgAttachDuration TEXT,
    d_LupDuration TEXT,
    d_UsedPLMNNameShort TEXT NULL,
    APN_AMBR_dl INT NULL,
    APN_AMBR_ul INT NULL,
    CA_RRC_Configured_L3 INT,
    CA_SSC_Band_1_L3 INT NULL,
    CA_SSC_Band_2_L3 TEXT,
    CA_SSC_BW_1_L3 real null,
    CA_SSC_BW_2_L3 REAL NULL,
    CA_SSC_Freq_1_L3 REAL NULL,
    CA_SSC_Freq_2_L3 REAL NULL,
    CA_SSC_RSRP_1 INT NULL,
    CA_SSC_RSRP_2 INT NULL,
    CauseText_L3 TEXT,
    CauseValue_L3 INT NULL,
    GBR_dl_dedicated TEXT,
    GBR_dl_negotiated TEXT,
    GBR_dl_requested Text,
    GBR_ul_dedicated TEXT,
    GBR_ul_negotiated TEXT,
    GBR_ul_requested TEXT,
    MBR_dl_dedicated TEXT,
    MBR_dl_negotiated TEXT,
    MBR_dl_requested TEXT,
    MBR_ul_dedicated TEXT,
    MBR_ul_negotiated TEXT,
    MBR_ul_requested TEXT,
    QCI_dedicated TEXT,
    QCI_default INT NULL,
    TCDuration TIME(3),
    TestId INT,
    TestrunId INT,
    TrafficClass_negotiated TEXT,
    TrafficClass_requested TEXT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_AttachDuration_L3 TIME(3),
    a_AttachSuccessRatio_L3 INT NULL,
    a_LTE_Freq REAL NULL,
    a_PDPCADuration_L3 TEXT,
    a_PDPCASuccessRatio_L3 TEXT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq TEXT,
    c_LTE_Freq TEXT,
    d_LTE_Freq TEXT,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance FLOAT,
    ExecutionHost TEXT,
    ExecutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_Cl INT NULL,
    a_DCNR_restricted TEXT,
    a_Eclo TEXT,
    a_LAC INT NULL,
    a_LTE_Band INT NULL,
    a_LTE_Bandwidth REAL NULL,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC TEXT,
    a_RSCP TEXT,
    a_RSRP INT NULL,
    a_RxLevel INT NULL,
    a_SID TEXT,
    a_SIM_AuthenticationEnd datetime(3),
    a_SIM_AuthenticationStart datetime(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples TEXT,
    a_uuPppHsdpaUsed TEXT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl TEXT,
    b_DCNR_restricted TEXT,
    b_Eclo TEXT,
    b_LAC TEXT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP INT,
    b_RSRP TEXT,
    b_RxLevel TEXT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd TEXT ,
    b_SIM_AuthenticationStart TEXT,
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed INT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo TEXT,
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel TEXT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo TEXT,
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel TEXT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset REAL NULL,
    uuCqiAverage TEXT,
    uuCqiSamples TEXT,
    uuPppHsdpaUsed TEXT,
    uuPppHsupaUsed TEXT,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid INT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId TEXT
);

CREATE TEMPORARY TABLE IF NOT EXISTS sms_mo_mt (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User TEXT,
    User_Group TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId int,
    Completed INT,
    DeliveryReportStatus TEXT,
    Failure int,
    Fake_DeliveryReport INT,
    Incomplete int,
    SMSC_Number TEXT,
    SMSC_Received_OK INT NULL,
    SMSEnd2EndDuration TIME(3) NULL,
    SMS_And_DeliveryReport_OK INT NULL,
    SMS_Contents_OK INT NULL,
    SMS_Originator TEXT,
    SMS_Originator_OK INT NULL,
    SMSdeliverDuration TIME(3),
    SMSsendDuration TIME(3) NULL,
    ServiceType text,
    Success INT,
    a_EcNo REAL NULL,
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_version TEXT,
    a_IPv4_Used TEXT,
    a_IPv6_Used TEXT,
    a_LTE_RgAttachDuration TEXT,
    a_LupDuration TIME(3),
    a_SMS_ConcatParts INT,
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_TAC TEXT,
    a_UsedPLMNNameShort TEXT,
    b_EcNo REAL NULL,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_version TEXT,
    b_IPv4_Used TEXT,
    b_IPv6_Used TEXT,
    b_LTE_RgAttachDuration TEXT,
    b_LupDuration TIME(3),
    b_SMS_ConcatParts INT NULL,
    b_SimAuthenticationAfterAttach int,
    b_SimAuthenticationAfterLup int,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNo TEXT,
    c_LTE_RgAttachDuration TEXT,
    c_LupDuration TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNo TEXT,
    d_LTE_RgAttachDuration TEXT,
    d_LupDuration TEXT,
    d_UsedPLMNNameShort TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    SMSEnd2EndDuration_L3 TIME(3) NULL,
    SMSsendDuration_L3 TIME(3) NULL,
    TCDuration TIME(3),
    TestId INT,
    TestrunId INT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq REAL,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq REAL,
    c_LTE_Freq TEXT,
    d_LTE_Freq TEXT,
    Delivery_SMSC TEXT,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance TEXT,
    ExecutionHost TEXT,
    ExecutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_Cl INT,
    a_DCNR_restricted TEXT,
    a_Eclo REAL NULL,
    a_LAC INT,
    a_LTE_Band INT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC TEXT,
    a_RSCP INT NULL,
    a_RSRP TEXT,
    a_RxLevel INT,
    a_SID TEXT,
    a_SIM_AuthenticationEnd datetime(3),
    a_SIM_AuthenticationStart datetime(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples TEXT,
    a_uuPppHsdpaUsed TEXT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl INT,
    b_DCNR_restricted TEXT,
    b_Eclo REAL ,
    b_LAC INT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP INT,
    b_RSRP TEXT,
    b_RxLevel INT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd datetime(3),
    b_SIM_AuthenticationStart datetime(3),
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed INT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo TEXT,
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel INT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi BIGINT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo FLOAT,
    d_LAC INT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel INT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset REAL,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid int,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId text
);

CREATE TEMPORARY TABLE IF NOT EXISTS SteeringOfRoaming (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId BIGINT,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict TEXT,
    TestDefinitionPath TEXT,
    User TEXT,
    UserGroup TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry TEXT,
    errorSideId INT,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId INT,
    Completed TINYINT,
    Failure TINYINT,
    Incomplete TINYINT,
    L3_Flag TEXT,
    ServiceType TEXT,
    Success TINYINT,
    a_EcNO DECIMAL(5,1),
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_Version TEXT,
    a_IPv4_Used TEXT,
    a_IPv6_Used TEXT,
    a_InitialLupReqTime DATETIME(3),
    a_LTE_RgAttachDuration TEXT,
    a_LupAcceptDuration TIME(3),
    a_LupAcceptDuration_All_VPLMN TIME(3),
    a_LupDuration TIME(3),
    a_LupMode TEXT,
    a_LupRejectDuration TIME(3),
    a_NrOfLupRejects_All_VPLMN INT,
    a_NrOfLupRequests INT,
    a_NrOfLupRequests_All_VPLMN INT,
    a_NrOfPlmnsRejected INT,
    a_OverallNrOfLupRequests INT,
    a_RejectCauses TEXT,
    a_RejectedPLMNs TEXT,
    a_SimAuthenticationAfterAttach TINYINT,
    a_SimAuthenticationAfterLup TINYINT,
    a_TAC TEXT,
    a_UsedPLMNNameShort TEXT,
    a_VPLMN_registered TEXT,
    b_EcNO DECIMAL(5,1),
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_Version TEXT,
    b_IPv4_Used TEXT,
    b_IPv6_Used TEXT,
    b_InitialLupReqTime DATETIME(3),
    b_LTE_RgAttachDuration TEXT,
    b_LupAcceptDuration TIME(3),
    b_LupDuration TIME(3),
    b_LupMode TEXT,
    b_LupRejectDuration TIME(3),
    b_NrOfLupRequests INT,
    b_NrOfPlmnsRejected INT,
    b_OverallNrOfLupRequests INT,
    b_RejectCauses TEXT,
    b_RejectedPLMNs TEXT,
    b_SimAuthenticationAfterAttach TINYINT,
    b_SimAuthenticationAfterLup TINYINT,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNO DECIMAL(5,1),
    c_InitialLupReqTime DATETIME(3),
    c_LTE_RgAttachDuration TEXT,
    c_LupAcceptDuration TIME(3),
    c_LupDuration TIME(3),
    c_LupMode TEXT,
    c_LupRejectDuration TIME(3),
    c_NrOfLupRequests INT,
    c_NrOfPlmnsRejected INT,
    c_OverallNrOfLupRequests INT,
    c_RejectCauses TEXT,
    c_RejectedPLMNs TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNO DECIMAL(5,1),
    d_InitialLupReqTime DATETIME(3),
    d_LTE_RgAttachDuration TEXT,
    d_LupAcceptDuration TIME(3),
    d_LupDuration TIME(3),
    d_LupMode TEXT,
    d_LupRejectDuration TIME(3),
    d_NrOfLupRequests INT,
    d_NrOfPlmnsRejected INT,
    d_OverallNrOfLupRequests INT,
    d_RejectCauses TEXT,
    d_RejectedPLMNs TEXT,
    d_UsedPLMNNameShort TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    TCDuration TIME(3),
    TestId BIGINT,
    TestrunId BIGINT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq TEXT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq TEXT,
    c_LTE_Freq TEXT,
    d_LTE_Freq TEXT,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance TEXT,
    ExecutionHost TEXT,
    ExecutionId BIGINT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_CI TEXT,
    a_DCNR_restricted TEXT,
    a_EcIo DECIMAL(5,1),
    a_LAC TEXT,
    a_LTE_Band TEXT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP DECIMAL(5,1),
    a_NR_RSRQ DECIMAL(5,1),
    a_NR_SINR DECIMAL(5,1),
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC TEXT,
    a_RSCP DECIMAL(5,1),
    a_RSRP DECIMAL(5,1),
    a_RxLevel DECIMAL(5,1),
    a_SID TEXT,
    a_SIM_AuthenticationEnd DATETIME(3),
    a_SIM_AuthenticationStart DATETIME(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples TEXT,
    a_uuPppHsdpaUsed TEXT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_CI TEXT,
    b_DCNR_restricted TEXT,
    b_EcIo DECIMAL(5,1),
    b_LAC TEXT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP DECIMAL(5,1),
    b_NR_RSRQ DECIMAL(5,1),
    b_NR_SINR DECIMAL(5,1),
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP DECIMAL(5,1),
    b_RSRP DECIMAL(5,1),
    b_RxLevel DECIMAL(5,1),
    b_SID TEXT,
    b_SIM_AuthenticationEnd DATETIME(3),
    b_SIM_AuthenticationStart DATETIME(3),
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed TEXT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_CI TEXT,
    c_DCNR_restricted TEXT,
    c_EcIo DECIMAL(5,1),
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP DECIMAL(5,1),
    c_NR_RSRQ DECIMAL(5,1),
    c_NR_SINR DECIMAL(5,1),
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP DECIMAL(5,1),
    c_RSRP DECIMAL(5,1),
    c_RxLevel DECIMAL(5,1),
    c_SID TEXT,
    c_SIM_AuthenticationEnd DATETIME(3),
    c_SIM_AuthenticationStart DATETIME(3),
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_CI TEXT,
    d_DCNR_restricted TEXT,
    d_EcIo DECIMAL(5,1),
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP DECIMAL(5,1),
    d_NR_RSRQ DECIMAL(5,1),
    d_NR_SINR DECIMAL(5,1),
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP DECIMAL(5,1),
    d_RSRP DECIMAL(5,1),
    d_RxLevel DECIMAL(5,1),
    d_SID TEXT,
    d_SIM_AuthenticationEnd DATETIME(3),
    d_SIM_AuthenticationStart DATETIME(3),
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset DECIMAL(5,1),
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid TINYINT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId BIGINT,
    InsertId BIGINT
);

CREATE TEMPORARY TABLE IF NOT EXISTS Voice_KPI (
    TestcaseId INT AUTO_INCREMENT PRIMARY KEY,
    OrderId int,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(20),
    TestDefinitionPath TEXT,
    User TEXT,
    UserGroup TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId int,
    errorSideCountry TEXT,
    errorSideId int,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId int,
    Completed INT,
    Failure int,
    Incomplete int,
    LocAudioFile TEXT,
    ServiceType TEXT,
    Success INT,
    a_EcNo FLOAT,
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_version TINYINT,
    a_IPv4_Used TINYINT,
    a_IPv6_Used TINYINT,
    a_LTE_RgAttachDuration DATETIME(3),
    a_LupDuration DATETIME(3),
    a_SimAuthenticationAfterAttach int,
    a_SimAuthenticationAfterLup int,
    a_TAC INT,
    a_UsedPLMNNameShort TEXT,
    b_EcNo float,
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_version TINYINT,
    b_IPv4_Used TINYINT,
    b_IPv6_Used TINYINT,
    b_LTE_RgAttachDuration DATETIME(3),
    b_LupDuration DATETIME(3),
    b_SimAuthenticationAfterAttach int,
    b_SimAuthenticationAfterLup int,
    b_TAC INT,
    b_UsedPLMNNameShort TEXT,
    c_EcNo FLOAT,
    c_LTE_RgAttachDuration DATETIME(3),
    c_LupDuration DATETIME(3),
    c_UsedPLMNNameShort TEXT,
    d_EcNo FLOAT,
    d_LTE_RgAttachDuration DATETIME(3),
    d_LupDuration DATETIME(3),
    d_UsedPLMNNameShort TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    TCDuration DATETIME(3),
    TestId INT,
    TestrunId INT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq FLOAT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq float,
    c_LTE_Freq float,
    d_LTE_Freq float,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone FLOAT,
    WeekOfYear INT,
    Distance FLOAT,
    ExexutionHost TEXT,
    ExexutionId INT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_Cl INT,
    a_DCNR_restricted TEXT,
    a_Eclo FLOAT,
    a_LAC INT,
    a_LTE_Band INT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP TEXT,
    a_NR_RSRQ TEXT,
    a_NR_SINR TEXT,
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC INT,
    a_RSCP INT,
    a_RSRP INT,
    a_RxLevel INT,
    a_SID TEXT,
    a_SIM_AuthenticationEnd datetime,
    a_SIM_AuthenticationStart datetime,
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi BIGINT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples INT,
    a_uuPppHsdpaUsed INT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_Cl TEXT,
    b_DCNR_restricted TEXT,
    b_Eclo FLOAT,
    b_LAC INT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP TEXT,
    b_NR_RSRQ TEXT,
    b_NR_SINR TEXT,
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP INT,
    b_RSRP TEXT,
    b_RxLevel INT,
    b_SID TEXT,
    b_SIM_AuthenticationEnd datetime,
    b_SIM_AuthenticationStart datetime,
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi BIGINT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed INT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_Cl TEXT,
    c_DCNR_restricted TEXT,
    c_Eclo FLOAT,
    c_LAC INT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP TEXT,
    c_NR_RSRQ TEXT,
    c_NR_SINR TEXT,
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP TEXT,
    c_RSRP TEXT,
    c_RxLevel INT,
    c_SID TEXT,
    c_SIM_AuthenticationEnd TEXT,
    c_SIM_AuthenticationStart TEXT,
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi BIGINT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_Cl TEXT,
    d_DCNR_restricted TEXT,
    d_Eclo FLOAT,
    d_LAC INT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP TEXT,
    d_NR_RSRQ TEXT,
    d_NR_SINR TEXT,
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP TEXT,
    d_RSRP TEXT,
    d_RxLevel INT,
    d_SID TEXT,
    d_SIM_AuthenticationEnd TEXT,
    d_SIM_AuthenticationStart TEXT,
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi BIGINT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset FLOAT,
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid int,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId INT,
    InsertId BIGINT
);
