import { BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, Title, Tooltip } from 'chart.js';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import DynamicFilter from '../components/DynamicFilter';
import FailureDetailsTable from '../components/FailureDetailsTable';
import { getAttachmentRatesByCountry, getAvailableCountriesAndOperators, getOperatorsByCountry } from '../services/kpiService';

// Enregistrer les composants de Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

// URL de base de l'API
const API_BASE_URL = 'http://localhost:8000/api';

// Type pour les données des filtres
interface FilterState {
  country: string;
  operator: string;
  verdict: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  searchQuery: string;
}

// Type pour les données de steering chart
interface SteeringChartData {
  country: string;
  success_rate: number;
  total_attempts: number;
  failures: number;
}

// Fonction pour obtenir la couleur du KPI selon la valeur
const getKpiColor = (value: number): string => {
  if (value > 90) return '#10b981'; // Vert (emerald-500) si KPI > 90%
  if (value > 75) return '#f59e0b'; // Orange (amber-500) si 75% < KPI < 90%
  return '#ef4444';                 // Rouge (red-500) si KPI < 75%
};

const SteeringDashboardPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [steeringChartData, setSteeringChartData] = useState<any>(null);
  const [attachmentChartData, setAttachmentChartData] = useState<any>(null);
  const [availableCountries, setAvailableCountries] = useState<Array<{id: string, name: string}>>([]);
  const [availableOperators, setAvailableOperators] = useState<Array<{id: string, name: string}>>([]);
  const [filters, setFilters] = useState<FilterState>({
    country: '',
    operator: '',
    verdict: '',
    dateRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    searchQuery: ''
  });
  const [attachmentKpi, setAttachmentKpi] = useState<any>(null);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Charger les données de steering et d'attachement
  const loadSteeringData = async () => {
    setIsLoading(true);
    setLoadingError(null);
    
    try {
      console.log('Chargement des données de steering avec filtres:', filters);
      
      // 1. Récupérer les données de taux de succès depuis /api/steering_chart_data
      const successParams = new URLSearchParams();
      if (filters.country && filters.country !== 'all') {
        successParams.append('country', filters.country);
      }
      if (filters.operator && filters.operator !== 'all') {
        successParams.append('operator', filters.operator);
      }
      if (filters.dateRange && filters.dateRange.startDate) {
        successParams.append('start_date', filters.dateRange.startDate);
      }
      if (filters.dateRange && filters.dateRange.endDate) {
        successParams.append('end_date', filters.dateRange.endDate);
      }
      
      const successQueryString = successParams.toString();
      const successUrl = `${API_BASE_URL}/steering_chart_data${successQueryString ? `?${successQueryString}` : ''}`;
      
      console.log('URL pour les données de taux de succès:', successUrl);
      const successResponse = await fetch(successUrl);
      
      if (!successResponse.ok) {
        throw new Error(`Erreur HTTP: ${successResponse.status}`);
      }
      
      const successResult = await successResponse.json();
      if (!successResult.success || !Array.isArray(successResult.data)) {
        throw new Error(successResult.message || 'Données de succès invalides.');
      }
      
      console.log('Données de succès reçues:', successResult.data);
      
      // Trier les données par taux de succès décroissant
      const sortedSuccessData = successResult.data.sort((a: SteeringChartData, b: SteeringChartData) => 
        b.success_rate - a.success_rate
      );
      
      // Créer l'ordre des pays basé sur le tri
      const countryOrder = sortedSuccessData.map((item: SteeringChartData) => item.country);
      
      // Formater les données pour le graphique de taux de succès
      const successChartDataPayload = {
        labels: countryOrder,
        datasets: [{
          label: 'Taux de succès',
          data: sortedSuccessData.map((item: SteeringChartData) => item.success_rate),
          backgroundColor: sortedSuccessData.map((item: SteeringChartData) => getKpiColor(item.success_rate)),
        }],
      };
      
      setSteeringChartData(successChartDataPayload);
      
      // 2. Récupérer les données d'attachement depuis /api/attachment_rates_by_country
      const attachmentResponse = await getAttachmentRatesByCountry(
        filters.country !== 'all' ? filters.country : undefined,
        filters.operator !== 'all' ? filters.operator : undefined,
        filters.dateRange.startDate,
        filters.dateRange.endDate
      );
      
      console.log('Données d\'attachement reçues:', attachmentResponse);
      
      if (!attachmentResponse || !attachmentResponse.success || !attachmentResponse.data) {
        throw new Error(attachmentResponse.message || "Aucune donnée d'attachement reçue.");
      }
      
      let attachmentData = attachmentResponse.data;
      
      // Filtrer les données d'attachement si un pays est sélectionné
      if (filters.country && filters.country !== 'all') {
        attachmentData = attachmentData.filter(item => item.country === filters.country);
      }
      
      // Trier les données d'attachement selon l'ordre des pays du premier graphique
      const sortedAttachmentData = countryOrder
        .map((country: string) => attachmentData.find((item: any) => item.country === country))
        .filter((item: any) => !!item);
      
      console.log('Données d\'attachement triées:', sortedAttachmentData);
      
      // Sauvegarder les KPIs d'attachement si disponibles
      if (attachmentResponse.kpi) {
        setAttachmentKpi(attachmentResponse.kpi);
      }
      
      // Définir les labels et couleurs pour les niveaux d'attachement
      const attachmentLabels = ['1 ère attach', '2 ème attach', '3 ème attach', '4 ème attach', '5 ème attach', '6 ème attach', '7 ème attach et plus'];
      const attachmentColors = ['#0EB605FF', '#FFD700', '#80513BFF', '#f59e0b', '#3b82f6', '#1f2937', '#ef4444'];
      
      // Formater les données pour le graphique d'attachement
      const stackedChartDataPayload = {
        labels: sortedAttachmentData.map((d: any) => d.country),
        datasets: attachmentLabels.map((label, index) => ({
          label: label,
          data: sortedAttachmentData.map((d: any) => {
            const dist = d.attachment_distribution;
            return (dist && typeof dist === 'object' && typeof dist[String(index)] === 'number') ? dist[String(index)] : 0;
          }),
          backgroundColor: attachmentColors[index],
          borderColor: attachmentColors[index],
          borderWidth: 1,
        })),
      };
      
      setAttachmentChartData(stackedChartDataPayload);
      
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoadingError(error instanceof Error ? error.message : "Erreur inconnue");
      setSteeringChartData(null);
      setAttachmentChartData(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les filtres disponibles
  const fetchAvailableFilters = async () => {
    try {
      const { success, countries, operators, message } = await getAvailableCountriesAndOperators();
      
      if (!success) {
        throw new Error(message || 'Erreur lors de la récupération des filtres');
      }
      
      // Transformation des données pour qu'elles correspondent au format attendu par DynamicFilter
      // L'API retourne { value: string, label: string }
      // DynamicFilter attend { id: string, name: string }
      console.log("Pays récupérés de l'API:", countries);
      console.log("Opérateurs récupérés de l'API:", operators);
      
      // Transformation des données pour qu'elles correspondent au format attendu par DynamicFilter
      const countryOptions = countries.map((country: any) => ({
        id: country.id ?? country.value ?? country.label ?? country.country ?? '',
        name: country.name ?? country.label ?? country.value ?? country.country ?? ''
      }));
      
      const operatorOptions = operators.map((operator: any) => ({
        id: operator.id ?? operator.value ?? operator.label ?? operator.operator ?? '',
        name: operator.name ?? operator.label ?? operator.value ?? operator.operator ?? ''
      }));
      
      console.log("Pays transformés:", countryOptions);
      console.log("Opérateurs transformés:", operatorOptions);
      
      setAvailableCountries(countryOptions);
      setAvailableOperators(operatorOptions);
      
    } catch (error) {
      console.error('Erreur lors du chargement des filtres:', error);
      setAvailableCountries([]);
      setAvailableOperators([]);
    }
  };

  // Gérer les changements de filtres
  const handleFilterChange = (newFilters: FilterState) => {
    setFilters(newFilters);
    // Recharger les données avec les nouveaux filtres
    loadSteeringData();
  };

  // Charger les données au montage du composant
  useEffect(() => {
    console.log("Initialisation du composant SteeringDashboardPage");
    
    // Charger d'abord les filtres, puis les données
    const initializeData = async () => {
      try {
        console.log("Chargement des filtres disponibles...");
        
        // Récupérer les pays et opérateurs
        const filtersResponse = await getAvailableCountriesAndOperators();
        console.log("Filtres récupérés:", filtersResponse);
        
        if (filtersResponse.success) {
          console.log(`Pays disponibles: ${filtersResponse.countries.length}, Opérateurs disponibles: ${filtersResponse.operators.length}`);
          setAvailableCountries(filtersResponse.countries);
          setAvailableOperators(filtersResponse.operators);
          
          // Une fois les filtres chargés, charger les données
          console.log("Chargement des données de steering...");
          await loadSteeringData();
        } else {
          console.error("Erreur lors du chargement des filtres:", filtersResponse.message);
          setLoadingError(`Erreur lors du chargement des filtres: ${filtersResponse.message}`);
        }
      } catch (error) {
        console.error("Erreur lors de l'initialisation des données:", error);
        setLoadingError(`Erreur lors de l'initialisation: ${error instanceof Error ? error.message : "Erreur inconnue"}`);
      } finally {
        setIsLoading(false);
      }
    };
    
    initializeData();
  }, []);

  // Mettre à jour la liste des opérateurs lorsque le pays change
  useEffect(() => {
    const updateOperators = async () => {
      try {
        if (filters.country && filters.country !== 'all') {
          const opsByCountry = await getOperatorsByCountry(filters.country);
          setAvailableOperators(opsByCountry);
        } else {
          // Tous les pays -> récupérer tous les opérateurs
          const { operators } = await getAvailableCountriesAndOperators();
          const normalized = operators.map((op: any) => ({
            id: op.id ?? op.value ?? op.operator ?? '',
            name: op.name ?? op.label ?? op.value ?? ''
          }));
          setAvailableOperators(normalized);
        }
      } catch (err) {
        console.error('Erreur lors de la mise à jour des opérateurs:', err);
      }
    };

    updateOperators();
  }, [filters.country]);

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Tableau de Bord - Steering of Roaming</h1>
      
      {/* Section des filtres */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtres</h2>
        <DynamicFilter
          countries={availableCountries}
          operators={availableOperators}
          onFilterChange={handleFilterChange}
          initialValues={{
            startDate: filters.dateRange.startDate,
            endDate: filters.dateRange.endDate
          }}
        />
      </div>
      
      {/* Message d'erreur si nécessaire */}
      {loadingError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6" role="alert">
          <strong className="font-bold">Erreur:</strong>
          <span className="block sm:inline"> {loadingError}</span>
        </div>
      )}

      {/* Section des graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6 mb-6">
        {/* Graphique de taux de succès de steering */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-lg font-semibold mb-4">Taux de Succès de Steering par Pays</h2>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          ) : steeringChartData ? (
            <div className="h-96">
              <Bar data={steeringChartData} options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top' as const,
                  },
                  title: {
                    display: true,
                    text: 'Taux de Succès (%)'
                  }
                },
                scales: {
                  y: {
                    beginAtZero: true,
                    max: 100,
                    title: {
                      display: true,
                      text: 'Taux de Succès (%)'
                    }
                  },
                  x: {
                    title: {
                      display: true,
                      text: 'Pays'
                    }
                  }
                }
              }} />
            </div>
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500">
              Aucune donnée disponible
            </div>
          )}
        </div>

        {/* Graphique de distribution des niveaux d'attachement */}
        <div className="bg-white rounded-lg shadow-md p-4">
          <h2 className="text-lg font-semibold mb-4">Distribution des Niveaux d'Attachement par Pays</h2>
          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
            </div>
          ) : attachmentChartData ? (
            <div className="h-96">
              <Bar data={attachmentChartData} options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top' as const,
                  },
                  title: {
                    display: true,
                    text: 'Distribution des Niveaux d\'Attachement'
                  },
                  tooltip: {
                    callbacks: {
                      label: function(context) {
                        const label = context.dataset.label || '';
                        const value = context.parsed.y || 0;
                        return `${label}: ${value}`;
                      }
                    }
                  }
                },
                scales: {
                  x: {
                    stacked: true,
                    title: {
                      display: true,
                      text: 'Pays'
                    }
                  },
                  y: {
                    stacked: true,
                    title: {
                      display: true,
                      text: 'Nombre d\'Attachements'
                    }
                  }
                }
              }} />
            </div>
          ) : (
            <div className="flex justify-center items-center h-64 text-gray-500">
              Aucune donnée disponible
            </div>
          )}
        </div>
      </div>

      {/* Section des statistiques générales */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-blue-800">Nombre Total de Tentatives</h3>
          <p className="text-2xl font-bold text-blue-600">
            {isLoading ? '...' : steeringChartData ? 
              steeringChartData.datasets[0].data.reduce((sum: number, val: number) => sum + val, 0).toLocaleString() : 
              '0'}
          </p>
        </div>
        <div className="bg-green-50 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-green-800">Taux de Succès Global</h3>
          <p className="text-2xl font-bold text-green-600">
            {isLoading ? '...' : steeringChartData && steeringChartData.datasets[0].data.length > 0 ? 
              (steeringChartData.datasets[0].data.reduce((sum: number, val: number) => sum + val, 0) / 
               steeringChartData.datasets[0].data.length).toFixed(1) + '%' : 
              '0%'}
          </p>
        </div>
        <div className="bg-yellow-50 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-yellow-800">Pays Analysés</h3>
          <p className="text-2xl font-bold text-yellow-600">
            {isLoading ? '...' : steeringChartData ? steeringChartData.labels.length : '0'}
          </p>
        </div>
        <div className="bg-purple-50 p-4 rounded-lg shadow">
          <h3 className="text-sm font-medium text-purple-800">Opérateurs</h3>
          <p className="text-2xl font-bold text-purple-600">
            {isLoading ? '...' : availableOperators.length}
          </p>
        </div>
      </div>

      {/* Table des détails des échecs */}
      <FailureDetailsTable 
        country={filters.country} 
        operator={filters.operator} 
        verdict={filters.verdict === 'fail' ? 'fail' : ''} 
        show={filters.verdict === 'fail'}
      />
    </div>
  );
};

export default SteeringDashboardPage;