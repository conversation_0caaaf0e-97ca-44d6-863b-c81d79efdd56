import axios from 'axios';
import { AttachmentRatesResponse } from './kpiService';

const API_BASE_URL = 'http://localhost:8000/api';

// Fonction temporaire pour résoudre les erreurs de linter
export const getAttachmentRatesByCountry = async (): Promise<{ data: AttachmentRatesResponse[], kpi: any }> => {
  try {
    console.log('Appel API getAttachmentRatesByCountry...');
    const response = await axios.get(`${API_BASE_URL}/attachment_rates_by_country`);
    console.log('Réponse brute getAttachmentRatesByCountry:', response.data);
    
    let dataArray: any[] = [];
    let kpiData = {
      total_countries: 0,
      total_tests: 0,
      avg_success_rate: 0
    };
    
    // Extraire les données correctement de la structure {success, data, message, kpi}
    if (response.data && response.data.success === true) {
      if (Array.isArray(response.data.data)) {
        console.log('Données d\'attachement extraites de response.data.data:', response.data.data);
        dataArray = response.data.data;
      }
      
      // Extraire les KPI s'ils sont disponibles
      if (response.data.kpi) {
        console.log('KPI extraits de response.data.kpi:', response.data.kpi);
        kpiData = response.data.kpi;
      }
    } else if (Array.isArray(response.data)) {
      // Cas où les données sont directement un tableau
      console.log('Données d\'attachement en tableau direct:', response.data);
      dataArray = response.data;
    } else if (response.data && typeof response.data === 'object') {
      // Essayer de trouver les données dans d'autres propriétés possibles
      console.log('Structure complète de response.data:', response.data);
      if (Array.isArray(response.data.result)) {
        console.log('Données trouvées dans response.data.result');
        dataArray = response.data.result;
      }
    }
    
    // Traiter les données pour s'assurer qu'elles sont dans le bon format
    const processedData = dataArray.map(item => {
      const processedItem = { ...item };
      
      // S'assurer que attachment_distribution est un objet
      if (processedItem.attachment_distribution) {
        if (typeof processedItem.attachment_distribution === 'string') {
          try {
            console.log(`Parsing attachment_distribution pour ${processedItem.country}:`, processedItem.attachment_distribution);
            processedItem.attachment_distribution = JSON.parse(processedItem.attachment_distribution);
            console.log(`Résultat du parsing:`, processedItem.attachment_distribution);
          } catch (e) {
            console.error(`Erreur lors du parsing de attachment_distribution pour ${processedItem.country}:`, e);
            processedItem.attachment_distribution = {}; // Fallback en cas d'erreur
          }
        }
        
        // Convertir toutes les valeurs en nombres
        Object.keys(processedItem.attachment_distribution).forEach(key => {
          const value = processedItem.attachment_distribution[key];
          processedItem.attachment_distribution[key] = typeof value === 'string' ? parseFloat(value) : (typeof value === 'number' ? value : 0);
        });
      } else {
        processedItem.attachment_distribution = {};
      }
      
      // Calculer le taux de succès si non présent
      if (typeof processedItem.success_rate === 'undefined' || processedItem.success_rate === null) {
        const totalTests = processedItem.total_tests || 0;
        if (totalTests > 0) {
          // Le taux de succès est la somme des niveaux 1-6 (tous sauf niveau 0)
          const successfulAttachments = Object.entries(processedItem.attachment_distribution)
            .filter(([level]) => parseInt(level) > 0)
            .reduce((sum, [_, value]) => sum + (typeof value === 'number' ? value : 0), 0);
          
          processedItem.success_rate = (successfulAttachments / totalTests) * 100;
        } else {
          processedItem.success_rate = 0;
        }
      }
      
      return processedItem;
    });
    
    console.log('Données d\'attachement traitées:', processedData);
    return { 
      data: processedData,
      kpi: kpiData
    };
  } catch (error) {
    console.error('Erreur dans getAttachmentRatesByCountry:', error);
    if (axios.isAxiosError(error)) {
      console.error('URL appelée:', error.config?.url);
      console.error('Réponse:', error.response?.data);
    }
    return { 
      data: [],
      kpi: {
        total_countries: 0,
        total_tests: 0,
        avg_success_rate: 0
      }
    };
  }
}; 