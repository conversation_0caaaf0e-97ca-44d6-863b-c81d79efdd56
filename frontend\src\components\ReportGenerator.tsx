import axios from 'axios';
import { useEffect, useState } from 'react';
import { toast } from 'react-hot-toast';
import { FaCalendarAlt, FaDownload, FaFileAlt, FaFileExcel, FaFilePdf, FaGlobe, FaNetworkWired, FaShareAlt } from 'react-icons/fa';
import { API_BASE_URL } from '../config';

// Interface pour les rapports générés
interface GeneratedReport {
  id: number;
  name: string;
  type: string;
  date: string;
  size: string;
  format: string;
  url?: string;
}

const ReportGenerator = () => {
  const [reportType, setReportType] = useState<string>('pdf');
  const [timeFrame, setTimeFrame] = useState<string>('month');
  const [country, setCountry] = useState<string>('all');
  const [operator, setOperator] = useState<string>('all');
  const [dataType, setDataType] = useState<string>('all');
  const [loading, setLoading] = useState<boolean>(false);
  const [includeCharts, setIncludeCharts] = useState<boolean>(true);
  const [includeTables, setIncludeTables] = useState<boolean>(true);
  const [includeSummary, setIncludeSummary] = useState<boolean>(true);
  const [includeRecommendations, setIncludeRecommendations] = useState<boolean>(true);
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([
    { 
      id: 1, 
      name: 'Rapport mensuel de roaming - Mai 2025', 
      type: 'summary', 
      date: '01/01/2025', 
      size: '2.5 MB',
      format: 'pdf'
    },
    { 
      id: 2, 
      name: 'Rapport d\'utilisation par pays - Q2 2025', 
      type: 'country', 
      date: '31/12/2025', 
      size: '4.8 MB',
      format: 'xlsx'
    },
    { 
      id: 3, 
      name: 'Analyse des coûts par opérateur - Juin 2025', 
      type: 'operator', 
      date: '18/04/2025', 
      size: '3.2 MB',
      format: 'pdf'
    },
    { 
      id: 4, 
      name: 'Rapport détaillé d\'anomalies - Q2 2025', 
      type: 'anomaly', 
      date: '18/04/2025', 
      size: '5.1 MB',
      format: 'pdf'
    },
  ]);

  // Opérateurs récupérés depuis l'API
  const [operators, setOperators] = useState<{ id: string; name: string }[]>([]);

  // Pays récupérés depuis l'API
  const [countries, setCountries] = useState<{ id: string; name: string }[]>([]);

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const resp = await axios.get(`${API_BASE_URL}/api/country_overview`);
        if (resp.data && Array.isArray(resp.data)) {
          setCountries(resp.data.map((c: any) => ({ id: c.value, name: c.label })));
        }
      } catch (err) {
        console.error('Erreur lors du chargement des pays', err);
      }
    };
    fetchCountries();
  }, []);

  // Charger les opérateurs quand le pays change (ou au premier rendu)
  useEffect(() => {
    const fetchOperators = async () => {
      try {
        const url = country !== 'all'
          ? `${API_BASE_URL}/api/operators?country=${encodeURIComponent(country)}`
          : `${API_BASE_URL}/api/operators`;
        const resp = await axios.get(url);
        if (resp.data && resp.data.success && Array.isArray(resp.data.data)) {
          setOperators(resp.data.data.map((o: any) => ({ id: o.value, name: o.label })));
        } else if (Array.isArray(resp.data)) {
          // Certains endpoints renvoient directement un tableau
          setOperators(resp.data.map((o: any) => ({ id: o.value || o.id, name: o.label || o.name })));
        }
      } catch (err) {
        console.error('Erreur lors du chargement des opérateurs', err);
      }
    };
    fetchOperators();
  }, [country]);

  const handleGenerateReport = async () => {
    setLoading(true);
    
    try {
      // Préparer les données pour l'API
      const requestData = {
        report_type: reportType,
        filters: {
          start_date: null,
          end_date: null,
          countries: country !== 'all' ? [country] : null,
          operators: operator !== 'all' ? [operator] : null,
          services: dataType !== 'all' ? [dataType] : null
        },
        include_charts: includeCharts,
        include_tables: includeTables,
        include_anomalies: includeRecommendations,
        title: `Rapport ${timeFrame === 'month' ? 'mensuel' : 
                timeFrame === 'week' ? 'hebdomadaire' : 
                timeFrame === 'day' ? 'quotidien' : 
                timeFrame === 'year' ? 'annuel' : 
                timeFrame === 'quarter' ? 'trimestriel' : 'personnalisé'} de Roaming`,
        notes: `Rapport généré le ${new Date().toLocaleDateString()} incluant les données ${
          country !== 'all' ? `pour ${country}` : 'pour tous les pays'
        } ${operator !== 'all' ? `et l'opérateur ${operator}` : ''}`
      };
      
      // Appeler l'API
      console.log('Envoi de la requête au backend:', requestData);
      const response = await axios.post(`${API_BASE_URL}/api/reports/auto-generate`, requestData);
      
      console.log('Réponse du backend:', response.data);
      
      if (response.data) {
        // Ajouter le nouveau rapport à la liste
        const newReport: GeneratedReport = {
          id: Date.now(), // Utiliser timestamp comme ID temporaire
          name: requestData.title,
          type: timeFrame,
          date: new Date().toLocaleDateString(),
          size: response.data.file_size ? `${(response.data.file_size / 1024).toFixed(1)} KB` : 'N/A',
          format: reportType,
          url: response.data.report_url
        };
        
        setGeneratedReports(prev => [newReport, ...prev]);
        
        toast.success('Rapport généré avec succès!');
      } else {
        toast.error('Erreur lors de la génération du rapport');
      }
    } catch (error) {
      console.error('Erreur lors de la génération du rapport:', error);
      toast.error('Erreur lors de la génération du rapport');
    } finally {
      setLoading(false);
    }
  };

  const handleDownloadReport = (report: GeneratedReport) => {
    if (report.url) {
      // Si le rapport a une URL, ouvrir dans un nouvel onglet
      window.open(`${API_BASE_URL}/api${report.url}`, '_blank');
    } else {
      // Sinon, afficher un message d'erreur
      toast.error('URL de téléchargement non disponible');
    }
  };

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Générateur de Rapports</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-medium mb-4">Paramètres du rapport</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type de rapport</label>
                <select 
                  value={reportType}
                  onChange={(e) => setReportType(e.target.value)}
                  className="form-select block w-full rounded-md"
                >
                  <option value="summary">Rapport de synthèse</option>
                  <option value="country">Analyse par pays</option>
                  <option value="operator">Analyse par opérateur</option>
                  <option value="usage">Analyse d'utilisation</option>
                  <option value="cost">Analyse des coûts</option>
                  <option value="anomaly">Détection d'anomalies</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Période</label>
                <select 
                  value={timeFrame}
                  onChange={(e) => setTimeFrame(e.target.value)}
                  className="form-select block w-full rounded-md"
                >
                  <option value="day">Jour</option>
                  <option value="week">Semaine</option>
                  <option value="month">Mois</option>
                  <option value="quarter">Trimestre</option>
                  <option value="year">Année</option>
                  <option value="custom">Période personnalisée</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Pays</label>
                <select 
                  value={country}
                  onChange={(e) => setCountry(e.target.value)}
                  className="form-select block w-full rounded-md"
                >
                  <option value="all">Tous les pays</option>
                  {countries.map(item => (
                    <option key={item.id} value={item.id}>{item.name}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Opérateur</label>
                <select 
                  value={operator}
                  onChange={(e) => setOperator(e.target.value)}
                  className="form-select block w-full rounded-md"
                >
                  <option value="all">Tous les opérateurs</option>
                  {operators.map(item => (
                    <option key={item.id} value={item.id}>{item.name}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Type de données</label>
                <select 
                  value={dataType}
                  onChange={(e) => setDataType(e.target.value)}
                  className="form-select block w-full rounded-md"
                >
                  <option value="all">Toutes les données</option>
                  <option value="data">Données</option>
                  <option value="voice">Voix</option>
                  <option value="sms">SMS</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Format de sortie</label>
                <div className="flex space-x-4 mt-2">
                  <div className="flex items-center">
                    <input 
                      type="radio" 
                      id="pdf" 
                      name="format" 
                      className="form-radio" 
                      checked={reportType === 'pdf'} 
                      onChange={() => setReportType('pdf')}
                    />
                    <label htmlFor="pdf" className="ml-2 text-sm text-gray-700">PDF</label>
                  </div>
                  <div className="flex items-center">
                    <input 
                      type="radio" 
                      id="excel" 
                      name="format" 
                      className="form-radio"
                      checked={reportType === 'excel'} 
                      onChange={() => setReportType('excel')}
                    />
                    <label htmlFor="excel" className="ml-2 text-sm text-gray-700">Excel</label>
                  </div>
                  <div className="flex items-center">
                    <input 
                      type="radio" 
                      id="csv" 
                      name="format" 
                      className="form-radio"
                      checked={reportType === 'csv'} 
                      onChange={() => setReportType('csv')}
                    />
                    <label htmlFor="csv" className="ml-2 text-sm text-gray-700">CSV</label>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="border-t border-gray-200 pt-4">
              <h4 className="font-medium text-gray-700 mb-2">Options avancées</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="charts" 
                    className="form-checkbox" 
                    checked={includeCharts}
                    onChange={(e) => setIncludeCharts(e.target.checked)}
                  />
                  <label htmlFor="charts" className="ml-2 text-sm text-gray-700">Inclure les graphiques</label>
                </div>
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="tables" 
                    className="form-checkbox"
                    checked={includeTables}
                    onChange={(e) => setIncludeTables(e.target.checked)}
                  />
                  <label htmlFor="tables" className="ml-2 text-sm text-gray-700">Inclure les tableaux détaillés</label>
                </div>
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="summary" 
                    className="form-checkbox"
                    checked={includeSummary}
                    onChange={(e) => setIncludeSummary(e.target.checked)}
                  />
                  <label htmlFor="summary" className="ml-2 text-sm text-gray-700">Inclure un résumé exécutif</label>
                </div>
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="recommendations" 
                    className="form-checkbox"
                    checked={includeRecommendations}
                    onChange={(e) => setIncludeRecommendations(e.target.checked)}
                  />
                  <label htmlFor="recommendations" className="ml-2 text-sm text-gray-700">Inclure les recommandations</label>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <button 
                onClick={handleGenerateReport}
                disabled={loading}
                className="btn bg-primary-500 text-white hover:bg-primary-600 disabled:bg-primary-300"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Génération en cours...
                  </>
                ) : (
                  <>
                    Générer le rapport
                  </>
                )}
              </button>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">Aperçu des éléments inclus</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center mb-2">
                  <FaFileAlt className="text-primary-500 mr-2" />
                  <h4 className="font-medium">Résumé exécutif</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Vue d'ensemble de l'utilisation roaming et des coûts associés.
                </p>
              </div>
              
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center mb-2">
                  <FaGlobe className="text-primary-500 mr-2" />
                  <h4 className="font-medium">Analyse par pays</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Répartition détaillée par pays avec tendances et comparaisons.
                </p>
              </div>
              
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center mb-2">
                  <FaNetworkWired className="text-primary-500 mr-2" />
                  <h4 className="font-medium">Analyse par opérateur</h4>
                </div>
                <p className="text-sm text-gray-600">
                  Performances par opérateur et distribution des utilisateurs.
                </p>
              </div>
            </div>
          </div>
        </div>
        
        <div>
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-medium mb-4">Rapports récents</h3>
            <ul className="divide-y divide-gray-200">
              {generatedReports.map(report => (
                <li key={report.id} className="py-3">
                  <div className="flex items-start">
                    {report.format === 'pdf' ? (
                      <FaFilePdf className="text-red-500 mt-1 mr-3" />
                    ) : (
                      <FaFileExcel className="text-green-500 mt-1 mr-3" />
                    )}
                    <div>
                      <h4 className="text-sm font-medium">{report.name}</h4>
                      <div className="text-xs text-gray-500 mt-1">
                        {report.date} • {report.size}
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 flex space-x-2">
                    <button className="text-xs text-primary-600 hover:text-primary-800 flex items-center" onClick={() => handleDownloadReport(report)}>
                      <FaDownload className="mr-1" size={12} /> Télécharger
                    </button>
                    <button className="text-xs text-gray-600 hover:text-gray-800 flex items-center">
                      <FaShareAlt className="mr-1" size={12} /> Partager
                    </button>
                  </div>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium mb-4">Rapports programmés</h3>
            <div className="space-y-4">
              <div className="border rounded-md p-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Rapport mensuel de synthèse</h4>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Actif</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Envoyé le premier du mois</p>
                <div className="flex items-center mt-2 text-xs text-gray-600">
                  <FaCalendarAlt className="mr-1" size={10} />
                  <span>Prochain: 18/04/2025</span>
                </div>
              </div>
              
              <div className="border rounded-md p-3">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">Rapport hebdomadaire d'utilisation</h4>
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Actif</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">Envoyé chaque lundi</p>
                <div className="flex items-center mt-2 text-xs text-gray-600">
                  <FaCalendarAlt className="mr-1" size={10} />
                  <span>Prochain: 18/04/2025</span>
                </div>
              </div>
              
              <button className="btn border border-dashed border-gray-300 text-gray-600 w-full hover:bg-gray-50 mt-2">
                + Ajouter un rapport programmé
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportGenerator; 
 
 