import axios from 'axios';

const API_BASE_URL = 'http://localhost:8000/api';

export interface WeekData {
  id: string;
  name: string;
}

export interface KpiData {
  totalRevenue: { value: string; trend: number; label: string };
  activeUsers: { value: string; trend: number; label: string };
  dataUsage: { value: string; trend: number; label: string };
  operators: { value: string; trend: number; label: string };
  attachRate: { value: string; trend: number; label: string };
  successRateRoamer: { value: string; trend: number; label: string; service: string };
  speedTest: { value: string; trend: number; label: string; operator: string };
  successRateDirection: { value: string; trend: number; label: string; direction: string };
}

export interface KpiTrendData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    tension: number;
  }[];
}

export interface ChartJsData {
  labels: string[];
  datasets: any[];
}

export interface PieChartData {
  labels: string[];
  values: number[];
  colors: string[];
}

export interface PeriodKpiData {
  kpis: KpiData;
  kpiTrend: KpiTrendData;
  barChart: ChartJsData;
  pieChart: PieChartData;
}

export interface SelectionItem {
  value: string;
  label: string;
}

// Interface pour les données de pays
export interface CountryData {
  country: string;
  total_tests: number;
  success_rate: number;
  avg_duration: number;
  unique_operators: number;
}

export interface NetworkPerformanceStats {
  summary: {
    avg_duration_sec: number;
    success_rate_in: number;
    success_rate_out: number;
    total_tests: number;
    total_incomplete: number;
    total_countries: number;
    total_locations: number;
  };
  by_country_operator: {
    country: string;
    operator: string;
    total_tests: number;
    data_emis_mo: number;
    data_recus_mo: number;
    total_data_mo: number;
  }[];
  by_date: {
    date: string;
    total_tests: number;
    success_rate_in: number;
    success_rate_out: number;
    avg_duration_sec: number;
    period_type: string;
  }[];
  by_week: {
    date: string;
    start_date: string;
    total_tests: number;
    success_rate_in: number;
    success_rate_out: number;
    avg_duration_sec: number;
    period_type: string;
  }[];
  by_month: {
    date: string;
    total_tests: number;
    success_rate_in: number;
    success_rate_out: number;
    avg_duration_sec: number;
    period_type: string;
  }[];
  by_year: {
    date: string;
    total_tests: number;
    success_rate_in: number;
    success_rate_out: number;
    avg_duration_sec: number;
    period_type: string;
  }[];
  available_countries: string[];
  available_locations: string[];
}

export interface AttachmentRatesResponse {
  country: string;
  success_rate: number;
  attachment_distribution: any;
  total_tests: number;
}

export interface AttachmentRatesAPIResponse {
  success: boolean;
  message: string;
  data: AttachmentRatesResponse[];
  kpi: {
    total_countries: number;
    total_tests: number;
    avg_success_rate: number;
  };
}

export interface HttpDownloadPerformance {
  by_country_operator: {
    country: string;
    operator: string;
    avg_data_rate_mbps: number;
    avg_activation_time_s: number;
  }[];
}

export interface SteeringChartResponse {
  countries: string[];
  success_rates: number[];
  total_attempts: number[];
  failures: number[];
}

export interface TransformedSteeringData {
  country: string;
  success_rate: number;
  total_attempts: number;
  failures: number;
  'Taux de succès'?: number;
  'Échecs'?: number;
  'Entrées'?: number;
  avg_duration?: number;
  start_date?: string;
  end_date?: string;
  operator?: string;
}

export interface SteeringChartResult {
  success: boolean;
  data: TransformedSteeringData[];
  error?: string;
}

// Nouvelle interface pour le taux de succès formaté
export interface FormattedSuccessRate {
  country: string;
  'Taux de succès': number;
  'Échecs': number;
  'Entrées'?: number; // Optionnel si non toujours fourni ou utilisé
}

export interface NetworkTypeData {
  success: boolean;
  message: string;
  data: {
    network_types: {
      [key: string]: number;
    };
    countries_by_network: {
      [network: string]: {
        country: string;
        location: string;
        operator: string;
        count: number;
      }[];
    };
  };
}

// Exporter les fonctions individuellement
export async function getWeeks(): Promise<WeekData[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/weeks`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des semaines:', error);
    return [];
  }
}

export async function getPeriodData(period: string, country?: string, operator?: string, year?: number, month?: number, week?: number, quarter?: number): Promise<PeriodKpiData> {
  try {
    let url = `${API_BASE_URL}/period-data/${period}`;
    const params = new URLSearchParams();

    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    if (year) {
      params.append('year', year.toString());
    }
    if (month) {
      params.append('month', month.toString());
    }
    if (week) {
      params.append('week', week.toString());
    }
    if (quarter) {
      params.append('quarter', quarter.toString());
    }

    if (params.toString()) {
      url += `?${params.toString()}`;
    }
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des données de période:', error);
    throw error;
  }
}



export async function getAttachmentRatesByCountry(
  country?: string,
  operator?: string,
  start_date?: string,
  end_date?: string
): Promise<AttachmentRatesAPIResponse> {
  try {
    console.log('Appel API getAttachmentRatesByCountry...');
    
    // Construire les paramètres de requête
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    if (start_date) {
      params.append('start_date', start_date);
    }
    if (end_date) {
      params.append('end_date', end_date);
    }
    
    const queryString = params.toString();
    const url = `${API_BASE_URL}/attachment_rates_by_country${queryString ? `?${queryString}` : ''}`;
    
    console.log(`URL de requête pour getAttachmentRatesByCountry: ${url}`);
    const response = await axios.get(url);
    console.log('Réponse brute getAttachmentRatesByCountry:', response.data);

    let dataArray: AttachmentRatesResponse[] = [];
    let kpiData = {
      total_countries: 0,
      total_tests: 0,
      avg_success_rate: 0
    };

    if (response.data && response.data.success === true) {
      if (Array.isArray(response.data.data)) {
        console.log('Données d\'attachement extraites de response.data.data:', response.data.data);
        dataArray = response.data.data;
      }

      if (response.data.kpi) {
        console.log('KPI extraits de response.data.kpi:', response.data.kpi);
        kpiData = response.data.kpi;
      }
    } else if (Array.isArray(response.data)) {
      console.log('Données d\'attachement en tableau direct:', response.data);
      dataArray = response.data;
    } else if (response.data && typeof response.data === 'object') {
      console.log('Structure complète de response.data:', response.data);
      if (Array.isArray(response.data.result)) {
        console.log('Données trouvées dans response.data.result');
        dataArray = response.data.result;
      } else if (Array.isArray(response.data.data)) {
          console.log('Données trouvées dans response.data.data sans propriété success.', response.data.data);
          dataArray = response.data.data;
      }
    }

    const processedData = dataArray.map(item => {
      const processedItem = { ...item };

      if (processedItem.attachment_distribution) {
        if (typeof processedItem.attachment_distribution === 'string') {
          try {
            console.log(`Parsing attachment_distribution pour ${processedItem.country}:`, processedItem.attachment_distribution);
            processedItem.attachment_distribution = JSON.parse(processedItem.attachment_distribution);
            console.log(`Résultat du parsing:`, processedItem.attachment_distribution);
          } catch (e) {
            console.error(`Erreur lors du parsing de attachment_distribution pour ${processedItem.country}:`, e);
            processedItem.attachment_distribution = {};
          }
        }

        Object.keys(processedItem.attachment_distribution).forEach(key => {
          const value = processedItem.attachment_distribution[key];
          processedItem.attachment_distribution[key] = typeof value === 'string' ? parseFloat(value) : (typeof value === 'number' ? value : 0);
        });
      } else {
        processedItem.attachment_distribution = {};
      }

      if (typeof processedItem.success_rate === 'undefined' || processedItem.success_rate === null) {
        const totalTests = processedItem.total_tests || 0;
        if (totalTests > 0) {
          const successfulAttachments = Object.entries(processedItem.attachment_distribution)
            .filter(([level]) => parseInt(level) > 0)
            .reduce((sum, [_, value]) => sum + (typeof value === 'number' ? value : 0), 0);

          processedItem.success_rate = (successfulAttachments / totalTests) * 100;
        } else {
          processedItem.success_rate = 0;
        }
      }

      return processedItem;
    });

    console.log('Données d\'attachement traitées:', processedData);
    console.log('KPI pour le graphique:', kpiData);

    return {
      success: true,
      message: `${processedData.length} pays trouvés`,
      data: processedData,
      kpi: kpiData
    };
  } catch (error) {
    console.error('Erreur lors de la récupération des taux d\'attachement par pays:', error);
    return {
      success: false,
      message: 'Erreur lors de la récupération des données d\'attachement',
      data: [],
      kpi: {
        total_countries: 0,
        total_tests: 0,
        avg_success_rate: 0
      }
    };
  }
}

export async function getSteeringChartData(filters: any): Promise<SteeringChartResult> {
  try {
    const params = new URLSearchParams();
    if (filters && filters.country && filters.country !== 'all') {
      params.append('country', filters.country);
    }
    if (filters && filters.operator && filters.operator !== 'all') {
      params.append('operator', filters.operator);
    }
    if (filters && filters.dateRange && filters.dateRange.startDate) {
      params.append('start_date', filters.dateRange.startDate);
    }
    if (filters && filters.dateRange && filters.dateRange.endDate) {
      params.append('end_date', filters.dateRange.endDate);
    }

    const queryString = params.toString();
    const url = `${API_BASE_URL}/steering_chart_data${queryString ? `?${queryString}` : ''}`;

    const response = await axios.get(url);

    if (response.data && response.data.success) {
      const chartData = Array.isArray(response.data.data) ? response.data.data : [];
      return { success: true, data: chartData };
    } else {
      return { success: false, data: [], error: response.data.error || "Format de réponse invalide" };
    }
  } catch (error) {
    const errorMessage = (axios.isAxiosError(error) && error.response?.data?.detail) ? error.response.data.detail : (error instanceof Error ? error.message : "Erreur inconnue");
    return { success: false, data: [], error: errorMessage };
  }
}

export async function getSteeringSuccessByCountry(filters: any): Promise<SteeringChartResult> {
  try {
    console.log('🔧 getSteeringSuccessByCountry appelé avec filtres:', filters);

    const params = new URLSearchParams();
    if (filters && filters.country && filters.country !== 'all') {
      params.append('country', filters.country);
    }
    if (filters && filters.operator && filters.operator !== 'all') {
      params.append('operator', filters.operator);
    }

    // Ajouter les filtres temporels
    if (filters && filters.period) {
      params.append('period', filters.period);
      console.log('🔧 Ajout du filtre period:', filters.period);
    }
    if (filters && filters.year) {
      params.append('year', filters.year.toString());
      console.log('🔧 Ajout du filtre year:', filters.year);
    }
    if (filters && filters.month) {
      params.append('month', filters.month.toString());
      console.log('🔧 Ajout du filtre month:', filters.month);
    }
    if (filters && filters.week) {
      params.append('week', filters.week.toString());
      console.log('🔧 Ajout du filtre week:', filters.week);
    }
    if (filters && filters.day) {
      params.append('day', filters.day.toString());
      console.log('🔧 Ajout du filtre day:', filters.day);
    }
    if (filters && filters.hour !== undefined) {
      params.append('hour', filters.hour.toString());
      console.log('🔧 Ajout du filtre hour:', filters.hour);
    }
    if (filters && filters.verdict) {
      params.append('verdict', filters.verdict);
    }

    const url = `${API_BASE_URL}/steering_success_by_country_filtered?${params}`;
    console.log('🔧 URL finale:', url);

    const response = await axios.get(url);

    console.log('🔧 Réponse de l\'API steering_success_by_country:', response.data);

    // Vérifier différents formats de réponse possibles
    if (response.data && response.data.success) {
      // Format avec success: true
      const chartData = Array.isArray(response.data.data) ? response.data.data : [];
      return { success: true, data: chartData };
    } else if (Array.isArray(response.data)) {
      // Format direct avec tableau
      return { success: true, data: response.data };
    } else if (response.data && Array.isArray(response.data.data)) {
      // Format avec data mais sans success
      return { success: true, data: response.data.data };
    } else {
      console.error('Format de réponse non reconnu:', response.data);
      return { success: false, data: [], error: response.data.error || "Format de réponse invalide" };
    }
  } catch (error) {
    const errorMessage = (axios.isAxiosError(error) && error.response?.data?.detail) ? error.response.data.detail : (error instanceof Error ? error.message : "Erreur inconnue");
    return { success: false, data: [], error: errorMessage };
  }
}

export async function getAvailableTimeFilters(): Promise<any> {
  try {
    const response = await axios.get(`${API_BASE_URL}/time_filters`);

    if (response.data && response.data.success) {
      return { success: true, data: response.data.data };
    } else {
      return { success: false, data: {}, error: response.data.error || "Format de réponse invalide" };
    }
  } catch (error) {
    const errorMessage = (axios.isAxiosError(error) && error.response?.data?.detail) ? error.response.data.detail : (error instanceof Error ? error.message : "Erreur inconnue");
    return { success: false, data: {}, error: errorMessage };
  }
}

export async function checkApiConnection(): Promise<boolean> {
  try {
    const response = await axios.get(`${API_BASE_URL}/check-api-connection`);
    return response.data.status === 'ok';
  } catch (error) {
    console.error('Erreur lors de la vérification de la connexion API:', error);
    return false;
  }
}

export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    const response = await axios.get(`${API_BASE_URL}/check-db-connection`);
    return response.data.status === 'ok';
  } catch (error) {
    console.error('Erreur lors de la vérification de la connexion à la base de données:', error);
    return false;
  }
}

export async function getFailureDetails(params: {
  verdict: string;
  country?: string;
  operator?: string;
}): Promise<any> {
  try {
    const response = await axios.get(`${API_BASE_URL}/failure_details`, { params });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des détails des échecs:', error);
    throw error;
  }
}

export async function getNetworkPerformanceStats(
  country?: string,
  operator?: string
): Promise<NetworkPerformanceStats> {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    const url = `${API_BASE_URL}/network_performance_stats?${params.toString()}`;
    const response = await axios.get(url);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de performance réseau:', error);
    throw error;
  }
}

export async function getHttpDownloadPerformance(): Promise<HttpDownloadPerformance> {
  try {
    const response = await axios.get(`${API_BASE_URL}/kpi/http_download_performance`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des performances de téléchargement HTTP:', error);
    return { by_country_operator: [] };
  }
}

export async function getHttpPeriodData(period: string = "month", country?: string, operator?: string, year?: number, month?: number, week?: number, quarter?: number) {
  try {
    const params = new URLSearchParams();
    params.append('period', period);
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    if (year) {
      params.append('year', year.toString());
    }
    if (month) {
      params.append('month', month.toString());
    }
    if (week) {
      params.append('week', week.toString());
    }
    if (quarter) {
      params.append('quarter', quarter.toString());
    }
    const response = await axios.get(`${API_BASE_URL}/kpi/http_period_data?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des données de période HTTP:', error);
    throw error;
  }
}

export async function getFailureCauses(country?: string, operator?: string, verdict?: string) {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    // Si verdict est 'fail', on le convertit en 'FAIL' pour le backend
    if (verdict === 'fail') {
      params.append('verdict', 'FAIL');
    } else if (verdict) {
      params.append('verdict', verdict);
    }
    
    console.log('Appel API getFailureCauses avec params:', params.toString());
    const response = await axios.get(`${API_BASE_URL}/kpi/fail_causes?${params.toString()}`);
    console.log('Réponse API getFailureCauses:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des causes d\'échec:', error);
    throw error;
  }
}

export async function getFailureAnalysis(country?: string, operator?: string) {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    const response = await axios.get(`${API_BASE_URL}/failure_analysis?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'analyse des échecs:', error);
    throw error;
  }
}

export async function getWeeklyTrends(country?: string, operator?: string) {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    const response = await axios.get(`${API_BASE_URL}/weekly_attach_trend?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des tendances hebdomadaires:', error);
    throw error;
  }
}

export async function getAnnualTrend(period: string = "month", country?: string, operator?: string, year?: number, month?: number) {
  try {
    const params = new URLSearchParams();
    params.append('period', period);
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    if (year) {
      params.append('year', year.toString());
    };
    if (month) {
      params.append('month', month.toString());
    }
    const response = await axios.get(`${API_BASE_URL}/annual_trend?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des tendances annuelles:', error);
    throw error;
  }
}

export async function getCountryOverview() {
  try {
    console.log('API Call: Requesting /api/country_overview');
    const response = await axios.get(`${API_BASE_URL}/country_overview`);
    console.log('API Call: Received response from /api/country_overview:', response.data);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération de l\'aperçu par pays:', error);
    throw error;
  }
}

export async function uploadData(file: File) {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post(`${API_BASE_URL}/upload-csv`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Erreur lors de l\'upload du fichier:', error);
    throw error;
  }
}

export async function getTopDataConsumptionCountries() {
  try {
    const response = await axios.get(`${API_BASE_URL}/top_data_consumption_countries`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des pays avec la plus forte consommation de données:', error);
    throw error;
  }
}

export async function getFailureCausesByCountryOperator(country: string, operator: string) {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    const response = await axios.get(`${API_BASE_URL}/fail_causes_by_country_operator?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des causes d\'échec par pays et opérateur:', error);
    throw error;
  }
}

export async function getCountries(): Promise<SelectionItem[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/countries`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des pays:', error);
    return [];
  }
}

export async function getOperators(): Promise<SelectionItem[]> {
  try {
    const response = await axios.get(`${API_BASE_URL}/operators`);
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération des opérateurs:', error);
    return [];
  }
}

export async function getAvailableCountriesAndOperators(): Promise<{ success: boolean; countries: {id:string,name:string}[]; operators: {id:string,name:string}[]; message?: string }> {
  try {
    // Appels parallèles
    const [countriesResponse, operatorsResponse] = await Promise.all([
      axios.get(`${API_BASE_URL}/countries`),
      axios.get(`${API_BASE_URL}/operators`)
    ]);

    // Normalisation helper
    const normalize = (arr: any[]): {id:string,name:string}[] => arr.map(item => ({
      id: item.id ?? item.value ?? item.country ?? item.operator ?? '',
      name: item.name ?? item.label ?? item.value ?? item.country ?? item.operator ?? ''
    })).filter(it => it.id && it.name);

    let countries: {id:string,name:string}[] = [];
    let operators: {id:string,name:string}[] = [];

    if (countriesResponse.data) {
      const raw = countriesResponse.data.data ?? countriesResponse.data;
      if (Array.isArray(raw)) countries = normalize(raw);
    }

    if (operatorsResponse.data) {
      const raw = operatorsResponse.data.data ?? operatorsResponse.data;
      if (Array.isArray(raw)) operators = normalize(raw);
    }

    return { success: true, countries, operators };
  } catch (error) {
    console.error('Erreur lors de la récupération des filtres:', error);
    return { success:false, countries:[], operators:[], message: (error as Error).message };
  }
}

export async function getWeeklyAttachTrend(country?: string, operator?: string): Promise<any> {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }

    const url = `${API_BASE_URL}/kpi/weekly_attach_trend${params.toString() ? `?${params.toString()}` : ''}`;
    const response = await axios.get(url);
    
    if (response.data && response.data.success) {
      return response.data;
    } else {
      console.error('Erreur dans la réponse de weekly_attach_trend:', response.data);
      return {
        success: false,
        data: [],
        stats: {
          total_records: 0,
          countries: 0,
          operators: 0,
          avg_success_rate: 0
        },
        message: response.data.message || 'Erreur lors de la récupération des données'
      };
    }
  } catch (error) {
    console.error('Erreur lors de l\'appel à getWeeklyAttachTrend:', error);
    return {
      success: false,
      data: [],
      stats: {
        total_records: 0,
        countries: 0,
        operators: 0,
        avg_success_rate: 0
      },
      message: error instanceof Error ? error.message : 'Erreur inconnue'
    };
  }
}

// Fonction pour récupérer les opérateurs pour un pays spécifique
export const getOperatorsByCountry = async (country: string) => {
  try {
    const response = await axios.get(`${API_BASE_URL}/operators`, { params: { country } });
    if (response.data && response.data.success) {
      return response.data.data.map((op: { value: string; label: string }) => ({
        id: op.value,
        name: op.label,
      }));
    }
    return [];
  } catch (error) {
    console.error('Erreur lors de la récupération des opérateurs par pays:', error);
    return [];
  }
};

export const getTopCountriesConsumption = async () => {
  const response = await axios.get(`${API_BASE_URL}/top_countries`);
  if (response.data && response.data.success && Array.isArray(response.data.data)) {
    return response.data.data;
  }
  console.error('Format de réponse inattendu pour /top_countries:', response.data);
  return [];
}; 

export async function getNetworkTypeDistribution(country?: string, operator?: string): Promise<NetworkTypeData> {
  try {
    const params = new URLSearchParams();
    if (country && country !== 'all') {
      params.append('country', country);
    }
    if (operator && operator !== 'all') {
      params.append('operator', operator);
    }
    
    const url = `${API_BASE_URL}/network_type_distribution${params.toString() ? `?${params.toString()}` : ''}`;
    console.log('Appel API getNetworkTypeDistribution avec URL:', url);
    
    const response = await axios.get(url);
    console.log('Réponse API getNetworkTypeDistribution:', response.data);
    
    return response.data;
  } catch (error) {
    console.error('Erreur lors de la récupération de la distribution des types de réseau:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Erreur inconnue',
      data: {
        network_types: {},
        countries_by_network: {}
      }
    };
  }
} 