import os
import tempfile
from datetime import datetime
from typing import Optional

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from fpdf import FPDF
from sqlalchemy import text

from ..models import SessionLocal, SteeringOfRoaming

REPORTS_DIR = "reports"
os.makedirs(REPORTS_DIR, exist_ok=True)

class SmartReportPDF(FPDF):
    """Classe personnalisée pour le rapport PDF"""
    def header(self):
        self.set_font("Arial", "B", 14)
        self.cell(0, 10, self.title, 0, 1, "C")
        self.ln(2)
        self.line(10, 18, 200, 18)
        self.ln(4)

    def footer(self):
        self.set_y(-15)
        self.set_font("Arial", "I", 8)
        self.cell(0, 10, f"Page {self.page_no()}/{{nb}}", 0, 0, "C")


def _fetch_data(periode: str, pays: str, operateur: str, type_donnees: str) -> pd.DataFrame:
    """Récupère et filtre les données depuis la base MySQL via requête SQL brute.
    On se limite aux colonnes nécessaires pour les graphiques afin d'éviter les problèmes de schéma."""

    engine = SessionLocal().get_bind()

    base_sql = """
        SELECT a_location_country,
               Verdict,
               a_NetworkType,
               Timestamp
        FROM steeringofroaming
        WHERE TCName = 'SteeringOfRoaming'
    """

    params = {}

    # Filtres dynamiques
    if pays and pays.lower() not in ["all", "tous", "tous les pays"]:
        base_sql += " AND a_location_country = :country"
        params["country"] = pays

    if operateur and operateur.lower() not in ["all", "tous", "tous les opérateurs"]:
        base_sql += " AND a_UsedPLMNName = :operator"
        params["operator"] = operateur

    if periode and periode.lower() in ["jour", "mois"]:
        from datetime import timedelta
        if periode.lower() == "jour":
            date_from = datetime.utcnow() - timedelta(days=30)
        else:  # mois → 12 derniers mois
            date_from = datetime.utcnow() - timedelta(days=365)
        base_sql += " AND Timestamp >= :date_from"
        params["date_from"] = date_from

    with engine.connect() as conn:
        df = pd.read_sql_query(text(base_sql), conn, params=params)

    # Colonne id synthétique pour les agrégations
    if "id" not in df.columns:
        df["id"] = range(len(df))

    return df


def _create_success_rate_chart(df: pd.DataFrame, tmp_dir: str) -> Optional[str]:
    if df.empty or "a_location_country" not in df.columns or "Verdict" not in df.columns:
        return None

    df["is_success"] = df["Verdict"].str.upper().isin(["PASS", "SUCCESS"])
    agg = df.groupby("a_location_country").agg(total_tests=("Verdict", "count"), successes=("is_success", "sum"))
    agg["success_rate"] = (agg["successes"] / agg["total_tests"]) * 100
    agg = agg.sort_values("success_rate", ascending=False).head(20)

    plt.figure(figsize=(10, 6))
    sns.barplot(x=agg.index, y=agg["success_rate"], palette="Blues_d")
    plt.title("Taux de succès par pays")
    plt.ylabel("Taux de succès (%)")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    path = os.path.join(tmp_dir, "success_rate.png")
    plt.savefig(path, dpi=300)
    plt.close()
    return path


def _create_attach_distribution_chart(df: pd.DataFrame, tmp_dir: str) -> Optional[str]:
    if df.empty or "a_location_country" not in df.columns or "a_NetworkType" not in df.columns:
        return None

    if "id" not in df.columns:
        df["id"] = 1  # fallback count
    pivot = df.pivot_table(index="a_location_country", columns="a_NetworkType", values="id", aggfunc="count", fill_value=0)
    # Garder top 10 pays pour la lisibilité
    pivot = pivot.sort_values(by=list(pivot.columns)[0], ascending=False).head(10)

    pivot.plot(kind="bar", stacked=True, figsize=(10, 6), colormap="tab20")
    plt.title("Distribution des niveaux d'attachement par pays(Steering Of Roaming)")
    plt.ylabel("Nombre d'événements")
    plt.xticks(rotation=45, ha="right")
    plt.tight_layout()
    path = os.path.join(tmp_dir, "attach_distribution.png")
    plt.savefig(path, dpi=300)
    plt.close()
    return path


def _create_top_consumption_chart(df: pd.DataFrame, tmp_dir: str) -> Optional[str]:
    if df.empty or "a_location_country" not in df.columns:
        return None

    # Utiliser le nombre de tests comme proxy de consommation si aucune métrique dédiée
    agg = df.groupby("a_location_country").size().reset_index(name="total")
    agg = agg.sort_values("total", ascending=False).head(30)

    plt.figure(figsize=(12, 6))
    sns.barplot(x="a_location_country", y="total", data=agg, palette="viridis")
    plt.title("Volume Data pour les 30 Top Pays (proxy)")
    plt.ylabel("Volume (nombre de tests)")
    plt.xticks(rotation=65, ha="right")
    plt.tight_layout()
    path = os.path.join(tmp_dir, "top_consumption.png")
    plt.savefig(path, dpi=300)
    plt.close()
    return path


def generate_smart_report(params) -> str:
    """Point d'entrée principal pour générer le rapport et retourner le chemin du PDF"""
    df = _fetch_data(params.periode, params.pays, params.operateur, params.type_donnees)

    if df.empty:
        raise ValueError("Aucune donnée disponible pour les filtres sélectionnés")

    # Créer les charts dans un dossier temporaire
    tmp_dir = tempfile.mkdtemp()

    charts = {
        "success_rate": _create_success_rate_chart(df, tmp_dir),
        "attach_distribution": _create_attach_distribution_chart(df, tmp_dir),
        "top_consumption": _create_top_consumption_chart(df, tmp_dir),
    }

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    pdf_name = f"rapport_{timestamp}.pdf"
    pdf_path = os.path.join(REPORTS_DIR, pdf_name)

    pdf = SmartReportPDF()
    pdf.set_title("Rapport intelligent de Roaming")
    pdf.alias_nb_pages()
    pdf.add_page()

    # Section 1: Taux de succès par pays
    pdf.set_font("Arial", "B", 12)
    pdf.cell(0, 10, "1. Taux de succès par pays", 0, 1)
    if charts["success_rate"]:
        pdf.image(charts["success_rate"], x=10, w=180)
        pdf.ln(5)
    else:
        pdf.set_font("Arial", "", 10)
        pdf.multi_cell(0, 8, "Aucune donnée disponible pour cette section.")

    # Section 2: Distribution des niveaux d'attachement
    pdf.add_page()
    pdf.set_font("Arial", "B", 12)
    pdf.cell(0, 10, "2. Distribution des niveaux d'attachement par pays", 0, 1)
    if charts["attach_distribution"]:
        pdf.image(charts["attach_distribution"], x=10, w=180)
        pdf.ln(5)
    else:
        pdf.set_font("Arial", "", 10)
        pdf.multi_cell(0, 8, "Aucune donnée disponible pour cette section.")

    # Section 3: Volume Data pour les 30 Top Pays
    pdf.add_page()
    pdf.set_font("Arial", "B", 12)
    pdf.cell(0, 10, "3. Volume Data pour Les 30 Top Pays", 0, 1)
    if charts["top_consumption"]:
        pdf.image(charts["top_consumption"], x=10, w=180)
        pdf.ln(5)
    else:
        pdf.set_font("Arial", "", 10)
        pdf.multi_cell(0, 8, "Aucune donnée disponible pour cette section.")

    pdf.output(pdf_path)

    return pdf_path 