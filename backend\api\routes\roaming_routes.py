from fastapi import APIRouter, Depends, HTTPException, File, UploadFile, BackgroundTasks
from fastapi.responses import JSONResponse
from typing import Dict, Any, List, Optional
import os
import shutil
from pathlib import Path
from datetime import datetime
import json
import sys
import mysql.connector
from mysql.connector import Error
import pandas as pd
from database import get_db_connection
from ..Kpi_service_clean import (
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_weekly_attach_trend,
    get_annual_trend
)

# Importer l'analyseur de données de roaming
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from roaming_analyzer import RoamingAnalyzer

# Créer le router FastAPI avec le bon préfixe
router = APIRouter(
    prefix="/api",
    tags=["roaming"],
    responses={404: {"description": "Non trouvé"}}
)

# Répertoire des données
DATA_DIR = Path("../data")
if not DATA_DIR.exists():
    DATA_DIR.mkdir(parents=True, exist_ok=True)

# Instance de l'analyseur
analyzer = RoamingAnalyzer(data_dir=str(DATA_DIR))

# Nouvelle route pour récupérer toutes les données avec tous les détails
@router.get("/all_roaming_data")
async def get_all_roaming_data():
    """
    Récupérer toutes les données de roaming avec tous les détails demandés
    
    Returns:
        Dict: Données complètes de roaming
    """
    try:
        # Connexion à la base de données
        connection = mysql.connector.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', ''),
            database=os.getenv('DB_NAME', 'kpi')
        )
        
        if connection.is_connected():
            cursor = connection.cursor(dictionary=True)
            
            # Requête SQL complète pour récupérer toutes les colonnes importantes
            query = """
            SELECT
                id,
                TCName,
                Verdict,
                errortext,
                a_NrOfPlmnsRejected,
                a_nrofluprequests,
                a_rejectCauses,
                a_rejectedPLMNs,
                a_networkType,
                a_location_country,
                a_location,
                a_number,
                a_imsi,
                a_Usedplmnname,
                a_usedplmn,
                a_TADIG,
                dayofweek,
                weekofyear,
                timestamp,
                a_VPLMN_registered,
                a_LupDuration,
                `attache0 %`,
                `attache1 %`,
                `attache2 %`,
                `attache3 %`,
                `attache4 %`,
                `attache5 %`,
                `attache6 %`,
                attach_success_rate
            FROM steeringofroaming
            WHERE TCName = 'SteeringOfRoaming'
            ORDER BY weekofyear ASC, a_location_country ASC
            """
            
            print("Exécution de la requête SQL pour récupérer toutes les données...")
            cursor.execute(query)
            results = cursor.fetchall()
            print(f"Nombre de résultats trouvés : {len(results)}")
            
            # Traiter les résultats pour ajouter des informations utiles
            processed_results = []
            for row in results:
                # Calculer le niveau de KPI
                success_rate = row.get('attach_success_rate', 0)
                if success_rate > 90:
                    kpi_level = 'green'
                elif success_rate > 75:
                    kpi_level = 'orange'
                else:
                    kpi_level = 'red'
                
                # Ajouter le niveau de KPI
                row['kpi_level'] = kpi_level
                
                # Convertir les timestamps en chaînes de caractères
                if 'timestamp' in row and row['timestamp']:
                    row['timestamp'] = row['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
                
                processed_results.append(row)
            
            # Fermer la connexion
            cursor.close()
            connection.close()
            
            return {"success": True, "data": processed_results}
        else:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
            
    except Error as e:
        print(f"Erreur lors de la récupération des données: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/upload")
async def upload_roaming_data(background_tasks: BackgroundTasks, file: UploadFile = File(...)):
    """
    Télécharger un fichier CSV de données de roaming et lancer l'analyse
    
    Args:
        background_tasks (BackgroundTasks): Tâches en arrière-plan FastAPI
        file (UploadFile): Fichier CSV à analyser
    
    Returns:
        JSONResponse: Réponse avec le statut de l'upload
    """
    try:
        # Sauvegarder le fichier dans le répertoire de données
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{file.filename}"
        file_path = DATA_DIR / filename
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        # Exécuter l'analyse en tâche de fond
        background_tasks.add_task(analyze_csv_file, str(file_path))
        
        return JSONResponse(
            content={
                "success": True,
                "message": "Fichier téléchargé avec succès. Analyse en cours.",
                "filename": filename
            },
            status_code=200
        )
    except Exception as e:
        return JSONResponse(
            content={
                "success": False,
                "message": f"Erreur lors du téléchargement: {str(e)}"
            },
            status_code=500
        )

@router.get("/analyze/{filename}")
async def analyze_file(filename: str):
    """
    Analyser un fichier CSV spécifique
    
    Args:
        filename (str): Nom du fichier à analyser
    
    Returns:
        JSONResponse: Résultat de l'analyse
    """
    try:
        file_path = DATA_DIR / filename
        
        if not file_path.exists():
            return JSONResponse(
                content={
                    "success": False,
                    "message": f"Fichier non trouvé: {filename}"
                },
                status_code=404
            )
        
        # Exécuter l'analyse
        result = analyzer.run_analysis(str(file_path))
        
        return JSONResponse(
            content={
                "success": True,
                "data": result,
                "chart_url": "/steering_chart.png"
            },
            status_code=200
        )
    except Exception as e:
        return JSONResponse(
            content={
                "success": False,
                "message": f"Erreur lors de l'analyse: {str(e)}"
            },
            status_code=500
        )

@router.get("/analyze")
async def analyze_latest():
    """
    Analyser le fichier CSV le plus récent
    
    Returns:
        JSONResponse: Résultat de l'analyse
    """
    try:
        # Trouver le fichier CSV le plus récent
        csv_files = list(DATA_DIR.glob("*.csv"))
        
        if not csv_files:
            # Si aucun fichier n'est trouvé, exécuter avec des données simulées
            result = analyzer.run_analysis()
        else:
            # Trier par date de modification et prendre le plus récent
            latest_file = max(csv_files, key=os.path.getmtime)
            result = analyzer.run_analysis(str(latest_file))
        
        return JSONResponse(
            content={
                "success": True,
                "data": result,
                "chart_url": "/steering_chart.png"
            },
            status_code=200
        )
    except Exception as e:
        return JSONResponse(
            content={
                "success": False,
                "message": f"Erreur lors de l'analyse: {str(e)}"
            },
            status_code=500
        )

@router.get("/steering_success_by_country")
async def steering_success_endpoint():
    """Endpoint pour obtenir les données de succès du steering par pays"""
    try:
        data = get_steering_success_by_country()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachment_rates_by_country")
async def attachment_rates_endpoint():
    """Endpoint pour obtenir les taux d'attachement par pays"""
    try:
        data = get_attachment_rates_by_country()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/steering_chart_data")
async def get_steering_chart_data(
    country: Optional[str] = None, 
    operator: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """
    Retourne les données pour le graphique de succès du steering, en utilisant la logique
    robuste de Kpi_service_clean pour garantir la cohérence.
    
    Args:
        country: Filtre par pays
        operator: Filtre par opérateur
        start_date: Date de début au format YYYY-MM-DD
        end_date: Date de fin au format YYYY-MM-DD
    """
    try:
        # Appeler la fonction de service qui contient la logique de calcul correcte
        from ..Kpi_service_clean import get_steering_chart_data
        
        # Nous passons les filtres à la fonction de service
        result = get_steering_chart_data(
            country=country, 
            operator=operator, 
            start_date=start_date, 
            end_date=end_date
        )
        
        # Si une erreur est retournée
        if 'error' in result:
            raise HTTPException(status_code=500, detail=result['error'])
            
        # Transformer les données pour le format attendu par le frontend
        data = []
        if 'data' in result:
            # Grouper par pays pour calculer les taux de succès
            country_data = {}
            for item in result['data']:
                country = item['country']
                if country not in country_data:
                    country_data[country] = {
                        'total': 0,
                        'success': 0,
                        'fail': 0
                    }
                
                country_data[country]['total'] += 1
                if item['verdict'] == 'PASS':
                    country_data[country]['success'] += 1
                else:
                    country_data[country]['fail'] += 1
            
            # Calculer les taux de succès
            for country, counts in country_data.items():
                success_rate = (counts['success'] / counts['total'] * 100) if counts['total'] > 0 else 0
                data.append({
                    'country': country,
                    'success_rate': round(success_rate, 2),
                    'total_attempts': counts['total'],
                    'failures': counts['fail']
                })
            
            # Trier par taux de succès décroissant
            data = sorted(data, key=lambda x: x['success_rate'], reverse=True)
        
        return {"success": True, "data": data}
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/period_data")
async def period_data_endpoint(period: str = "month"):
    """Endpoint pour obtenir les données par période"""
    try:
        if period == "month":
            data = get_annual_trend()
        else:
            data = get_weekly_attach_trend()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/country_overview")
async def get_country_overview():
    """Récupérer une vue d'ensemble des statistiques par pays"""
    try:
        data = analyzer.get_roaming_data()
        if not data:
            raise HTTPException(status_code=404, detail="Aucune donnée trouvée")
        
        # Agréger les données par pays
        country_stats = {}
        for item in data:
            country = item['country']
            if country not in country_stats:
                country_stats[country] = {
                    'country': country,
                    'total_attempts': 0,
                    'success_count': 0,
                    'network_types': set(),
                    'reject_causes': set(),
                    'operator_count': 0,
                    'avg_attach_duration': 0,
                    'samples': 0
                }
            
            stats = country_stats[country]
            stats['total_attempts'] += item['total_attempts']
            stats['success_count'] += item['success_count']
            stats['network_types'].update(item['network_types'])
            stats['reject_causes'].update(item['reject_causes'])
            stats['operator_count'] = max(stats['operator_count'], item['operator_count'])
            stats['avg_attach_duration'] = ((stats['avg_attach_duration'] * stats['samples'] + 
                                          item['avg_attach_duration']) / (stats['samples'] + 1))
            stats['samples'] += 1
        
        # Calculer les taux de succès finaux et convertir les sets en listes
        for stats in country_stats.values():
            stats['success_rate'] = (stats['success_count'] / stats['total_attempts'] * 100) if stats['total_attempts'] > 0 else 0
            stats['network_types'] = list(stats['network_types'])
            stats['reject_causes'] = list(stats['reject_causes'])
            stats['avg_attach_duration'] = round(stats['avg_attach_duration'], 2)
            del stats['samples']
        
        return {"success": True, "data": list(country_stats.values())}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/countries")
async def get_countries():
    """Retourne la liste des pays disponibles au format { value: name, label: name }"""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")

        cursor = connection.cursor()
        query = """
        SELECT DISTINCT a_location_country 
        FROM steeringofroaming 
        WHERE a_location_country IS NOT NULL 
        AND a_location_country != ''
        AND a_location_country != 'a_location_country'
        AND TCName = 'SteeringOfRoaming'
        ORDER BY a_location_country
        """
        cursor.execute(query)
        raw_results = cursor.fetchall()
        
        countries = [{"value": row['a_location_country'], "label": row['a_location_country']} for row in raw_results]
        
        return {"success": True, "data": countries}
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/chart")
async def get_chart():
    """Générer et récupérer le graphique"""
    try:
        data = analyzer.get_roaming_data()
        if not data:
            raise HTTPException(status_code=404, detail="Aucune donnée trouvée")
        
        chart_path = analyzer.create_steering_chart(data)
        return {
            "success": True,
            "chart_url": chart_path,
            "data": data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/network_performance_stats", response_model=Dict[str, Any])
def get_network_performance_stats_endpoint():
    """
    Récupère les statistiques de performance du réseau
    """
    try:
        from ..Kpi_service_clean import get_network_performance_stats
        result = get_network_performance_stats()
        return result
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des statistiques de performance du réseau: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/weeks")
def get_weeks_endpoint():
    """
    Récupère la liste des semaines disponibles dans les données
    """
    try:
        from ..Kpi_service_clean import get_db_connection
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT 
                DISTINCT WEEK(Timestamp) as week,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE Timestamp IS NOT NULL
            GROUP BY WEEK(Timestamp)
            ORDER BY week
        """)
        results = cursor.fetchall()
        connection.close()
        
        return [{"week": row['week'], "count": row['count']} for row in results if row['week']]
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des semaines: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/api/operators")
def get_operators_endpoint():
    """
    Récupère la liste des opérateurs disponibles dans les données
    """
    try:
        from ..Kpi_service_clean import get_db_connection
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor(dictionary=True)
        cursor.execute("""
            SELECT 
                a_UsedPLMNName as operator,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE a_UsedPLMNName IS NOT NULL AND a_UsedPLMNName != ''
            GROUP BY a_UsedPLMNName
            ORDER BY COUNT(*) DESC
        """)
        results = cursor.fetchall()
        connection.close()
        
        return [{"operator": row['operator'], "count": row['count']} for row in results if row['operator']]
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des opérateurs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def analyze_csv_file(file_path: str):
    """
    Fonction d'aide pour analyser un fichier CSV en arrière-plan
    
    Args:
        file_path (str): Chemin vers le fichier CSV à analyser
    """
    try:
        # Exécuter l'analyse
        analyzer.run_analysis(file_path)
    except Exception as e:
        print(f"Erreur lors de l'analyse en arrière-plan: {e}") 