import { useState } from 'react';
import {
    RiDashboardLine,
    RiFileTextLine,
    RiFileUploadLine,
    RiLineChartLine,
    RiLogoutBoxLine,
    RiMenuFoldLine,
    RiMenuUnfoldLine,
    RiSettings4Line,
    RiWifiLine
} from 'react-icons/ri';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

type SidebarLinkProps = {
  to: string;
  icon: React.ReactNode;
  label: string;
  isActive: boolean;
};

const SidebarLink = ({ to, icon, label, isActive }: SidebarLinkProps) => (
  <Link
    to={to}
    className={`flex items-center px-4 py-2 mt-2 rounded-lg transition-colors duration-200 ${
      isActive 
        ? 'bg-primary-500 text-white'
        : 'text-gray-600 hover:bg-primary-50 hover:text-primary-700'
    }`}
  >
    <span className="text-xl">{icon}</span>
    <span className="mx-4 font-medium">{label}</span>
  </Link>
);

const Sidebar = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { logout } = useAuth();
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };

  return (
    <div 
      className={`
        h-screen bg-white border-r border-gray-200 transition-all duration-300 flex flex-col
        ${collapsed ? 'w-16' : 'w-64'}
      `}
    >
      <div className="flex items-center justify-between h-16 px-4 border-b border-gray-200">
        {!collapsed && (
          <h1 className="text-xl font-bold text-primary-700">Roaming Analytics</h1>
        )}
        <button 
          onClick={() => setCollapsed(!collapsed)}
          className="p-1 rounded-md hover:bg-gray-100"
        >
          {collapsed ? <RiMenuUnfoldLine size={20} /> : <RiMenuFoldLine size={20} />}
        </button>
      </div>
      
      <div className="flex-1 flex flex-col overflow-y-auto py-4 px-3">
        <nav className="flex-1">
          <SidebarLink 
            to="/dashboard" 
            icon={<RiDashboardLine />} 
            label={collapsed ? '' : "Dashboard"}
            isActive={isActive('/dashboard')}
          />
          <SidebarLink 
            to="/dashboard/http" 
            icon={<RiWifiLine />} 
            label={collapsed ? '' : "HTTP Dashboard"} 
            isActive={isActive('/dashboard/http')}
          />
          <SidebarLink 
            to="/trends" 
            icon={<RiLineChartLine />} 
            label={collapsed ? '' : "Tendances"} 
            isActive={isActive('/trends')}
          />
          <SidebarLink 
            to="/upload" 
            icon={<RiFileUploadLine />} 
            label={collapsed ? '' : "Upload CSV"} 
            isActive={isActive('/upload')}
          />
          <SidebarLink 
            to="/reports" 
            icon={<RiFileTextLine />} 
            label={collapsed ? '' : "Rapports"} 
            isActive={isActive('/reports')}
          />
          <SidebarLink 
            to="/settings" 
            icon={<RiSettings4Line />} 
            label={collapsed ? '' : "Paramètres"} 
            isActive={isActive('/settings')}
          />
        </nav>
        
        <div className="pt-4 mt-4 border-t border-gray-200">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-2 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-700 transition-colors duration-200"
          >
            <span className="text-xl"><RiLogoutBoxLine /></span>
            {!collapsed && <span className="mx-4 font-medium">Déconnexion</span>}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Sidebar; 
 
 