import {
    BarElement,
    CategoryScale,
    ChartData,
    Chart as ChartJS,
    ChartOptions,
    Legend,
    LinearScale,
    Title,
    Tooltip
} from 'chart.js';
import React from 'react';
import { Bar } from 'react-chartjs-2';

// Enregistrer les composants nécessaires pour Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface StackedBarChartProps {
  data: ChartData<'bar'>;
  options?: ChartOptions<'bar'>;
  width?: number;
  height?: number;
  className?: string;
}

const StackedBarChart: React.FC<StackedBarChartProps> = ({ 
  data, 
  options = {}, 
  width, 
  height,
  className = ''
}) => {
  // Options par défaut pour les graphiques empilés
  const defaultOptions: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          label: function(context) {
            const label = context.dataset.label || '';
            const value = context.parsed.y;
            return `${label}: ${value.toFixed(1)}%`;
          }
        }
      },
      legend: {
        position: 'top',
        labels: {
          font: {
            size: 12
          }
        }
      }
    },
    scales: {
      x: {
        type: 'category',
        stacked: true,
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11
          },
          maxRotation: 45,
          minRotation: 45
        }
      },
      y: {
        type: 'linear',
        stacked: true,
        beginAtZero: true,
        max: 100,
        grid: {
          color: 'rgba(0,0,0,0.1)'
        },
        ticks: {
          callback: function(value) {
            return value + '%';
          }
        }
      }
    },
    animation: {
      duration: 750
    }
  };

  // Fusionner les options par défaut avec les options fournies
  const chartOptions: ChartOptions<'bar'> = {
    ...defaultOptions,
    ...options,
    plugins: {
      ...defaultOptions.plugins,
      ...(options.plugins || {})
    }
  };

  return (
    <div className={`w-full h-full ${className}`}>
      <Bar 
        data={data} 
        options={chartOptions} 
        width={width} 
        height={height}
      />
    </div>
  );
};

export default StackedBarChart;