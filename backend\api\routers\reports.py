from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
import os
import pandas as pd
from datetime import datetime
from typing import Optional, List, Dict, Any
import uuid

from ..services.csv_processing import read_csv, filter_data, calculate_cost, detect_anomalies
from ..services import report_generator
from ..models import User, ReportRequest, ReportResponse, ReportInfo
from ..routers.auth import get_current_user
from ..utils.data_processing import generate_report, load_data
from .files import processed_files_db, REPORTS_DIR
from ..services import smart_report_generator
from ..routers.smart_report import ReportParams

router = APIRouter(prefix="/reports", tags=["rapports"])

# Base de données simulée pour les rapports
reports_db = {}

@router.post("/", response_model=ReportResponse)
async def generate_report(
    report_request: ReportRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Génère un rapport (Excel, CSV, PDF) à partir des données.
    """
    file_id = report_request.file_id
    
    # Vérifier si le fichier existe
    if file_id not in processed_files_db:
        raise HTTPException(
            status_code=404,
            detail="Fichier non trouvé"
        )
    
    file_info = processed_files_db[file_id]
    
    # Vérifier les droits d'accès
    if file_info["uploader"] != current_user.username:
        raise HTTPException(
            status_code=403,
            detail="Accès non autorisé à ce fichier"
        )
    
    try:
        # Lire le fichier CSV
        df = read_csv(file_info["path"])
        
        # Appliquer les filtres
        filtered_df = filter_data(
            df,
            country=report_request.country,
            operator=report_request.operator,
            start_date=report_request.start_date,
            end_date=report_request.end_date
        )
        
        # Calculer les coûts
        filtered_df = calculate_cost(filtered_df)
        
        # Détecter les anomalies si demandé
        if report_request.include_anomalies:
            filtered_df, _ = detect_anomalies(filtered_df)
        
        # Nom unique pour le rapport
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_name = f"report_{file_id}_{timestamp}"
        
        # Générer le rapport selon le type demandé
        if report_request.report_type.lower() == "excel":
            # Utiliser xlsxwriter pour générer un rapport Excel
            report_path = os.path.join(REPORTS_DIR, f"{report_name}.xlsx")
            with pd.ExcelWriter(report_path) as writer:
                filtered_df.to_excel(writer, sheet_name="Données", index=False)
                
                # Ajouter une feuille de statistiques
                stats_df = pd.DataFrame([{"paramètre": key, "valeur": value} 
                                        for key, value in file_info["stats"].items()])
                stats_df.to_excel(writer, sheet_name="Statistiques", index=False)
                
        elif report_request.report_type.lower() == "csv":
            # Générer un CSV simple
            report_path = os.path.join(REPORTS_DIR, f"{report_name}.csv")
            filtered_df.to_csv(report_path, index=False)
            
        elif report_request.report_type.lower() == "pdf":
            # Utiliser le module de génération de PDF
            report_path = report_generator.generate_pdf_report(
                filtered_df,
                title=f"Rapport de Roaming - {file_info['original_filename']}",
                description=f"Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Type de rapport non supporté: {report_request.report_type}"
            )
        
        # Retourner les informations sur le rapport généré
        return {
            "file_id": file_id,
            "report_filename": os.path.basename(report_path),
            "report_type": report_request.report_type,
            "rows_included": len(filtered_df),
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Erreur lors de la génération du rapport: {str(e)}"
        )
        
@router.get("/download/{filename}")
async def download_report(
    filename: str,
    current_user: User = Depends(get_current_user)
):
    """
    Télécharge un rapport généré précédemment.
    """
    report_path = os.path.join(REPORTS_DIR, filename)
    
    # Vérifier si le fichier existe
    if not os.path.exists(report_path):
        raise HTTPException(
            status_code=404,
            detail="Rapport non trouvé"
        )
    
    # Déterminer le type MIME en fonction de l'extension
    media_type = None
    if filename.endswith(".pdf"):
        media_type = "application/pdf"
    elif filename.endswith(".xlsx"):
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    elif filename.endswith(".csv"):
        media_type = "text/csv"
    else:
        media_type = "application/octet-stream"
    
    return FileResponse(
        path=report_path,
        filename=filename,
        media_type=media_type
    )

@router.get("/")
async def list_reports(current_user: User = Depends(get_current_user)):
    """
    Liste tous les rapports disponibles.
    """
    reports = []
    
    # Parcourir le répertoire des rapports
    for filename in os.listdir(REPORTS_DIR):
        file_path = os.path.join(REPORTS_DIR, filename)
        
        # Ne lister que les fichiers (pas les dossiers)
        if os.path.isfile(file_path):
            # Extraire l'ID du fichier à partir du nom du rapport (format: report_<file_id>_<timestamp>.<ext>)
            parts = filename.split('_')
            file_id = parts[1] if len(parts) > 1 else None
            
            # Extraire le type de rapport
            extension = filename.split('.')[-1] if '.' in filename else None
            
            # Vérifier si l'utilisateur a accès au fichier d'origine
            has_access = False
            if file_id in processed_files_db:
                if processed_files_db[file_id]["uploader"] == current_user.username:
                    has_access = True
            
            # Ajouter le rapport à la liste si l'utilisateur y a accès
            if has_access:
                reports.append({
                    "filename": filename,
                    "file_id": file_id,
                    "report_type": extension,
                    "size_kb": round(os.path.getsize(file_path) / 1024, 2),
                    "created_at": datetime.fromtimestamp(os.path.getctime(file_path)).isoformat()
                })
    
    return {"reports": reports}

@router.get("/reports/", response_model=List[ReportInfo])
async def list_reports_db(current_user: User = Depends(get_current_user)):
    """Lister tous les rapports disponibles"""
    # Ici, vous devriez récupérer les rapports depuis une base de données
    # Pour cet exemple, on simule une liste de rapports
    
    reports = []
    
    # Lister les fichiers dans le dossier des rapports
    for filename in os.listdir(REPORTS_DIR):
        if filename.endswith('.pdf'):
            file_path = os.path.join(REPORTS_DIR, filename)
            
            # Extraire l'ID et le type depuis le nom du fichier
            parts = filename.replace('.pdf', '').split('_')
            if len(parts) >= 3 and parts[0] == 'report':
                file_id = parts[1]
                report_type = '_'.join(parts[2:])
                
                # Créer un objet rapport
                report = ReportInfo(
                    id=str(uuid.uuid4()),
                    file_id=file_id,
                    report_type=report_type,
                    path=file_path,
                    created_at=datetime.fromtimestamp(os.path.getctime(file_path)),
                    size=os.path.getsize(file_path),
                    user_id=current_user.id
                )
                
                reports.append(report)
    
    return reports

@router.post("/reports/", response_model=ReportInfo)
async def generate_new_report(
    file_id: str,
    report_type: str,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user)
):
    """Générer un nouveau rapport"""
    # Vérifier si le fichier existe
    file_path = f"uploads/{file_id}.csv"  # Supposons que c'est un CSV
    if not os.path.exists(file_path):
        file_path = f"uploads/{file_id}.xlsx"  # Essayons XLSX
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Fichier non trouvé"
            )
    
    try:
        # Générer un ID unique pour le rapport
        report_id = str(uuid.uuid4())
        
        # Chemin du rapport
        report_path = os.path.join(REPORTS_DIR, f"report_{file_id}_{report_type}.pdf")
        
        # Charger les données
        df = load_data(file_path)
        
        # Générer le rapport en arrière-plan
        background_tasks.add_task(generate_report, df, file_id, report_type, report_path)
        
        # Créer l'objet rapport
        report = ReportInfo(
            id=report_id,
            file_id=file_id,
            report_type=report_type,
            path=report_path,
            created_at=datetime.now(),
            size=0,  # Sera mis à jour une fois le rapport généré
            user_id=current_user.id
        )
        
        # Enregistrer dans la base de données simulée
        reports_db[report_id] = report
        
        return report
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la génération du rapport: {str(e)}"
        )

@router.post("/auto-generate", response_model=ReportResponse)
async def auto_generate_report(
    background_tasks: BackgroundTasks,
    report_request: ReportRequest,
    current_user: User = Depends(get_current_user)
):
    """
    Génère automatiquement un rapport à partir des données de la base de données
    sans nécessiter un fichier préalablement téléchargé.
    """
    try:
        from ..Kpi_service_clean import get_network_performance_stats, get_steering_success_by_country
        
        # Générer un ID unique pour le rapport
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_id = str(uuid.uuid4())
        report_name = f"auto_report_{report_id}_{timestamp}"
        
        # Récupérer les données directement de la base de données
        network_stats = get_network_performance_stats()
        steering_stats = get_steering_success_by_country()
        
        # Créer un DataFrame à partir des données récupérées
        data_frames = []
        
        # Ajouter les données par pays/opérateur
        if network_stats and 'by_country_operator' in network_stats:
            df_country = pd.DataFrame(network_stats['by_country_operator'])
            if not df_country.empty:
                data_frames.append(('Données par pays/opérateur', df_country))
        
        # Ajouter les données par période (jour, semaine, mois, année)
        for period_type, key in [('jour', 'by_date'), ('semaine', 'by_week'), 
                               ('mois', 'by_month'), ('année', 'by_year')]:
            if network_stats and key in network_stats and network_stats[key]:
                df_period = pd.DataFrame(network_stats[key])
                if not df_period.empty:
                    data_frames.append((f'Données par {period_type}', df_period))
        
        # Ajouter les données de steering
        if steering_stats:
            df_steering = pd.DataFrame(steering_stats)
            if not df_steering.empty:
                data_frames.append(('Données de steering', df_steering))
        
        # Si aucune donnée n'est disponible
        if not data_frames:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Aucune donnée disponible pour générer le rapport"
            )
        
        # Générer le rapport selon le type demandé
        report_path = ""
        if report_request.report_type.lower() == "excel":
            report_path = os.path.join(REPORTS_DIR, f"{report_name}.xlsx")
            
            # Utiliser la fonction generate_excel_report pour créer un Excel
            from ..services.report_generator import generate_excel_report
            
            # Combiner tous les DataFrames en un seul pour le rapport Excel
            combined_df = pd.DataFrame()
            for name, df in data_frames:
                if not df.empty:
                    # Ajouter une colonne pour identifier la source des données
                    df['source'] = name
                    if combined_df.empty:
                        combined_df = df
                    else:
                        # Essayer de combiner les DataFrames, même s'ils ont des colonnes différentes
                        try:
                            combined_df = pd.concat([combined_df, df], ignore_index=True)
                        except Exception as e:
                            print(f"Erreur lors de la concaténation de {name}: {str(e)}")
            
            if not combined_df.empty:
                report_path = generate_excel_report(
                    combined_df,
                    title=f"Rapport Automatique de Roaming - {timestamp}",
                    description=f"Généré automatiquement le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Impossible de générer le rapport Excel: aucune donnée disponible"
                )
        elif report_request.report_type.lower() == "csv":
            report_path = os.path.join(REPORTS_DIR, f"{report_name}.csv")
            
            # Utiliser la fonction generate_csv_report pour créer un CSV
            from ..services.report_generator import generate_csv_report
            
            print(f"Préparation des données pour le rapport CSV")
            # Combiner tous les DataFrames en un seul pour le rapport CSV
            combined_df = pd.DataFrame()
            for name, df in data_frames:
                print(f"Traitement du DataFrame '{name}' avec {len(df)} lignes")
                if not df.empty:
                    try:
                        # Ajouter une colonne pour identifier la source des données
                        df['source'] = name
                        if combined_df.empty:
                            combined_df = df
                            print(f"Premier DataFrame ajouté: {len(combined_df)} lignes")
                        else:
                            # Essayer de combiner les DataFrames, même s'ils ont des colonnes différentes
                            print(f"Concaténation avec le DataFrame existant ({len(combined_df)} lignes)")
                            print(f"Colonnes du DataFrame existant: {combined_df.columns.tolist()}")
                            print(f"Colonnes du nouveau DataFrame: {df.columns.tolist()}")
                            combined_df = pd.concat([combined_df, df], ignore_index=True)
                            print(f"Résultat de la concaténation: {len(combined_df)} lignes")
                    except Exception as e:
                        print(f"Erreur lors de la concaténation de {name}: {str(e)}")
                        import traceback
                        print(traceback.format_exc())
            
            print(f"Génération du rapport CSV avec {len(combined_df)} lignes et {len(combined_df.columns) if not combined_df.empty else 0} colonnes")
            if not combined_df.empty:
                report_path = generate_csv_report(
                    combined_df,
                    title=f"Rapport Automatique de Roaming - {timestamp}",
                    description=f"Généré automatiquement le {datetime.now().strftime('%d/%m/%Y à %H:%M')}"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Impossible de générer le rapport CSV: aucune donnée disponible"
                )
        elif report_request.report_type.lower() == "pdf":
            # Construire les paramètres pour le smart report
            filt = report_request.filters if report_request.filters else None
            periode_val = "mois"
            pays_val = "all"
            operateur_val = "all"
            if filt:
                # Si un intervalle de dates précis est fourni, on passe "jour" pour plus de granularité
                if filt.start_date or filt.end_date:
                    periode_val = "jour"
                if filt.countries:
                    pays_val = filt.countries[0]
                if filt.operators:
                    operateur_val = filt.operators[0]
            smart_params = ReportParams(
                periode=periode_val.capitalize(),
                pays=pays_val,
                operateur=operateur_val,
                type_donnees="all",
                format="pdf"
            )

            report_path = smart_report_generator.generate_smart_report(smart_params)

            if not report_path or not os.path.exists(report_path):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Échec de la génération du rapport PDF intelligent"
                )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Type de rapport non supporté: {report_request.report_type}"
            )
        
        # Retourner les informations sur le rapport généré
        return {
            "report_url": f"/reports/download/{os.path.basename(report_path)}",
            "report_type": report_request.report_type,
            "date_generated": datetime.now(),
            "file_size": os.path.getsize(report_path) if os.path.exists(report_path) else 0,
            "message": "Rapport généré avec succès à partir des données de la base de données"
        }
        
    except Exception as e:
        import traceback
        print(f"Erreur détaillée lors de la génération automatique du rapport: {str(e)}")
        print(traceback.format_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la génération automatique du rapport: {str(e)}"
        )

@router.get("/reports/{report_id}")
async def download_report_db(report_id: str, current_user: User = Depends(get_current_user)):
    """Télécharger un rapport"""
    # Vérifier si le rapport existe dans notre base de données simulée
    if report_id in reports_db:
        report = reports_db[report_id]
        file_path = report.path
    else:
        # Essayer de trouver le rapport par son ID dans le dossier des rapports
        file_path = None
        for filename in os.listdir(REPORTS_DIR):
            if filename.startswith(f"report_{report_id}") and filename.endswith('.pdf'):
                file_path = os.path.join(REPORTS_DIR, filename)
                break
        
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Rapport non trouvé"
            )
    
    try:
        # Vérifier si le fichier existe
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Fichier de rapport non trouvé"
            )
            
        return FileResponse(
            path=file_path,
            filename=os.path.basename(file_path),
            media_type="application/pdf"
        )
    
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors du téléchargement du rapport: {str(e)}"
        )