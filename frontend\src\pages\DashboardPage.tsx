import { BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import DynamicFilter from '../components/DynamicFilter';
import FailureDetailsTable from '../components/FailureDetailsTable';
import NetworkPerformanceStatsComponent from '../components/NetworkPerformanceStats';
import type { NetworkPerformanceStats } from './../services/kpiService';
import {
    AttachmentRatesAPIResponse,
    ChartJsData,
    getAttachmentRatesByCountry as getAttachmentRatesByCountryFromKpi,
    getAvailableCountriesAndOperators,
    getNetworkPerformanceStats,
    getOperatorsByCountry,
    getPeriodData,
    getSteeringSuccessByCountry,
    getWeeklyAttachTrend,
    getWeeks,
    KpiData,
    KpiTrendData,
    PieChartData
} from './../services/kpiService';

// URL de base de l'API
const API_BASE_URL = 'http://localhost:8000/api';

// Enregistrer les composants de Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend, LineElement, PointElement);

// Type pour les données des graphiques
type ChartData<TType = 'bar', TData = number[], TLabel = string> = {
  labels: TLabel[];
  datasets: {
    label: string;
    data: TData;
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
    type?: TType;
  }[];
};

// Type pour les données des APIs
interface SteeringSuccessDataAPI {
  a_location_country: string;
  success_rate: number;
}

interface AttachmentRatesDataAPI {
  a_location_country: string;
  attache0?: number;
  attache1?: number;
  attache2?: number;
  attache3?: number;
  attache4?: number;
  attache5?: number;
  attache6?: number;
  attach_success_rate?: number;
  overall_success_rate?: number;
  country?: string;
}

// Interface pour les options retournées par l'API (pays et opérateurs)
interface FilterOption {
  value: string;
  label: string;
}

// Interface type attendu par DynamicFilter
interface FilterItemOption {
  id: string;
  name: string;
}

interface WeekData {
  id: string;
  name: string;
}

// Type pour les données retournées par getPeriodData (basé sur l'utilisation)
interface PeriodKpiData {
  kpis: KpiData;
  kpiTrend: KpiTrendData;
  barChart: ChartJsData;
  pieChart: PieChartData;
}

// Interface KpiData est importée depuis kpiService

// KpiTrendData est importée depuis kpiService

// PieChartData est importée depuis kpiService

// Type pour la prop initialValues de DynamicFilter
interface DynamicFilterInitialValues {
    startDate: string;
    endDate: string;
}

// Type pour l'état complet des filtres (retourné par DynamicFilter via onFilterChange)
// NOTE: Cette interface DOIT correspondre à la structure de l'objet que DynamicFilter
// passe à son prop onFilterChange. Basé sur le code de DynamicFilter, cela inclut country, operator, verdict, dateRange, searchQuery.
interface FilterState {
  country: string;
  operator: string;
  verdict: string;
  dateRange: DynamicFilterInitialValues; // Utilise le type défini ci-dessus
  searchQuery: string;
}

// Définir les types pour les données de l'API /api/steering_chart_data
interface SteeringChartAPIData {
  a_location_country: string;
  a_UsedPLMNName: string;
  total_attempts: number;
  successful_attempts: number;
  attachment_success_rate: number;
}

// Définir les types pour les données traitées pour le graphique empilé (basé sur l'API steering_chart_data)
interface ProcessedSteeringData {
  country: string;
  successful: number;
  failed: number;
  total: number;
  successRate: number;
}

// Cache pour les données de steering brutes (utilisé par loadSteeringData)
let cachedSteeringSuccessData: ChartJsData | null = null;
let cachedAttachmentRatesData: ChartJsData | null = null;

// Fonction pour obtenir la couleur du KPI (simplifiée)
const getKpiColor = (value: number): string => {
  if (value > 90) return '#10b981'; // Vert (emerald-500) si KPI > 90%
  if (value > 75) return '#f59e0b'; // Orange (amber-500) si 75% < KPI < 90%
  return '#ef4444';                 // Rouge (red-500) si KPI < 75%
};

// Composant pour afficher un graphique de ligne avec des points colorés selon les seuils de KPI
const LineChartWithColoredPoints: React.FC<{
  data: { country: string; success_rate: number }[];
  height?: number;
}> = ({ data, height = 400 }) => {
  // Trier les données par ordre alphabétique des pays
  const sortedData = [...data].sort((a, b) => a.country.localeCompare(b.country));
  
  return (
    <div className="relative" style={{ height: `${height}px` }}>
      <svg width="100%" height="100%" viewBox={`0 0 ${sortedData.length * 40 + 80} ${height}`} preserveAspectRatio="xMidYMid meet">
        {/* Grille horizontale */}
        {[0, 25, 50, 75, 100].map((value) => {
          const y = height - (value / 100 * (height - 80)) - 40;
          return (
            <g key={`grid-${value}`}>
              <line 
                x1="60" 
                y1={y} 
                x2={sortedData.length * 40 + 60} 
                y2={y} 
                stroke="#e5e7eb" 
                strokeWidth="1" 
              />
              <text 
                x="50" 
                y={y + 5} 
                textAnchor="end" 
                fontSize="12" 
                fill="#6b7280"
              >
                {value}%
              </text>
            </g>
          );
        })}
        
        {/* Axe X */}
        <line 
          x1="60" 
          y1={height - 40} 
          x2={sortedData.length * 40 + 60} 
          y2={height - 40} 
          stroke="#9ca3af" 
          strokeWidth="1" 
        />
        
        {/* Axe Y */}
        <line 
          x1="60" 
          y1="20" 
          x2="60" 
          y2={height - 40} 
          stroke="#9ca3af" 
          strokeWidth="1" 
        />
        
        {/* Titre de l'axe Y */}
        <text 
          x="20" 
          y="15" 
          textAnchor="middle" 
          fontSize="12" 
          fill="#000000" 
          transform="rotate(-90, 15, 180)"
        >
          Taux de succès (%)
        </text>
        
        {/* Points et lignes */}
        <polyline
          points={sortedData.map((item, index) => {
            const x = index * 40 + 80;
            const y = height - (item.success_rate / 100 * (height - 80)) - 40;
            return `${x},${y}`;
          }).join(' ')}
          fill="none"
          stroke="black"
          strokeWidth="2"
        />
        
        {/* Points colorés selon les seuils de KPI */}
        {sortedData.map((item, index) => {
          const x = index * 40 + 80;
          const y = height - (item.success_rate / 100 * (height - 80)) - 40;
          const color = getKpiColor(item.success_rate);
          
          return (
            <g key={`point-${index}`}>
              {/* Point coloré */}
              <circle 
                cx={x} 
                cy={y} 
                r="6" 
                fill={color}
                stroke="white"
                strokeWidth="1"
              />
              
              {/* Étiquette du pays (rotation pour économiser de l'espace) */}
              <text 
                x={x} 
                y={height - 20} 
                textAnchor="end" 
                fontSize="10" 
                fill="#374151"
                transform={`rotate(-45, ${x}, ${height - 20})`}
              >
                {item.country}
              </text>
              
              {/* Valeur du KPI */}
              <text 
                x={x} 
                y={y - 10} 
                textAnchor="middle" 
                fontSize="10" 
                fill="#374151"
              >
                {item.success_rate}%
              </text>
            </g>
          );
        })}
      </svg>
    </div>
  );
};

// Ajouter l'interface pour les détails d'échec
interface FailureDetail {
  errorText: string;
  a_LupRejectDuration: number;
  a_NrOfLupRejects_All_VPLMN: number;
  a_NrOfPlmnsRejected: number;
  a_RejectCauses: string;
  a_RejectedPLMNs: string;
  b_LupRejectDuration: number;
  b_NrOfPlmnsRejected: number;
}

// Type pour la référence du composant NetworkPerformanceStats
type NetworkPerformanceRef = {
  setSelectedTimePeriod: (period: 'day' | 'week' | 'month' | 'quarter' | 'year' | 'all') => void;
};

// Définir les types pour les données de steering
interface SteeringChartData {
  country: string;
  success_rate: number;
  total_attempts: number;
  failures: number;
}

// Définition du composant DashboardPage
const DashboardPage: React.FC = () => {
  // Ref pour le chargement des données de steering
  const steeringDataLoadedRef = useRef<boolean>(false);
  
  // Ref pour le composant NetworkPerformanceStats
  const networkPerformanceRef = useRef<NetworkPerformanceRef>(null);
  
  // États pour la gestion du temps
  const [lastRefreshTime, setLastRefreshTime] = useState<string>('');
  
  // États pour les données de steering
  const [loadingSteeringData, setLoadingSteeringData] = useState<boolean>(true);
  const [steeringError, setSteeringError] = useState<string | null>(null);
  const [steeringSuccessData, setSteeringSuccessData] = useState<ChartJsData | null>(null);
  const [attachmentRatesData, setAttachmentRatesData] = useState<AttachmentRatesAPIResponse | null>(null);
  const [filteredSteeringSuccessData, setFilteredSteeringSuccessData] = useState<any[]>([]);
  const [filteredAttachmentRatesData, setFilteredAttachmentRatesData] = useState<any[]>([]);
  
  // États pour les détails des échecs
  const [failureDetails, setFailureDetails] = useState<any[]>([]);
  const [failureCauses, setFailureCauses] = useState<any>({
    reject_causes: {},
    rejected_plmns: {},
    top_failures: {},
    details: []
  });
  const [loadingFailureDetails, setLoadingFailureDetails] = useState<boolean>(false);

  // États pour les graphiques de succès
  const [loadingSuccessChart, setLoadingSuccessChart] = useState<boolean>(true);
  const [errorSuccessChart, setErrorSuccessChart] = useState<string | null>(null);
  const [successChartData, setSuccessChartData] = useState<ChartJsData | null>(null);

  // États pour les graphiques empilés
  const [loadingStackedChart, setLoadingStackedChart] = useState<boolean>(false);
  const [errorStackedChart, setErrorStackedChart] = useState<string | null>(null);
  const [stackedChartData, setStackedChartData] = useState<ChartJsData>({ labels: [], datasets: [] });
  
  // État pour les KPIs d'attachement
  const [attachmentKpi, setAttachmentKpi] = useState<{
    total_countries: number;
    total_tests: number;
    avg_success_rate: number;
  }>({
    total_countries: 0,
    total_tests: 0,
    avg_success_rate: 0
  });

  // État pour les données de performance réseau
  const [networkPerformanceData, setNetworkPerformanceData] = useState<NetworkPerformanceStats | null>(null);
  const [loadingNetworkData, setLoadingNetworkData] = useState<boolean>(true);

  // États pour les données des autres KPIs et graphiques
  const [currentPeriod, setCurrentPeriod] = useState<string>('week');
  const [weeks, setWeeks] = useState<WeekData[]>([]);
  const [selectedWeek, setSelectedWeek] = useState<string>('');
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [selectedQuarter, setSelectedQuarter] = useState<string>('');
  const [selectedYear, setSelectedYear] = useState<string>('');
  const [isLoadingKpiData, setIsLoadingKpiData] = useState<boolean>(true);
  const [kpiData, setKpiData] = useState<PeriodKpiData | null>(null);
  const [kpiTrendData, setKpiTrendData] = useState<KpiTrendData>({ labels: [], datasets: [] });
  const [barChartData, setBarChartData] = useState<ChartJsData>({ labels: [], datasets: [] });
  const [pieChartData, setPieChartData] = useState<PieChartData>({ labels: [], values: [], colors: [] });

  // États pour les filtres actifs (géré par DynamicFilter)
  const [activeFilters, setActiveFilters] = useState<FilterState>({
    country: 'all',
    operator: 'all',
    verdict: 'all',
    dateRange: { startDate: '', endDate: '' },
    searchQuery: '',
  });

  // Ajout d'un state pour les pays et opérateurs dynamiques
  const [availableCountries, setAvailableCountries] = useState<FilterItemOption[]>([]);
  const [availableOperators, setAvailableOperators] = useState<FilterItemOption[]>([]);

  // Fonction pour charger les filtres disponibles (pays et opérateurs)
  const fetchAvailableFilters = async () => {
    try {
      const { success, countries, operators, message } = await getAvailableCountriesAndOperators();
      
      if (!success) {
        throw new Error(message || 'Erreur lors de la récupération des filtres');
      }
      
      // L'API retourne { value: string, label: string }
      // DynamicFilter attend { id: string, name: string }
      const countryOptions: FilterItemOption[] = (countries as any[]).map(country => ({
        id: country.value,
        name: country.label
      }));
      
      const operatorOptions: FilterItemOption[] = (operators as any[]).map(operator => ({
        id: operator.value,
        name: operator.label
      }));
      
      // Ne pas ajouter 'Tous les pays' ici, DynamicFilter le gère
      setAvailableCountries(countryOptions);
      setAvailableOperators(operatorOptions);
      
    } catch (error) {
      console.error('Erreur lors de la récupération des filtres disponibles:', error);
      setAvailableCountries([]); // Fallback à une liste vide
      setAvailableOperators([]);
    }
  };

  // Correction des types implicites dans applyFiltersToSteeringData
  const applyFiltersToSteeringData = (successData: any[], attachmentData: any[], filters: FilterState): { steeringData: any[], attachmentData: any[] } => {
    console.log("Applying filters:", filters);

    const filteredSuccessData = successData.filter((item: any) => {
      if (filters.country !== 'all' && item.a_location_country !== filters.country) {
        return false;
      }
      if (filters.operator !== 'all' && item.a_UsedPLMNName !== filters.operator) {
        return false;
      }
      // if (filters.verdict !== 'all' && item.Verdict !== filters.verdict) { return false; }
      return true;
    });

    const filteredAttachmentData = attachmentData.filter((item: any) =>
      (filters.country === 'all' || item.a_location_country === filters.country) &&
      (filters.operator === 'all' || item.a_UsedPLMNName === filters.operator)
    );

    return { steeringData: filteredSuccessData, attachmentData: filteredAttachmentData };
  };

  // Fonction pour charger les semaines (si currentPeriod est 'week')
  const fetchWeeks = async () => {
    try {
      const weeks = await getWeeks();
      setWeeks(weeks);
    } catch (error) {
      console.error('Erreur lors de la récupération des semaines:', error);
    }
  };

  // Fonction pour charger les données KPI par semaine sélectionnée
  const fetchKpiDataByWeek = async (weekId: string) => {
    setIsLoadingKpiData(true);
    try {
      const data = await getWeeklyAttachTrend();
      setKpiData(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des données KPI:', error);
    } finally {
      setIsLoadingKpiData(false);
    }
  };

  // Fonction générique pour charger les données KPI par période (month, quarter, year)
  const getDataFromDatabase = async (period: string) => {
    setIsLoadingKpiData(true);
    try {
      const data = await getPeriodData(period);
      setKpiData(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des données de la base:', error);
    } finally {
      setIsLoadingKpiData(false);
    }
  };

  // Fonction pour charger les données de steering
  const loadSteeringData = async () => {
    try {
      const [successData, attachmentData] = await Promise.all([
        getSteeringSuccessByCountry(),
        getAttachmentRatesByCountryFromKpi()
      ]);
      
      // Transformer les données pour le graphique
      const transformedData = successData.map((item: { country: string; success_rate: number }) => ({
        country: item.country,
        success_rate: item.success_rate
      }));
      
      const chartData: ChartJsData = {
        labels: transformedData.map(item => item.country),
        datasets: [{
          label: 'Taux de succès',
          data: transformedData.map(item => item.success_rate),
          backgroundColor: transformedData.map(item => getKpiColor(item.success_rate))
        }]
      };
      
      setSteeringSuccessData(chartData);
      setAttachmentRatesData(attachmentData);
    } catch (error) {
      console.error('Erreur lors du chargement des données de steering:', error);
      setSteeringSuccessData({ labels: [], datasets: [] });
      setAttachmentRatesData(null);
    } finally {
      setLoadingSteeringData(false);
    }
  };

  const fetchAndProcessChartData = async () => {
    setLoadingSuccessChart(true);
    setLoadingStackedChart(true);
    setErrorSuccessChart(null);
    setErrorStackedChart(null);

    try {
      // 1. Fetch data for "Taux de succès par pays"
      const successParams = new URLSearchParams();
      if (activeFilters.country && activeFilters.country !== 'all') {
        successParams.append('country', activeFilters.country);
      }
      if (activeFilters.operator && activeFilters.operator !== 'all') {
        successParams.append('operator', activeFilters.operator);
      }
      // Ajout des dates de début et de fin si elles sont définies
      if (activeFilters.dateRange && activeFilters.dateRange.startDate) {
        successParams.append('start_date', activeFilters.dateRange.startDate);
      }
      if (activeFilters.dateRange && activeFilters.dateRange.endDate) {
        successParams.append('end_date', activeFilters.dateRange.endDate);
      }
      const successQueryString = successParams.toString();
      const successUrl = `${API_BASE_URL}/steering_chart_data${successQueryString ? `?${successQueryString}` : ''}`;
      
      const successResponse = await fetch(successUrl);
      if (!successResponse.ok) throw new Error(`Erreur HTTP: ${successResponse.status}`);
      const successResult = await successResponse.json();
      if (!successResult.success || !Array.isArray(successResult.data)) {
        throw new Error(successResult.message || 'Données de succès invalides.');
      }

      // 2. Sort success data and get the definitive country order
      const sortedSuccessData = successResult.data.sort((a: SteeringChartData, b: SteeringChartData) => b.success_rate - a.success_rate);
      const countryOrder = sortedSuccessData.map((item: SteeringChartData) => item.country);
      
      const successChartDataPayload: ChartJsData = {
        labels: countryOrder,
        datasets: [{
          label: 'Taux de succès',
          data: sortedSuccessData.map((item: SteeringChartData) => item.success_rate),
          backgroundColor: sortedSuccessData.map((item: SteeringChartData) => getKpiColor(item.success_rate)),
        }],
      };
      setSuccessChartData(successChartDataPayload);
      setLoadingSuccessChart(false);

      // 3. Fetch data for "Distribution des niveaux d'attachement"
      const attachmentResponse = await getAttachmentRatesByCountryFromKpi(
        activeFilters.country !== 'all' ? activeFilters.country : undefined,
        activeFilters.operator !== 'all' ? activeFilters.operator : undefined,
        activeFilters.dateRange.startDate || undefined,
        activeFilters.dateRange.endDate || undefined
      );
      if (!attachmentResponse || !attachmentResponse.success || !attachmentResponse.data) {
        throw new Error(attachmentResponse.message || "Aucune donnée d'attachement reçue.");
      }
      
      let attachmentData = attachmentResponse.data;
      if (activeFilters.country && activeFilters.country !== 'all') {
        attachmentData = attachmentData.filter(item => item.country === activeFilters.country);
      }

      // 4. Sort attachment data using the order from the first chart
      const sortedAttachmentData = countryOrder
        .map((country: string) => attachmentData.find((item: any) => item.country === country))
        .filter((item: any): item is NonNullable<typeof item> => !!item); 

      if (attachmentResponse.kpi) {
        setAttachmentKpi(attachmentResponse.kpi);
      }

      const attachmentLabels = ['1 ère attach', '2 ème attach', '3 ème attach', '4 ème attach', '5 ème attach', '6 ème attach', '7 ème attach et plus'];
      const attachmentColors = ['#0EB605FF', '#FFD700', '#80513BFF', '#f59e0b', '#3b82f6', '#1f2937', ' #ef4444'];

      const stackedChartDataPayload: ChartJsData = {
        labels: sortedAttachmentData.map((d: any) => d.country),
        datasets: attachmentLabels.map((label, index) => ({
          label: label,
          data: sortedAttachmentData.map((d: any) => {
            const dist = d.attachment_distribution;
            return (dist && typeof dist === 'object' && typeof dist[String(index)] === 'number') ? dist[String(index)] : 0;
          }),
          backgroundColor: attachmentColors[index],
          borderColor: attachmentColors[index],
          borderWidth: 1,
        })),
      };
      setStackedChartData(stackedChartDataPayload);
      setFilteredAttachmentRatesData(sortedAttachmentData);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error("Erreur lors de la récupération et du traitement des données des graphiques:", errorMessage);
      setErrorSuccessChart(errorMessage);
      setErrorStackedChart(errorMessage);
      setSuccessChartData(null);
      setStackedChartData({ labels: [], datasets: [] });
    } finally {
      setLoadingSuccessChart(false);
      setLoadingStackedChart(false);
    }
  };

  // Fonction pour charger les données de performance réseau
  const fetchNetworkPerformanceData = async () => {
    setLoadingNetworkData(true);
    try {
      const data = await getNetworkPerformanceStats();
      setNetworkPerformanceData(data);
    } catch (error) {
      console.error('Erreur lors de la récupération des données de performance réseau:', error);
    } finally {
      setLoadingNetworkData(false);
    }
  };

  // Charger les semaines (si currentPeriod est 'week') ou les données KPI par période
  useEffect(() => {
    if (currentPeriod === 'week') {
      fetchWeeks();
    } else {
      // Appeler getDataFromDatabase pour les périodes 'month', 'quarter', 'year'
      getDataFromDatabase(currentPeriod);
    }
  }, [currentPeriod]); // Dépendance à currentPeriod

   // Charger les données KPI pour la semaine sélectionnée lorsque selectedWeek change (si currentPeriod est 'week')
  useEffect(() => {
    if (currentPeriod === 'week' && selectedWeek) {
      fetchKpiDataByWeek(selectedWeek);
    }
  }, [selectedWeek, currentPeriod]); // Dépendances à selectedWeek et currentPeriod

  // Met à jour la liste des opérateurs lorsque le pays change
  useEffect(() => {
    const fetchOperators = async () => {
      if (activeFilters.country && activeFilters.country !== 'all') {
        const operators = await getOperatorsByCountry(activeFilters.country);
        const operatorOptions: FilterItemOption[] = (operators as any[]).map(op => ({
          id: op.value,
          name: op.label
        }));
        setAvailableOperators(operatorOptions);
      } else {
        // Si "Tous les pays" est sélectionné, recharger tous les opérateurs
        const { operators } = await getAvailableCountriesAndOperators();
        const operatorOptions: FilterItemOption[] = (operators as any[]).map(op => ({
          id: op.value,
          name: op.label
        }));
        setAvailableOperators(operatorOptions);
      }
    };

    fetchOperators();
  }, [activeFilters.country]);

  // Charger les données des graphiques dynamiques de taux de succès au chargement de la page
  useEffect(() => {
      fetchAndProcessChartData();
      // Recharge les deux graphiques dynamiques à chaque changement de filtre
  }, [activeFilters, currentPeriod, selectedWeek]); // Ajout de selectedWeek comme dépendance

  // Charger les données de performance réseau au chargement de la page
  useEffect(() => {
    fetchNetworkPerformanceData();
  }, [activeFilters, currentPeriod, selectedWeek]); // Ajout des dépendances

  // useEffect pour charger les données initiales
  useEffect(() => {
    if (steeringDataLoadedRef.current) return;
    
    console.log("Chargement des données initiales...");
    
    // Charger les données de steering, de performance réseau et les filtres disponibles
    loadSteeringData();
    fetchNetworkPerformanceData();
    fetchAvailableFilters();
    fetchAndProcessChartData();
    
    steeringDataLoadedRef.current = true;
  }, []); // Dépendance vide pour une exécution unique

  const handleFilterChange = useCallback((filters: FilterState) => {
    console.log("Filtres changés:", filters);
    setActiveFilters(filters);
    
    // Charger immédiatement les données avec les nouveaux filtres
    // sans attendre que le useEffect se déclenche
    setTimeout(() => {
      fetchAndProcessChartData();
      fetchNetworkPerformanceData();
    }, 0);
  }, []); // Aucune dépendance car setActiveFilters est stable

  // --- Rendu du Composant --- //

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-[2000px] mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* En-tête */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Tableau de Bord Roaming</h1>
          <p className="mt-2 text-sm text-gray-600">
            Vue d'overview des activités de roaming et des performances
          </p>
        </div>

        {/* Section Filtres */}
        <div className="bg-white p-6 rounded-lg shadow-md mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Filtres et Contrôles</h2>
          
          {/* Composant DynamicFilter avec les filtres dynamiques */}
          <DynamicFilter
            countries={availableCountries}
            operators={availableOperators}
            onFilterChange={handleFilterChange}
            initialValues={{ startDate: '', endDate: '' }}
          />
        </div>

        {/* Taux de succès par pays */}
        <div className="mt-8" style={{ overflowX: 'auto' }}>
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">Taux de succès par pays</h3>
            {currentPeriod === 'week' && selectedWeek && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour la semaine {selectedWeek.split('-')[1]} de {selectedWeek.split('-')[2]}
              </div>
            )}
            {currentPeriod === 'month' && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour le mois en cours
              </div>
            )}
            {currentPeriod === 'quarter' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par trimestre en cours de développement - Affichage des données globales
              </div>
            )}
            {currentPeriod === 'year' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par année en cours de développement - Affichage des données globales
              </div>
            )}
          </div>
          {loadingSuccessChart ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p className="ml-4 text-gray-600">Chargement du graphique dynamique...</p>
            </div>
          ) : errorSuccessChart ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {errorSuccessChart}
            </div>
          ) : (successChartData?.datasets && successChartData.datasets.length > 0) ? (
            <div style={{ minWidth: Math.max(800, (successChartData.labels?.length || 0) * 60), height: 480 }}>
              <Bar 
                data={successChartData}
                      options={{
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            min: 0,
                            max: 100,
                      title: { display: true, text: 'Taux de succès (%)' }, 
                      grid: { color: 'rgba(0,0,0,0.2)' }
                          },
                          x: {
                      title: { display: true, text: 'Pays' }, 
                      grid: { display: false }, 
                            ticks: {
                              autoSkip: false,
                              font: { size: 11, weight: 'bold' },
                              color: '#000000',
                        maxRotation: 60, 
                        minRotation: 45
                      }
                          },
                        },
                        plugins: {
                          legend: {
                      display: true,
                      position: 'top',
                      labels: {
                        filter: (legendItem) => {
                          // Ne pas afficher la légende pour le dataset des points
                          return legendItem.text !== 'Indicateur de qualité';
                        }
                      }
                          },
                    tooltip: { 
                      enabled: true,
                      callbacks: {
                        afterLabel: (context) => {
                          const value = context.parsed.y;
                          if (value > 90) return 'Qualité: Excellente';
                          if (value > 75) return 'Qualité: Moyenne';
                          return 'Qualité: Insuffisante';
                        }
                      }
                    }
                        },
                  layout: { padding: { left: 10, right: 10, top: 20, bottom: 0 } }
                      }}
                    />
            </div>
          ) : (
            <div className="h-full flex items-center justify-center text-gray-500">
              Aucune donnée disponible pour ce graphique dynamique.
            </div>
          )}
        </div>

        {/* Taux d'attachement par pays */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6 overflow-x-auto">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-bold">Distribution des niveaux d'attachement par pays (Steering Of Roaming)</h3>
            {currentPeriod === 'week' && selectedWeek && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour la semaine {selectedWeek.split('-')[1]} de {selectedWeek.split('-')[2]}
              </div>
            )}
            {currentPeriod === 'month' && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour le mois en cours
              </div>
            )}
            {currentPeriod === 'quarter' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par trimestre en cours de développement - Affichage des données globales
              </div>
            )}
            {currentPeriod === 'year' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par année en cours de développement - Affichage des données globales
            </div>
          )}
        </div>
          {loadingStackedChart ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
              <p className="ml-4 text-gray-600">Chargement du graphique d'attachement...</p>
            </div>
          ) : errorStackedChart ? (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {errorStackedChart}
            </div>
          ) : (stackedChartData?.datasets && stackedChartData.datasets.length > 0) ? (
            <div className="overflow-x-auto" style={{ minWidth: Math.max(800, (stackedChartData.labels?.length || 0) * 80), height: 480 }}>
              <Bar
                data={stackedChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  scales: {
                    x: {
                      stacked: true,
                      title: { display: true, text: 'Pays' }, 
                      grid: { display: false }, 
                      ticks: { 
                        autoSkip: false, 
                        font: { size: 11, weight: 'bold' },
                        color: '#000000',
                        maxRotation: 60,
                        minRotation: 45 
                      }
                    },
                    y: {
                      stacked: true,
                      title: { display: true, text: 'Distribution des tentatives (%)' },
                      grid: { color: 'rgba(0,0,0,0.2)' },
                      min: 0,
                      max: 100
                    }
                  },
                  plugins: {
                    tooltip: {
                      callbacks: {
                        footer: (tooltipItems) => {
                          // Calculer le total pour ce pays
                          const dataIndex = tooltipItems[0].dataIndex;
                          const datasets = tooltipItems[0].chart.data.datasets;
                          const total = datasets.reduce((sum, dataset) => sum + (dataset.data[dataIndex] as number || 0), 0);
                          return `Total: ${total.toFixed(0)}%`;
                        }
                      }
                    },
                    legend: {
                      position: 'top',
                      labels: {
                        font: { size: 10 }
                      }
                    }
                  }
                }}
              />
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              Aucune donnée disponible pour le graphique d'attachement.
            </div>
          )}

          {/* KPIs d'attachement */}
          {attachmentKpi && attachmentKpi.total_countries > 0 && (
            <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Pays analysés</p>
                <p className="text-2xl font-bold">{attachmentKpi.total_countries}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Tests totaux</p>
                <p className="text-2xl font-bold">{attachmentKpi.total_tests.toLocaleString()}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Taux de succès moyen</p>
                <p className="text-2xl font-bold" style={{ color: getKpiColor(attachmentKpi.avg_success_rate) }}>
                  {attachmentKpi.avg_success_rate.toFixed(1)}%
                </p>
              </div>
              </div>
          )}
              </div>

        {/* Tableau des détails d'échec - s'affiche uniquement quand le filtre verdict est "fail" */}
        <FailureDetailsTable 
          country={activeFilters.country !== 'all' ? activeFilters.country : undefined}
          operator={activeFilters.operator !== 'all' ? activeFilters.operator : undefined}
          verdict={activeFilters.verdict}
          show={activeFilters.verdict === 'fail'}
        />

        {/* Graphique de débit HTTP */}
        <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex justify-between items-center mb-4">
            
            {currentPeriod === 'week' && selectedWeek && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour la semaine {selectedWeek.split('-')[1]} de {selectedWeek.split('-')[2]}
              </div>
            )}
            {currentPeriod === 'month' && (
              <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                Données filtrées pour le mois en cours
              </div>
            )}
            {currentPeriod === 'quarter' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par trimestre en cours de développement - Affichage des données globales
            </div>
            )}
            {currentPeriod === 'year' && (
              <div className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                Filtrage par année en cours de développement - Affichage des données globales
            </div>
          )}
          </div>
          <NetworkPerformanceStatsComponent 
            ref={networkPerformanceRef}
          />
          </div>
      </div>
    </div>
  );
};

export default DashboardPage;