import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import os
from typing import Dict, List, Any
from fpdf import FPDF
import tempfile
import xlsxwriter

class RoamingReport(FPDF):
    """Classe personnalisée pour les rapports PDF sur le roaming"""
    
    def header(self):
        self.set_font('Arial', 'B', 15)
        self.cell(0, 10, self.title, 0, 1, 'C')
        self.ln(4)
        self.line(10, 20, 200, 20)
        self.ln(10)
        
    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.cell(0, 10, f'Généré le {datetime.now().strftime("%d/%m/%Y %H:%M")}', 0, 0, 'L')
        self.cell(0, 10, f'Page {self.page_no()}/{{nb}}', 0, 0, 'R')

def generate_pdf_report(df: pd.DataFrame, title: str = "Rapport de Roaming", description: str = "") -> str:
    """
    Génère un rapport PDF à partir des données de roaming.
    """
    reports_dir = "reports"
    os.makedirs(reports_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(reports_dir, f"roaming_report_{timestamp}.pdf")
    
    total_records = len(df)
    
    has_status_column = "statut" in df.columns or "Verdict" in df.columns
    has_country_column = "pays" in df.columns or "a_location_country" in df.columns
    has_operator_column = "operateur" in df.columns or "a_UsedPLMNName" in df.columns
    has_service_column = "type_service" in df.columns
    has_date_column = "date" in df.columns or "Timestamp" in df.columns
    
    success_rate = 0
    if has_status_column:
        status_column = "statut" if "statut" in df.columns else "Verdict"
        success_values = ["success", "PASS"]
        success_count = df[df[status_column].isin(success_values)].shape[0]
        success_rate = (success_count / total_records) * 100 if total_records > 0 else 0
    
    countries = 0
    if has_country_column:
        country_column = "pays" if "pays" in df.columns else "a_location_country"
        countries = df[country_column].nunique()
    
    operators = 0
    if has_operator_column:
        operator_column = "operateur" if "operateur" in df.columns else "a_UsedPLMNName"
        operators = df[operator_column].nunique()
    
    temp_dir = tempfile.mkdtemp()
    
    country_chart_path = ""
    if has_country_column:
        country_column = "pays" if "pays" in df.columns else "a_location_country"
        plt.figure(figsize=(10, 6))
        country_counts = df[country_column].value_counts().head(10)
        sns.barplot(x=country_counts.index, y=country_counts.values)
        plt.title("Top 10 des pays")
        plt.xticks(rotation=45)
        plt.tight_layout()
        country_chart_path = os.path.join(temp_dir, "country_chart.png")
        plt.savefig(country_chart_path)
        plt.close()
    
    service_chart_path = ""
    if has_service_column:
        try:
            print("Vérification des données de type de service:")
            print(f"Colonnes disponibles: {df.columns.tolist()}")
            print(f"Valeurs uniques de type_service: {df['type_service'].unique()}")
            print(f"Nombre de valeurs par type: {df['type_service'].value_counts()}")
            
            plt.figure(figsize=(8, 8))
            service_counts = df["type_service"].value_counts()
            if not service_counts.empty:
                plt.clf()  # Nettoyer la figure précédente
                plt.figure(figsize=(8, 8))
                plt.pie(service_counts.values, labels=service_counts.index, autopct='%1.1f%%')
                plt.title("Répartition par type de service")
                service_chart_path = os.path.join(temp_dir, "service_chart.png")
                plt.savefig(service_chart_path, bbox_inches='tight', dpi=300)
                print(f"Graphique sauvegardé dans: {service_chart_path}")
                plt.close()
            else:
                print("Aucune donnée pour le graphique de type de service")
        except Exception as e:
            print(f"Erreur lors de la génération du graphique de service: {e}")
            import traceback
            traceback.print_exc()
        plt.close()
    
    time_chart_path = ""
    if has_date_column:
        date_column = "date" if "date" in df.columns else "Timestamp"
        if pd.api.types.is_datetime64_any_dtype(df[date_column]):
            plt.figure(figsize=(12, 6))
            df_by_date = df.groupby(df[date_column].dt.date).size()
            df_by_date.plot(kind="line")
            plt.title("Évolution du nombre d'enregistrements")
            plt.tight_layout()
            time_chart_path = os.path.join(temp_dir, "time_chart.png")
            plt.savefig(time_chart_path)
            plt.close()
        else:
            try:
                df[date_column] = pd.to_datetime(df[date_column])
                plt.figure(figsize=(12, 6))
                df_by_date = df.groupby(df[date_column].dt.date).size()
                df_by_date.plot(kind="line")
                plt.title("Évolution du nombre d'enregistrements")
                plt.tight_layout()
                time_chart_path = os.path.join(temp_dir, "time_chart.png")
                plt.savefig(time_chart_path)
                plt.close()
            except Exception as e:
                print(f"Erreur lors de la conversion de la colonne de date : {e}")
    
    pdf = RoamingReport()
    pdf.set_title(title)
    pdf.alias_nb_pages()
    pdf.add_page()
    
    pdf.set_font('Arial', 'B', 16)
    pdf.cell(0, 10, title, 0, 1)
    
    if description:
        pdf.set_font('Arial', '', 12)
        pdf.multi_cell(0, 10, description)
        pdf.ln(5)
    
    pdf.set_font('Arial', 'B', 14)
    pdf.cell(0, 10, "Résumé des données", 0, 1)
    
    pdf.set_font('Arial', '', 12)
    pdf.cell(0, 10, f"Nombre total d'enregistrements: {total_records}", 0, 1)
    if has_status_column:
        pdf.cell(0, 10, f"Taux de réussite: {success_rate:.2f}%", 0, 1)
    if has_country_column:
        pdf.cell(0, 10, f"Nombre de pays: {countries}", 0, 1)
    if has_operator_column:
        pdf.cell(0, 10, f"Nombre d'opérateurs: {operators}", 0, 1)
    pdf.ln(5)
    
    if country_chart_path:
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Répartition par pays", 0, 1)
        pdf.image(country_chart_path, x=10, w=180)
        pdf.ln(5)
    
    if service_chart_path:
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Répartition par type de service", 0, 1)
        pdf.image(service_chart_path, x=10, w=180)
        pdf.ln(5)
    
    if time_chart_path:
        pdf.add_page()
        pdf.set_font('Arial', 'B', 14)
        pdf.cell(0, 10, "Évolution temporelle", 0, 1)
        pdf.image(time_chart_path, x=10, w=180)
    
    pdf.output(output_file)
    return output_file 