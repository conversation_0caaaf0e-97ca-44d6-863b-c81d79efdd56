#!/usr/bin/env python3
"""
Script de test pour vérifier que les filtres de date fonctionnent correctement
"""

import requests
import json
from datetime import datetime, timedelta

# Configuration
API_BASE_URL = "http://localhost:8000/api"

def test_date_filter():
    """Tester le filtrage par date"""
    print("🧪 Test des filtres de date pour le Steering Dashboard")
    print("=" * 60)
    
    # Calculer des dates de test
    today = datetime.now()
    start_date = (today - timedelta(days=30)).strftime('%Y-%m-%d')  # 30 jours avant
    end_date = today.strftime('%Y-%m-%d')  # Aujourd'hui
    
    print(f"📅 Période de test: du {start_date} au {end_date}")
    
    # Test 1: Sans filtres de date
    print("\n1️⃣ Test sans filtres de date")
    try:
        url = f"{API_BASE_URL}/steering_success_by_country_filtered"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès - {len(data.get('data', []))} pays trouvés")
            if data.get('data'):
                print(f"   Premier pays: {data['data'][0].get('country', 'N/A')}")
        else:
            print(f"❌ Erreur {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    
    # Test 2: Avec filtres de date
    print(f"\n2️⃣ Test avec filtres de date ({start_date} à {end_date})")
    try:
        params = {
            'start_date': start_date,
            'end_date': end_date
        }
        url = f"{API_BASE_URL}/steering_success_by_country_filtered"
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès - {len(data.get('data', []))} pays trouvés")
            print(f"   Filtres appliqués: {data.get('filters', {})}")
            if data.get('data'):
                print(f"   Premier pays: {data['data'][0].get('country', 'N/A')}")
                print(f"   Taux de succès: {data['data'][0].get('steering_success_rate', 'N/A')}%")
        else:
            print(f"❌ Erreur {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    
    # Test 3: Avec filtres de date + pays
    print(f"\n3️⃣ Test avec filtres de date + pays (France)")
    try:
        params = {
            'start_date': start_date,
            'end_date': end_date,
            'country': 'France'
        }
        url = f"{API_BASE_URL}/steering_success_by_country_filtered"
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Succès - {len(data.get('data', []))} pays trouvés")
            print(f"   Filtres appliqués: {data.get('filters', {})}")
            if data.get('data'):
                for country_data in data['data']:
                    print(f"   Pays: {country_data.get('country', 'N/A')}")
                    print(f"   Taux de succès: {country_data.get('steering_success_rate', 'N/A')}%")
                    print(f"   Total tests: {country_data.get('total_requests', 'N/A')}")
        else:
            print(f"❌ Erreur {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
    
    # Test 4: Vérifier les données dans la base
    print(f"\n4️⃣ Vérification des données dans la base")
    try:
        # Test de l'endpoint des pays pour voir quels pays sont disponibles
        url = f"{API_BASE_URL}/countries"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and 'data' in data:
                countries = data['data']
            else:
                countries = data
            
            print(f"✅ {len(countries)} pays disponibles dans la base:")
            for i, country in enumerate(countries[:5]):  # Afficher les 5 premiers
                if isinstance(country, dict):
                    print(f"   {i+1}. {country.get('value', country.get('country', 'N/A'))}")
                else:
                    print(f"   {i+1}. {country}")
            if len(countries) > 5:
                print(f"   ... et {len(countries) - 5} autres")
        else:
            print(f"❌ Erreur {response.status_code}: {response.text}")
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")

def test_backend_connection():
    """Tester la connexion au backend"""
    print("\n🔗 Test de connexion au backend")
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend accessible")
            return True
        else:
            print(f"⚠️ Backend répond avec le code {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend non accessible: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test des filtres de date pour GlobalRoamer")
    print("=" * 60)
    
    # Vérifier la connexion au backend
    if test_backend_connection():
        # Lancer les tests
        test_date_filter()
    else:
        print("\n💡 Assurez-vous que le backend est démarré avec:")
        print("   cd backend")
        print("   python -m uvicorn main:app --host 0.0.0.0 --reload")
    
    print("\n" + "=" * 60)
    print("✅ Tests terminés")
