import pymysql
import os
from dotenv import load_dotenv

# Charger les variables d'environnement du fichier .env
load_dotenv()

def get_db_connection():
    """Établit et retourne une connexion à la base de données MySQL."""
    try:
        connection = pymysql.connect(
            host=os.getenv('DB_HOST', 'localhost'),
            user=os.getenv('DB_USER', 'root'),
            password=os.getenv('DB_PASSWORD', ''),
            database=os.getenv('DB_NAME', 'kpi'),
            charset='utf8mb4',
            cursorclass=pymysql.cursors.DictCursor
        )
        return connection
    except pymysql.MySQLError as e:
        print(f"Erreur de connexion à la base de données: {e}")
        return None 