from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import FileResponse
from datetime import datetime
from typing import Optional

from pydantic import BaseModel

from ..models import SessionLocal, SteeringOfRoaming  # SQLAlchemy session and model
from ..services import smart_report_generator

router = APIRouter(tags=["Reports"])

class ReportParams(BaseModel):
    """Schéma de réception des paramètres du rapport envoyé depuis le frontend"""
    periode: Optional[str] = "Mois"
    pays: Optional[str] = "all"
    operateur: Optional[str] = "all"
    type_donnees: Optional[str] = "all"
    format: str = "pdf"

@router.post("/generate-smart-report", response_class=FileResponse)
async def generate_smart_report(params: ReportParams):
    """Génère dynamiquement un rapport PDF basé sur les filtres fournis"""
    try:
        pdf_path = smart_report_generator.generate_smart_report(params)
        if not pdf_path:
            raise HTTPException(status_code=500, detail="Échec de la génération du PDF")

        filename = f"rapport_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        return FileResponse(path=pdf_path, filename=filename, media_type="application/pdf")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e)) 