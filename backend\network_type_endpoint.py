from fastapi import HTTPException
from sqlalchemy import text
from datetime import datetime
from api.get_db_connection import get_db_connection

async def get_network_type_distribution(country: str = None, operator: str = None):
    """
    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming
    """
    try:
        print(f"\n[{datetime.now()}] Appel à get_network_type_distribution avec country={country}, operator={operator}")
        
        # Utiliser le moteur de connexion standard pour accéder à la base de données
        engine = get_db_connection()
        if not engine:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        with engine.connect() as conn:
            # Construction de la requête SQL avec les filtres
            query_parts = [
                "SELECT",
                "    a_NetworkType as network_type,", 
                "    a_location_country as country,",
                "    a_location as location,",
                "    a_UsedPLMNName as operator,",
                "    COUNT(*) as count",
                "FROM steeringofroaming",
                "WHERE", 
                "    TCName = 'SteeringOfRoaming'", 
                "    AND a_NetworkType IS NOT NULL", 
                "    AND a_NetworkType != ''", 
                "    AND Verdict != 'FAIL'"
            ]
            
            params = {}
            
            if country and country != 'all':
                query_parts.append("    AND a_location_country = :country")
                params["country"] = country
                
            if operator and operator != 'all':
                query_parts.append("    AND a_UsedPLMNName = :operator")
                params["operator"] = operator
                
            query_parts.append("GROUP BY network_type, country, location, operator")
            
            query_text = text("\n".join(query_parts))
            
            # Exécution de la requête
            print(f"Exécution de la requête SQL: {query_text}")
            print(f"Avec les paramètres: {params}")
            
            result = conn.execute(query_text, params)
            rows = result.fetchall()
            
            # Organisation des données
            network_types = {}
            countries_by_network = {}
            
            for row in rows:
                network_type = row.network_type
                
                # Comptage des occurrences par type de réseau
                if network_type not in network_types:
                    network_types[network_type] = 0
                network_types[network_type] += row.count
                
                # Organisation des données par pays et opérateur pour chaque type de réseau
                if network_type not in countries_by_network:
                    countries_by_network[network_type] = []
                    
                countries_by_network[network_type].append({
                    'country': row.country,
                    'location': row.location,
                    'operator': row.operator,
                    'count': row.count
                })
        
        return {
            "success": True,
            "message": f"Distribution des types de réseau récupérée avec succès",
            "data": {
                "network_types": network_types,
                "countries_by_network": countries_by_network
            }
        }
    except Exception as e:
        print(f"Erreur dans get_network_type_distribution: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e)) 