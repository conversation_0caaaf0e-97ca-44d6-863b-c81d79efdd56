from fastapi import APIRouter, HTTPException, Query
from typing import Dict, List, Any, Optional
from ..Kpi_service_clean import (
    get_weekly_attach_trend,
    get_annual_trend,
    get_quarterly_trend,
    get_yearly_trend,
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_http_download_performance,
    get_db_connection
)
import logging

router = APIRouter(
    prefix="/api/kpi",
    tags=["kpi"]
)

logger = logging.getLogger(__name__)

@router.get("/weeks")
async def get_weeks():
    """Endpoint pour obtenir les données hebdomadaires"""
    try:
        data = get_weekly_attach_trend()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/period_data")
async def get_period_data(period: str = "month", country: str = None, operator: str = None):
    """
    Endpoint pour obtenir les données par période
    
    - period: La période de temps (week, month, quarter, year)
    - country: Filtrer par pays (optionnel)
    - operator: Filtrer par opérateur (optionnel)
    """
    try:
        if period == "week":
            data = get_weekly_attach_trend(country, operator)
        elif period == "month":
            data = get_annual_trend(country, operator)
        elif period == "quarter":
            data = get_quarterly_trend(country, operator)
        elif period == "year":
            data = get_yearly_trend(country, operator)
        else:
            # Par défaut, utiliser les données mensuelles
            data = get_annual_trend(country, operator)
        
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/steering_success")
async def get_steering_success():
    """Endpoint pour obtenir les données de succès du steering"""
    try:
        data = get_steering_success_by_country()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachment_rates")
async def get_attachment_rates():
    """Endpoint pour obtenir les taux d'attachement"""
    try:
        data = get_attachment_rates_by_country()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/attachment_rates_by_country")
async def get_attachment_rates_by_country_endpoint():
    """Endpoint pour obtenir les taux d'attachement par pays"""
    try:
        data = get_attachment_rates_by_country()
        return {
            "success": True,
            "message": f"{len(data)} pays trouvés",
            "data": data
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/http_download_performance")
def http_download_performance_route():
    """
    Récupère les données de performance de téléchargement HTTP
    """
    try:
        data = get_http_download_performance()
        return data
    except Exception as e:
        logger.error(f"Erreur dans http_download_performance_route: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/countries")
async def get_countries(
    limit: int = None
):
    """
    Récupère la liste des pays disponibles dans les données
    """
    try:
        connection = get_db_connection()
        if not connection:
            logger.error("Impossible de se connecter à la base de données")
            return {
                "success": False,
                "data": [],
                "message": "Impossible de se connecter à la base de données"
            }
        
        cursor = connection.cursor(dictionary=True)
        query = """
            SELECT 
                a_location_country AS value,
                a_location_country AS label,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE TCName = 'SteeringOfRoaming'
            AND a_location_country IS NOT NULL 
            AND a_location_country != ''
            GROUP BY a_location_country
            ORDER BY COUNT(*) DESC
        """
        
        if limit:
            query += f" LIMIT {limit}"
        
        cursor.execute(query)
        results = cursor.fetchall()
        connection.close()
        
        countries = [{"id": row['value'], "name": row['label'], "count": row['count']} for row in results if row['value']]
        
        logger.info(f"Pays récupérés avec succès: {len(countries)} pays trouvés")
        
        return {
            "success": True,
            "data": countries,
            "count": len(countries)
        }
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des pays: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "data": [],
            "message": str(e)
        }

@router.get("/operators")
async def get_operators(
    country: Optional[str] = None,
    limit: int = None
):
    """
    Récupère la liste des opérateurs disponibles dans les données
    """
    try:
        connection = get_db_connection()
        if not connection:
            logger.error("Impossible de se connecter à la base de données")
            return {
                "success": False,
                "data": [],
                "message": "Impossible de se connecter à la base de données"
            }
        
        cursor = connection.cursor(dictionary=True)
        query = """
            SELECT 
                a_UsedPLMNName as value,
                a_UsedPLMNName as label,
                COUNT(*) as count
            FROM steeringofroaming
            WHERE TCName = 'SteeringOfRoaming'
            AND a_UsedPLMNName IS NOT NULL 
            AND a_UsedPLMNName != ''
        """
        
        params = []
        if country and country != 'all':
            query += " AND a_location_country = %s"
            params.append(country)
            
        query += " GROUP BY a_UsedPLMNName ORDER BY COUNT(*) DESC"
        
        if limit:
            query += f" LIMIT {limit}"
        
        cursor.execute(query, params)
        results = cursor.fetchall()
        connection.close()
        
        operators = [{"id": row['value'], "name": row['label'], "count": row['count']} for row in results if row['value']]
        
        logger.info(f"Opérateurs récupérés avec succès: {len(operators)} opérateurs trouvés")
        
        return {
            "success": True,
            "data": operators,
            "count": len(operators)
        }
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des opérateurs: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "data": [],
            "message": str(e)
        } 