#!/usr/bin/env python
"""
Script pour mettre à jour le fichier main.py afin de remplacer l'endpoint get_network_type_distribution
par un appel à la fonction importée de api.network_type_endpoint.
"""

import re
import os

def update_main_file():
    # Chemin du fichier main.py
    main_file_path = 'main.py'
    
    # Vérifier si le fichier existe
    if not os.path.exists(main_file_path):
        print(f"Erreur: Le fichier {main_file_path} n'existe pas.")
        return False
    
    # Lire le contenu du fichier
    with open(main_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Ajouter l'import de la fonction get_network_type_distribution
    import_pattern = r'from api\.get_db_connection import get_db_connection'
    import_replacement = 'from api.get_db_connection import get_db_connection\nfrom api.network_type_endpoint import get_network_type_distribution as get_network_type_distribution_func'
    content = re.sub(import_pattern, import_replacement, content)
    
    # Remplacer l'endpoint get_network_type_distribution
    endpoint_pattern = r'@app\.get\("/api/network_type_distribution"\)\nasync def get_network_type_distribution\(.*?\):\n.*?return {.*?}\n\s*}\s*except.*?}'
    endpoint_replacement = '@app.get("/api/network_type_distribution")\nasync def get_network_type_distribution(country: str = None, operator: str = None):\n    """\n    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming\n    Utilise la fonction importée de api.network_type_endpoint\n    """\n    return await get_network_type_distribution_func(country, operator)'
    
    # Utiliser une approche plus simple pour remplacer l'endpoint
    start_marker = '@app.get("/api/network_type_distribution")'
    end_marker = 'return {'
    
    # Trouver la position de début de l'endpoint
    start_pos = content.find(start_marker)
    if start_pos == -1:
        print("Erreur: Impossible de trouver le début de l'endpoint get_network_type_distribution.")
        return False
    
    # Trouver la position de fin de la fonction
    end_pos = content.find('    except Exception as e:', start_pos)
    if end_pos == -1:
        print("Erreur: Impossible de trouver la fin de l'endpoint get_network_type_distribution.")
        return False
    
    # Trouver la position de la fin de la fonction (après le bloc except)
    next_app_get = content.find('@app.get', end_pos)
    if next_app_get == -1:
        print("Erreur: Impossible de trouver la fin du bloc except.")
        return False
    
    # Extraire le contenu avant et après l'endpoint
    content_before = content[:start_pos]
    content_after = content[next_app_get:]
    
    # Créer le nouvel endpoint
    new_endpoint = '@app.get("/api/network_type_distribution")\nasync def get_network_type_distribution(country: str = None, operator: str = None):\n    """\n    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming\n    Utilise la fonction importée de api.network_type_endpoint\n    """\n    return await get_network_type_distribution_func(country, operator)\n\n'
    
    # Assembler le nouveau contenu
    new_content = content_before + new_endpoint + content_after
    
    # Sauvegarder le contenu mis à jour dans un nouveau fichier
    with open('main_updated.py', 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print("Le fichier main_updated.py a été créé avec succès.")
    print("Pour utiliser ce fichier, renommez-le en main.py ou lancez le serveur avec:")
    print("uvicorn main_updated:app --host 0.0.0.0 --reload")
    
    return True

if __name__ == "__main__":
    update_main_file() 