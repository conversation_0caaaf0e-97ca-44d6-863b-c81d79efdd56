import { ArcElement, BarElement, CategoryScale, Chart as ChartJS, Legend, LinearScale, LineElement, PointElement, Title, Tooltip } from 'chart.js';
import React, { useEffect, useState } from 'react';
import { Pie } from 'react-chartjs-2';
import DynamicFilter from '../components/DynamicFilter';
import { getAvailableCountriesAndOperators, getFailureCauses, getNetworkTypeDistribution, getOperatorsByCountry } from '../services/kpiService';

// Enregistrer les composants de Chart.js
ChartJS.register(CategoryScale, LinearScale, BarElement, LineElement, PointElement, ArcElement, Title, Tooltip, Legend);

// Type pour les données des filtres
interface FilterState {
  country: string;
  operator: string;
  verdict: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  searchQuery: string;
  timestamp?: number; // Ajout d'un timestamp pour forcer le rechargement
}

// Interface personnalisée pour les statistiques de performance réseau
// Cette interface est adaptée pour correspondre aux données que nous utilisons dans ce composant
interface CustomNetworkStats {
  totalConnections: number;
  successfulConnections: number;
  failedConnections: number;
  averageLatency: number;
  packetLoss: number;
  networkTypes: {
    [key: string]: number;
  };
  errorTypes: {
    [key: string]: number;
  };
  successRate: number;
}

// Interface pour les détails des pays et opérateurs par type de réseau
interface NetworkTypeDetails {
  country: string;
  location: string;
  operator: string;
  count: number;
}

// Interface pour les causes d'échec
interface FailureCause {
  cause: string;
  count: number;
  percentage: number;
  countries: { [country: string]: number };
}

const NetworkDashboardPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [availableCountries, setAvailableCountries] = useState<Array<{id: string, name: string}>>([]);
  const [availableOperators, setAvailableOperators] = useState<Array<{id: string, name: string}>>([]);
  const [networkStats, setNetworkStats] = useState<CustomNetworkStats | null>(null);
  const [networkTypeData, setNetworkTypeData] = useState<any>(null);
  const [errorRateData, setErrorRateData] = useState<any>(null);
  const [selectedNetworkType, setSelectedNetworkType] = useState<string | null>(null);
  const [networkTypeDetails, setNetworkTypeDetails] = useState<NetworkTypeDetails[]>([]);
  const [failureCausesData, setFailureCausesData] = useState<any>(null);
  const [loadingFailureCauses, setLoadingFailureCauses] = useState<boolean>(false);
  const [filters, setFilters] = useState<FilterState>({
    country: '',
    operator: '',
    verdict: '',
    dateRange: {
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0]
    },
    searchQuery: '',
    timestamp: Date.now()
  });

  // Fonction pour charger les données de performance réseau
  const loadNetworkData = async () => {
    setIsLoading(true);
    try {
      console.log("Chargement des données avec les filtres:", {
        country: filters.country,
        operator: filters.operator
      });

      // Récupérer les données de type de réseau depuis l'API
      const networkTypeResponse = await getNetworkTypeDistribution(
        filters.country && filters.country !== '' && filters.country !== 'all' ? filters.country : undefined,
        filters.operator && filters.operator !== '' && filters.operator !== 'all' ? filters.operator : undefined
      );

      if (networkTypeResponse.success && networkTypeResponse.data) {
        const { network_types, countries_by_network } = networkTypeResponse.data;

        // Calculer le total des connexions
        const totalConnections = Object.values(network_types).reduce((sum, count) => sum + count, 0);

        // Mettre à jour les statistiques réseau
        const stats: CustomNetworkStats = {
          totalConnections,
          successfulConnections: totalConnections, // Toutes les connexions sont réussies (Verdict != 'FAIL')
          failedConnections: 0, // On n'inclut pas les échecs dans cette vue
          averageLatency: 0, // Pas disponible dans les données actuelles
          packetLoss: 0, // Pas disponible dans les données actuelles
          networkTypes: network_types,
          errorTypes: {}, // Pas disponible dans cette vue
          successRate: 100 // Toutes les connexions sont réussies
        };

        setNetworkStats(stats);

        // Préparer les données pour les graphiques
        prepareChartData(stats, countries_by_network);
      } else {
        console.error('Erreur lors de la récupération des données de type de réseau:', networkTypeResponse.message);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données réseau:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Charger les filtres disponibles
  const fetchAvailableFilters = async () => {
    try {
      const { success, countries, operators, message } = await getAvailableCountriesAndOperators();

      if (!success) {
        throw new Error(message || 'Erreur lors de la récupération des filtres');
      }

      // Transformation des données pour qu'elles correspondent au format attendu par DynamicFilter
      const countryOptions = countries.map((country: any) => ({
        id: country.id ?? country.value ?? country.label ?? country.country ?? '',
        name: country.name ?? country.label ?? country.value ?? country.country ?? ''
      }));

      const operatorOptions = operators.map((operator: any) => ({
        id: operator.id ?? operator.value ?? operator.label ?? operator.operator ?? '',
        name: operator.name ?? operator.label ?? operator.value ?? operator.operator ?? ''
      }));

      console.log("Pays récupérés pour Network Dashboard:", countryOptions);
      console.log("Opérateurs récupérés pour Network Dashboard:", operatorOptions);

      setAvailableCountries(countryOptions);
      setAvailableOperators(operatorOptions);

    } catch (error) {
      console.error('Erreur lors du chargement des filtres:', error);
      setAvailableCountries([]);
      setAvailableOperators([]);
    }
  };

  // Préparer les données pour les graphiques
  const prepareChartData = (stats: CustomNetworkStats, countriesByNetwork: any) => {
    // Graphique des types de réseau
    const networkTypeChart = {
      labels: Object.keys(stats.networkTypes),
      datasets: [
        {
          label: 'Connexions par type de réseau',
          data: Object.values(stats.networkTypes),
          backgroundColor: [
            'rgba(54, 162, 235, 0.6)',
            'rgba(255, 206, 86, 0.6)',
            'rgba(255, 99, 132, 0.6)',
            'rgba(75, 192, 192, 0.6)',
            'rgba(153, 102, 255, 0.6)'
          ],
          borderColor: [
            'rgba(54, 162, 235, 1)',
            'rgba(255, 206, 86, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)'
          ],
          borderWidth: 1
        }
      ],
      // Stocker les données des pays par type de réseau pour l'affichage des détails
      _countriesByNetwork: countriesByNetwork
    };
    setNetworkTypeData(networkTypeChart);

    // Graphique des types d'erreurs
    const errorRateChart = {
      labels: Object.keys(stats.errorTypes),
      datasets: [
        {
          label: 'Types d\'erreurs',
          data: Object.values(stats.errorTypes),
          backgroundColor: [
            'rgba(255, 99, 132, 0.6)',
            'rgba(255, 159, 64, 0.6)',
            'rgba(255, 205, 86, 0.6)',
            'rgba(75, 192, 192, 0.6)'
          ],
          borderColor: [
            'rgba(255, 99, 132, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)'
          ],
          borderWidth: 1
        }
      ]
    };
    setErrorRateData(errorRateChart);
  };

  // Fonction pour charger les causes d'échec
  const loadFailureCauses = async (country?: string, operator?: string) => {
    setLoadingFailureCauses(true);
    try {
      console.log('Chargement des causes d\'échec pour:', { country, operator });
      const response = await getFailureCauses(country, operator, 'FAIL');

      if (response && response.success && response.data) {
        // Transformer les données pour le graphique en secteurs
        const causes = response.data;
        const labels = causes.map((cause: any) => cause.cause || 'Inconnu');
        const data = causes.map((cause: any) => cause.count);
        const total = data.reduce((sum: number, count: number) => sum + count, 0);

        // Calculer les pourcentages
        const causesWithPercentage = causes.map((cause: any) => ({
          ...cause,
          percentage: total > 0 ? (cause.count / total) * 100 : 0
        }));

        const chartData = {
          labels,
          datasets: [{
            data,
            backgroundColor: [
              '#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6',
              '#8b5cf6', '#ec4899', '#6b7280', '#14b8a6', '#f59e0b'
            ],
            borderWidth: 1
          }],
          _rawData: causesWithPercentage // Stocker les données brutes pour le tableau
        };

        setFailureCausesData(chartData);
        console.log('Causes d\'échec chargées:', chartData);
      } else {
        console.warn('Aucune donnée de causes d\'échec trouvée');
        setFailureCausesData(null);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des causes d\'échec:', error);
      setFailureCausesData(null);
    } finally {
      setLoadingFailureCauses(false);
    }
  };

  // Gérer les changements de filtres
  const handleFilterChange = async (newFilters: FilterState) => {
    console.log('Filtres appliqués:', newFilters);

    // Ajouter un timestamp pour forcer le rechargement même si les valeurs sont les mêmes
    const filtersWithTimestamp = {
      ...newFilters,
      timestamp: Date.now()
    };

    // Mettre à jour les filtres et attendre que la mise à jour soit complète
    await new Promise<void>(resolve => {
      setFilters(filtersWithTimestamp);
      resolve();
    });

    // Si le pays a changé, mettre à jour la liste des opérateurs
    if (newFilters.country !== filters.country) {
      await updateOperators(newFilters.country);
    }

    // Si le verdict change vers "fail", charger les causes d'échec
    if (newFilters.verdict === 'fail') {
      await loadFailureCauses(
        newFilters.country,
        newFilters.operator
      );
    }

    // Recharger les données avec les nouveaux filtres immédiatement
    // en passant explicitement les valeurs à loadNetworkData
    await loadNetworkDataWithFilters(newFilters.country, newFilters.operator);
  };

  // Nouvelle fonction pour charger les données avec des filtres spécifiques
  const loadNetworkDataWithFilters = async (country?: string, operator?: string) => {
    setIsLoading(true);
    try {
      console.log("Chargement des données avec filtres explicites:", { country, operator });

      // Récupérer les données de type de réseau depuis l'API avec les filtres explicites
      const networkTypeResponse = await getNetworkTypeDistribution(
        country && country !== '' && country !== 'all' ? country : undefined,
        operator && operator !== '' && operator !== 'all' ? operator : undefined
      );

      if (networkTypeResponse.success && networkTypeResponse.data) {
        const { network_types, countries_by_network } = networkTypeResponse.data;

        // Calculer le total des connexions
        const totalConnections = Object.values(network_types).reduce((sum, count) => sum + count, 0);

        // Mettre à jour les statistiques réseau
        const stats: CustomNetworkStats = {
          totalConnections,
          successfulConnections: totalConnections,
          failedConnections: 0,
          averageLatency: 0,
          packetLoss: 0,
          networkTypes: network_types,
          errorTypes: {},
          successRate: 100
        };

        setNetworkStats(stats);

        // Préparer les données pour les graphiques
        prepareChartData(stats, countries_by_network);
      } else {
        console.error('Erreur lors de la récupération des données de type de réseau:', networkTypeResponse.message);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des données réseau:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Mettre à jour la liste des opérateurs lorsque le pays change
  const updateOperators = async (country: string) => {
    try {
      if (country && country !== 'all') {
        const opsByCountry = await getOperatorsByCountry(country);
        setAvailableOperators(opsByCountry);
      } else {
        // Tous les pays -> récupérer tous les opérateurs
        const { operators } = await getAvailableCountriesAndOperators();
        const normalized = operators.map((op: any) => ({
          id: op.id ?? op.value ?? op.operator ?? '',
          name: op.name ?? op.label ?? op.value ?? ''
        }));
        setAvailableOperators(normalized);
      }
    } catch (err) {
      console.error('Erreur lors de la mise à jour des opérateurs:', err);
    }
  };

  // Gérer le clic sur un segment du graphique de type de réseau
  const handleNetworkTypeClick = (event: any, elements: any) => {
    if (elements.length > 0) {
      const { index } = elements[0];
      const networkType = networkTypeData.labels[index];
      setSelectedNetworkType(networkType);

      // Récupérer les détails des pays et opérateurs pour ce type de réseau
      const details = networkTypeData._countriesByNetwork[networkType] || [];
      setNetworkTypeDetails(details);
    }
  };

  // Charger les données au montage du composant
  useEffect(() => {
    const initializeData = async () => {
      try {
        await fetchAvailableFilters();
        await loadNetworkDataWithFilters();
      } catch (error) {
        console.error("Erreur lors de l'initialisation des données:", error);
      }
    };

    initializeData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">Tableau de Bord - Performance Réseau</h1>

      {/* Section des filtres */}
      <div className="bg-white rounded-lg shadow-md p-4 mb-6">
        <h2 className="text-lg font-semibold mb-4">Filtres</h2>
        <DynamicFilter
          countries={availableCountries}
          operators={availableOperators}
          onFilterChange={handleFilterChange}
          initialValues={{
            startDate: filters.dateRange.startDate,
            endDate: filters.dateRange.endDate
          }}
        />
      </div>

      {/* Statistiques de performance réseau */}
      <div className="mb-6">
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : networkStats ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-blue-800">Connexions Totales</h3>
              <p className="text-2xl font-bold text-blue-600">{networkStats.totalConnections.toLocaleString()}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-green-800">Taux de Succès</h3>
              <p className="text-2xl font-bold text-green-600">{networkStats.successRate.toFixed(1)}%</p>
            </div>
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-yellow-800">Types de Réseau</h3>
              <p className="text-2xl font-bold text-yellow-600">{Object.keys(networkStats.networkTypes).length}</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-purple-800">Pays Analysés</h3>
              <p className="text-2xl font-bold text-purple-600">
                {availableCountries.length}
              </p>
            </div>
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">Aucune donnée disponible</div>
        )}
      </div>

      {/* Section des graphiques */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {filters.verdict === 'fail' ? (
          /* Graphique des causes d'échec - visible uniquement quand verdict=fail */
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-lg font-semibold mb-4">Causes d'Échec</h2>
            {loadingFailureCauses ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
              </div>
            ) : failureCausesData ? (
              <div className="h-64">
                <Pie
                  data={failureCausesData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'right' as const,
                      },
                      title: {
                        display: true,
                        text: 'Répartition des Causes d\'Échec'
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            const label = context.label || '';
                            const value = context.raw as number;
                            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                          }
                        }
                      }
                    }
                  }}
                />
              </div>
            ) : (
              <div className="flex justify-center items-center h-64 text-gray-500">
                Aucune donnée disponible pour les causes d'échec
              </div>
            )}
          </div>
        ) : (
          /* Graphique des types de réseau - visible quand verdict != fail */
          <div className="bg-white rounded-lg shadow-md p-4">
            <h2 className="text-lg font-semibold mb-4">Répartition par Type de Réseau</h2>
            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
              </div>
            ) : networkTypeData ? (
              <div className="h-64">
                <Pie
                  data={networkTypeData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        position: 'right' as const,
                      },
                      title: {
                        display: true,
                        text: 'Connexions par Type de Réseau'
                      },
                      tooltip: {
                        callbacks: {
                          label: function(context) {
                            const label = context.label || '';
                            const value = context.raw as number;
                            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                            const percentage = Math.round((value / total) * 100);
                            return `${label}: ${value} (${percentage}%)`;
                          }
                        }
                      }
                    },
                    onClick: handleNetworkTypeClick
                  }}
                />
              </div>
            ) : (
              <div className="flex justify-center items-center h-64 text-gray-500">
                Aucune donnée disponible
              </div>
            )}
          </div>
        )}

        {/* Détails par pays et opérateurs */}
        <div className="bg-white rounded-lg shadow-md p-4">
          {filters.verdict === 'fail' ? (
            /* Détails des causes d'échec par pays - visible uniquement quand verdict=fail */
            <>
              <h2 className="text-lg font-semibold mb-4">
                Détails des Échecs par Pays
              </h2>
              {loadingFailureCauses ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                </div>
              ) : failureCausesData && failureCausesData._rawData ? (
                <div className="overflow-auto max-h-64">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cause de Rejet</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pourcentage</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {failureCausesData._rawData.map((cause: FailureCause, index: number) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{cause.cause || "Inconnu"}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{cause.count}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{cause.percentage.toFixed(1)}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="flex justify-center items-center h-64 text-gray-500">
                  Aucune donnée disponible sur les causes d'échec
                </div>
              )}
            </>
          ) : (
            /* Détails des pays et opérateurs par type de réseau - visible quand verdict != fail */
            <>
              <h2 className="text-lg font-semibold mb-4">
                {selectedNetworkType ? `Détails pour ${selectedNetworkType}` : 'Détails par Type de Réseau'}
              </h2>
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
                </div>
              ) : selectedNetworkType && networkTypeDetails.length > 0 ? (
                <div className="overflow-auto max-h-64">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Pays</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Localisation</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opérateur</th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nombre</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {networkTypeDetails.map((detail, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{detail.country}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{detail.location}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{detail.operator}</td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{detail.count}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="flex justify-center items-center h-64 text-gray-500">
                  {selectedNetworkType
                    ? `Aucune donnée disponible pour ${selectedNetworkType}`
                    : 'Cliquez sur un segment du graphique pour voir les détails'}
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Section des statistiques détaillées */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <h2 className="text-lg font-semibold mb-4">
          {filters.verdict === 'fail' ? 'Détails des Causes d\'Échec par Pays' : 'Statistiques Détaillées'}
        </h2>
        {isLoading || loadingFailureCauses ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
          </div>
        ) : filters.verdict === 'fail' && failureCausesData && failureCausesData._rawData ? (
          /* Affichage détaillé des pays par cause d'échec */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {failureCausesData._rawData.map((cause: FailureCause, index: number) => (
              <div key={index} className="bg-red-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-red-800">Cause: {cause.cause || "Inconnu"}</h3>
                <p className="text-lg font-bold text-red-600">{cause.count} échecs ({cause.percentage.toFixed(1)}%)</p>
                {cause.countries && Object.keys(cause.countries).length > 0 && (
                  <div className="mt-2">
                    <p className="text-xs text-red-700 font-medium mb-1">Pays concernés:</p>
                    <div className="text-xs text-red-700 max-h-24 overflow-y-auto">
                      {Object.entries(cause.countries)
                        .sort(([, countA], [, countB]) => (countB as number) - (countA as number))
                        .map(([country, count], i) => (
                          <div key={i} className="flex justify-between">
                            <span>{country}</span>
                            <span>{count} échecs</span>
                          </div>
                        ))
                      }
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : networkStats ? (
          /* Affichage normal des statistiques de réseau */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Object.entries(networkStats.networkTypes).map(([type, count], index) => (
              <div key={index} className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-800">Type de Réseau: {type}</h3>
                <p className="text-2xl font-bold text-blue-600">{count.toLocaleString()} connexions</p>
                <p className="text-sm text-blue-600">
                  {((count / networkStats.totalConnections) * 100).toFixed(1)}% du total
                </p>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-8">Aucune donnée disponible</div>
        )}
      </div>
    </div>
  );
};

export default NetworkDashboardPage;