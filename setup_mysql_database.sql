-- Script de création de la base de données roaming_dashboard
-- Exécuter ce script dans phpMyAdmin ou MySQL Workbench

-- Création de la base de données
DROP DATABASE IF EXISTS roaming_dashboard;
CREATE DATABASE roaming_dashboard CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE roaming_dashboard;

-- Table des utilisateurs
CREATE TABLE users (
    id VARCHAR(36) PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(100) NOT NULL UNIQUE,
    full_name VA<PERSON><PERSON><PERSON>(255),
    role ENUM('admin', 'analyst', 'user') NOT NULL DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des opérateurs partenaires
CREATE TABLE operators (
    id VARCHAR(36) PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    country_code CHAR(2) NOT NULL,
    region VARCHAR(100) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    status ENUM('active', 'inactive', 'pending') NOT NULL DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table principale SteeringOfRoaming (basée sur vos données CSV)
CREATE TABLE SteeringOfRoaming (
    TestcaseId BIGINT AUTO_INCREMENT PRIMARY KEY,
    OrderId BIGINT,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict VARCHAR(10),
    TestDefinitionPath TEXT,
    User VARCHAR(50),
    UserGroup VARCHAR(50),
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry TEXT,
    errorSideId INT,
    errorSideLocation TEXT,
    errorSideNumber VARCHAR(20),
    errorSidePlmn VARCHAR(50),
    errorSideProbe TEXT,
    errorSideRxLevel INT,
    errorSideUsedPLMNName TEXT,
    errorState VARCHAR(50),
    errorStateId INT,
    Completed TINYINT(1),
    Failure TINYINT(1),
    Incomplete TINYINT(1),
    L3_Flag TINYINT(1),
    ServiceType VARCHAR(10),
    Success TINYINT(1),
    
    -- Colonnes de données de roaming
    a_location_country VARCHAR(100),
    a_location TEXT,
    a_number VARCHAR(50),
    a_imsi VARCHAR(50),
    a_Usedplmnname VARCHAR(255),
    a_usedplmn VARCHAR(50),
    a_TADIG VARCHAR(50),
    a_VPLMN_registered VARCHAR(100),
    a_networkType VARCHAR(50),
    a_LupDuration FLOAT,
    a_NrOfPlmnsRejected VARCHAR(100),
    a_nrofluprequests INT,
    a_rejectCauses VARCHAR(255),
    a_rejectedPLMNs VARCHAR(255),
    
    -- Colonnes temporelles pour les analyses
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    WeekOfYear INT,
    
    -- Colonnes d'attachement
    attache0 FLOAT DEFAULT 0,
    attache1 FLOAT DEFAULT 0,
    attache2 FLOAT DEFAULT 0,
    attache3 FLOAT DEFAULT 0,
    attache4 FLOAT DEFAULT 0,
    attache5 FLOAT DEFAULT 0,
    attache6 FLOAT DEFAULT 0,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Index pour optimiser les performances
CREATE INDEX idx_steering_timestamp ON SteeringOfRoaming(Timestamp);
CREATE INDEX idx_steering_country ON SteeringOfRoaming(a_location_country);
CREATE INDEX idx_steering_verdict ON SteeringOfRoaming(Verdict);
CREATE INDEX idx_steering_tcname ON SteeringOfRoaming(TCName(100));
CREATE INDEX idx_steering_temporal ON SteeringOfRoaming(DayOfMonth, DayOfWeek, HourOfDay, MonthOfYear, WeekOfYear);

-- Insérer un utilisateur admin par défaut
INSERT INTO users (id, email, username, full_name, role) VALUES 
('admin-001', '<EMAIL>', 'admin', 'Administrateur Orange Maroc', 'admin');

-- Insérer quelques opérateurs de test
INSERT INTO operators (id, name, country_code, region, contact_email, status) VALUES 
('op-001', 'Orange Maroc', 'MA', 'Africa', '<EMAIL>', 'active'),
('op-002', 'Orange France', 'FR', 'Europe', '<EMAIL>', 'active'),
('op-003', 'Vodafone UK', 'GB', 'Europe', '<EMAIL>', 'active');

-- Vue pour les statistiques quotidiennes
CREATE VIEW v_daily_steering_stats AS
SELECT 
    DATE(Timestamp) as usage_date,
    a_location_country,
    COUNT(*) as total_tests,
    SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) as successful_tests,
    SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate,
    AVG(a_LupDuration) as avg_duration
FROM SteeringOfRoaming
WHERE Timestamp IS NOT NULL
GROUP BY DATE(Timestamp), a_location_country;

-- Vue pour les performances par pays
CREATE VIEW v_country_performance AS
SELECT 
    a_location_country,
    COUNT(*) as total_connections,
    SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate,
    AVG(a_LupDuration) as avg_duration,
    AVG(attache0) as avg_attache0,
    AVG(attache1) as avg_attache1,
    AVG(attache2) as avg_attache2
FROM SteeringOfRoaming
WHERE a_location_country IS NOT NULL
GROUP BY a_location_country;
