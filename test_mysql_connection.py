#!/usr/bin/env python3
"""
Script de test de connexion MySQL pour roaming_dashboard
"""

import pymysql
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

def test_mysql_connection():
    """Tester la connexion à MySQL"""
    print("🔍 Test de connexion à MySQL...")
    
    # Configuration de connexion
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'user': os.getenv('DB_USER', 'root'),
        'password': os.getenv('DB_PASSWORD', ''),
        'database': os.getenv('DB_NAME', 'kpi'),
        'port': int(os.getenv('DB_PORT', '3306')),
        'charset': 'utf8mb4',
        'cursorclass': pymysql.cursors.DictCursor
    }
    
    print(f"📡 Tentative de connexion à {config['host']}:{config['port']}")
    print(f"🗄️  Base de données: {config['database']}")
    print(f"👤 Utilisateur: {config['user']}")
    
    try:
        # Tenter la connexion
        connection = pymysql.connect(**config)
        print("✅ Connexion MySQL réussie!")
        
        # Tester une requête simple
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION() as version")
            result = cursor.fetchone()
            print(f"🔢 Version MySQL: {result['version']}")
            
            # Vérifier si la base de données existe
            cursor.execute("SHOW DATABASES LIKE %s", (config['database'],))
            db_exists = cursor.fetchone()
            
            if db_exists:
                print(f"✅ Base de données '{config['database']}' trouvée")
                
                # Lister les tables
                cursor.execute("SHOW TABLES")
                tables = cursor.fetchall()
                print(f"📋 Tables disponibles ({len(tables)}):")
                for table in tables:
                    table_name = list(table.values())[0]
                    print(f"   - {table_name}")
                    
                # Tester la table SteeringOfRoaming si elle existe
                cursor.execute("SHOW TABLES LIKE 'SteeringOfRoaming'")
                steering_table = cursor.fetchone()
                
                if steering_table:
                    cursor.execute("SELECT COUNT(*) as count FROM SteeringOfRoaming")
                    count_result = cursor.fetchone()
                    print(f"📊 Enregistrements dans SteeringOfRoaming: {count_result['count']}")
                else:
                    print("⚠️  Table SteeringOfRoaming non trouvée")
                    
            else:
                print(f"❌ Base de données '{config['database']}' non trouvée")
                print("💡 Vous devez créer la base de données avec le script setup_mysql_database.sql")
        
        connection.close()
        return True
        
    except pymysql.MySQLError as e:
        print(f"❌ Erreur de connexion MySQL: {e}")
        print("\n🔧 Solutions possibles:")
        print("1. Vérifiez que XAMPP est démarré avec MySQL")
        print("2. Vérifiez les paramètres de connexion dans .env")
        print("3. Créez la base de données avec setup_mysql_database.sql")
        return False
    except Exception as e:
        print(f"❌ Erreur inattendue: {e}")
        return False

def check_xampp_status():
    """Vérifier si XAMPP/MySQL est en cours d'exécution"""
    print("\n🔍 Vérification du statut XAMPP/MySQL...")
    
    try:
        # Tenter une connexion basique sans spécifier de base de données
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='',
            port=3306
        )
        print("✅ MySQL est en cours d'exécution")
        connection.close()
        return True
    except Exception as e:
        print("❌ MySQL n'est pas accessible")
        print("💡 Démarrez XAMPP et activez MySQL")
        return False

if __name__ == "__main__":
    print("🚀 Test de connexion MySQL pour GlobalRoamer")
    print("=" * 50)
    
    # Vérifier XAMPP
    xampp_ok = check_xampp_status()
    
    if xampp_ok:
        # Tester la connexion complète
        connection_ok = test_mysql_connection()
        
        if connection_ok:
            print("\n🎉 Configuration MySQL OK!")
            print("✅ Vous pouvez maintenant démarrer votre application")
        else:
            print("\n⚠️  Configuration MySQL incomplète")
            print("📋 Suivez les instructions ci-dessus pour corriger")
    else:
        print("\n❌ XAMPP/MySQL non démarré")
        print("🔧 Démarrez XAMPP et réessayez")
    
    print("\n" + "=" * 50)
