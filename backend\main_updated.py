"""
Version mise à jour du fichier main.py avec l'endpoint get_network_type_distribution modifié
pour utiliser la fonction importée de api.network_type_endpoint.

Pour utiliser ce fichier, renommez-le en main.py ou lancez le serveur avec:
uvicorn main_updated:app --host 0.0.0.0 --reload
"""

from fastapi import FastAPI, Query, Request, Response, status, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse, FileResponse
from pathlib import Path
import os
from dotenv import load_dotenv
import sys
from datetime import datetime
import uvicorn
import pandas as pd
from sqlalchemy import text
from typing import Optional, List, Dict, Any
import re
import sqlite3

# Charger les variables d'environnement
load_dotenv()

# Ajouter le répertoire parent au PYTHONPATH
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Importer les routes
from api.routes import roaming_routes
from api.routes import kpi_routes
from api.Kpi_routes import router as kpi_router
from api.routers.reports import router as reports_router
from api.routes.alert_routes import router as alert_router
from api.Kpi_service_clean import (
    get_weekly_attach_trend, 
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_failure_details,
    get_failure_details_extended,
    get_network_performance_stats,
    get_http_download_performance,
    get_country_overview
)
from api.get_db_connection import get_db_connection

# Importer la fonction get_network_type_distribution du nouveau module
from api.network_type_endpoint import get_network_type_distribution as get_network_type_distribution_func

# Créer l'application FastAPI
app = FastAPI(
    title="Roaming Dashboard API",
    description="API pour l'analyse des données de roaming",
    version="1.0.0"
)

# Configurer CORS
origins = [
    "http://localhost:5173",  # URL de développement React/Vite
    "http://localhost:3000",  # URL alternative de développement React
    "http://127.0.0.1:5173",
    "http://127.0.0.1:3000",
    "http://localhost:8000"   # URL de l'API FastAPI
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Créer les répertoires nécessaires
charts_dir = Path("charts")
if not charts_dir.exists():
    charts_dir.mkdir(parents=True)

# Route racine pour vérifier que l'API fonctionne
@app.get("/")
async def root():
    return {"status": "API is running", "version": "1.0.0"}

# Route de santé
@app.get("/api/health")
async def health_check():
    return {"status": "ok", "message": "Le serveur fonctionne correctement"}

# [... Autres endpoints ...]

@app.get("/api/network_type_distribution")
async def get_network_type_distribution(country: str = None, operator: str = None):
    """
    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming
    Utilise la fonction importée de api.network_type_endpoint
    """
    return await get_network_type_distribution_func(country, operator)

# [... Autres endpoints ...]

# Monter les répertoires statiques
app.mount("/charts", StaticFiles(directory=str(charts_dir)), name="charts")

# Inclure les routes - Notez que le préfixe /api est retiré car il est déjà dans les routers
app.include_router(roaming_routes.router)
app.include_router(kpi_router)
app.include_router(reports_router, prefix="/api")
app.include_router(alert_router)  # Inclure le router d'alertes

# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    print(f"Exception globale: {str(exc)}")
    import traceback
    traceback.print_exc()
    return JSONResponse(
        status_code=500,
        content={"message": f"Une erreur interne s'est produite: {str(exc)}"}
    )

@app.middleware("http")
async def log_requests(request, call_next):
    """Log toutes les requêtes entrantes"""
    print(f"\n[{datetime.now()}] ⚠️⚠️⚠️ REQUÊTE REÇUE: {request.method} {request.url.path} avec query params: {request.query_params}")
    
    # Gérer le cas spécifique de success_rate
    if 'success_rate' in request.url.path:
        print(f"⭐⭐⭐ DÉTECTION DE REQUÊTE success_rate: {request.url}")
    
    # Continuer le traitement normal
    response = await call_next(request)
    print(f"[{datetime.now()}] ✅✅✅ RÉPONSE ENVOYÉE: {request.method} {request.url.path} avec status: {response.status_code}")
    
    return response

if __name__ == "__main__":
    uvicorn.run("main_updated:app", host="0.0.0.0", port=8000, reload=True) 