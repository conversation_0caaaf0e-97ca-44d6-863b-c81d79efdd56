from fastapi import FastAPI, File, UploadFile, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import pandas as pd
import json
import os
import subprocess
from pathlib import Path
import tempfile
import shutil
import sys
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from fastapi.responses import RedirectResponse, FileResponse, JSONResponse
import pymysql
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv
from datetime import datetime, timedelta
from functools import lru_cache
from typing import Callable
import time
import analyze_roaming_data
from database import get_db_connection
import sqlite3

# Ajouter le répertoire parent au PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# Charger les variables d'environnement
load_dotenv()

# Créer l'application FastAPI
app = FastAPI(
    title="Roaming Dashboard API",
    description="API pour l'analyse des données de roaming",
    version="1.0.0"
)

# Cache decorator avec expiration
def timed_lru_cache(seconds: int, maxsize: int = 128):
    def wrapper_decorator(func):
        func = lru_cache(maxsize=maxsize)(func)
        func.lifetime = seconds
        func.expiration = time.time() + seconds

        def wrapped_func(*args, **kwargs):
            if time.time() > func.expiration:
                func.cache_clear()
                func.expiration = time.time() + func.lifetime
            return func(*args, **kwargs)

        return wrapped_func

    return wrapper_decorator

# Configurer CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:5173",  # Frontend Vite
        "http://localhost:3000",  # Frontend alternatif
        "http://127.0.0.1:5173",
        "http://127.0.0.1:3000",
        "*"  # Permettre toutes les origines en développement
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Middleware pour logger les requêtes
@app.middleware("http")
async def log_requests(request, call_next):
    print(f"\n[{datetime.now()}] Requête entrante: {request.method} {request.url}")
    print(f"Headers: {request.headers}")
    try:
        response = await call_next(request)
        print(f"Réponse: {response.status_code}")
        return response
    except Exception as e:
        print(f"Erreur dans le middleware: {str(e)}")
        raise

# Importer les modules nécessaires
from api.Kpi_service_clean import (
    get_steering_success_by_country,
    get_attachment_rates_by_country,
    get_weekly_attach_trend,
    get_annual_trend,
    get_failure_details_extended,
    get_failure_details
)

from api.roaming_router import router as roaming_routes
from api.Kpi_routes import router as kpi_routes

# Créer les répertoires nécessaires
charts_dir = Path("charts")
if not charts_dir.exists():
    charts_dir.mkdir(parents=True)

class AnalysisResult(BaseModel):
    countries: List[str]
    success_rates: Dict[str, float]
    attachment_rates: Dict[str, Dict[str, float]]
    chart_url: str

@app.get("/")
async def root():
    return {"status": "API is running", "version": "1.0.0"}

@app.get("/api/db-check")
async def check_db_connection():
    """Vérifie la connexion à la base de données"""
    try:
        connection = get_db_connection()
        if connection:
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            return {
                "success": True,
                "message": "Connexion à la base de données réussie",
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "message": "Impossible de se connecter à la base de données",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        return {
            "success": False,
            "message": f"Erreur lors de la vérification de la connexion: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

@app.get("/api/health")
async def health_check():
    """
    Endpoint de vérification de l'état de santé de l'API.
    Retourne le statut actuel et l'horodatage.
    """
    try:
        # Vérifier la connexion à la base de données
        connection = get_db_connection()
        db_status = "connected" if connection else "disconnected"
        if connection:
            connection.close()
    except Exception as e:
        db_status = f"error: {str(e)}"

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "service": "roaming-dashboard-api",
        "database": db_status,
        "endpoints": {
            "failure_details": "/api/failure_details",
            "health": "/api/health",
            "db_check": "/api/db-check"
        }
    }

@app.get("/api/steering_success_by_country")
async def get_steering_success(period: str = None, country: str = None, operator: str = None, year: int = None, month: int = None, week: int = None, quarter: int = None, verdict: str = None):
    """Endpoint pour obtenir les données de succès du steering par pays"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_steering_success avec period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}, verdict={verdict}")
        
        # Si aucune année n'est spécifiée, utiliser l'année courante
        if period and not year:
            year = datetime.now().year
            print(f"Aucune année spécifiée, utilisation de l'année courante: {year}")
        
        # Vérifier que tous les paramètres nécessaires sont présents
        if period == 'week' and week is None:
            print(f"ERREUR: La période 'week' nécessite le paramètre 'week', mais il est manquant")
            return JSONResponse(
                status_code=400,
                content={"error": "Le paramètre 'week' est requis pour la période 'week'"}
            )
        elif period == 'month' and month is None:
            print(f"ERREUR: La période 'month' nécessite le paramètre 'month', mais il est manquant")
            return JSONResponse(
                status_code=400,
                content={"error": "Le paramètre 'month' est requis pour la période 'month'"}
            )
        elif period == 'quarter' and quarter is None:
            print(f"ERREUR: La période 'quarter' nécessite le paramètre 'quarter', mais il est manquant")
            return JSONResponse(
                status_code=400,
                content={"error": "Le paramètre 'quarter' est requis pour la période 'quarter'"}
            )
        
        try:
            data = get_steering_success_by_country(
                country=country, 
                operator=operator, 
                period=period, 
                year=year, 
                month=month, 
                week=week, 
                quarter=quarter,
                verdict=verdict
            )
            print(f"Données récupérées: {len(data)} entrées")
            return data
        except Exception as e:
            print(f"ERREUR dans get_steering_success_by_country: {str(e)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Erreur interne: {str(e)}")
    except Exception as e:
        print(f"ERREUR dans get_steering_success: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/verdicts")
async def get_verdicts():
    """Retourne la liste des verdicts disponibles."""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")

        cursor = connection.cursor(dictionary=True)
        query = "SELECT DISTINCT Verdict as value FROM steeringofroaming WHERE Verdict IS NOT NULL AND TRIM(Verdict) != '' ORDER BY Verdict"
        
        cursor.execute(query)
        raw_results = cursor.fetchall()
        verdicts = [{"value": row['value'], "label": row['value']} for row in raw_results]
        
        cursor.close()
        connection.close()
        
        return {"success": True, "data": verdicts}
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/attachment_rates_by_country")
async def get_attachment_rates(country: str = None, operator: str = None):
    """Endpoint pour obtenir les taux d'attachement par pays"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_attachment_rates avec country={country}, operator={operator}")
        
        from api.Kpi_service_clean import get_attachment_rates_by_country
        
        # Appeler la fonction avec les paramètres de filtrage
        data = get_attachment_rates_by_country()
        
        # Filtrer les données si nécessaire
        if country and country != 'all':
            data = [item for item in data if item.get('country') == country]
        
        if operator and operator != 'all':
            data = [item for item in data if item.get('operator') == operator]
        
        # Calculer les KPI globaux
        if data:
            total_countries = len(set(item['country'] for item in data))
            total_tests = sum(item['total_tests'] for item in data)
            avg_success_rate = sum(item['success_rate'] for item in data) / len(data)
            
            kpi = {
                'total_countries': total_countries,
                'total_tests': total_tests,
                'avg_success_rate': round(avg_success_rate, 2)
            }
        else:
            kpi = {
                'total_countries': 0,
                'total_tests': 0,
                'avg_success_rate': 0
            }
        
        return {
            'data': data,
            'kpi': kpi
        }
    except Exception as e:
        print(f"Erreur dans get_attachment_rates: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/kpi/weeks")
async def get_weeks():
    """Endpoint pour obtenir les données hebdomadaires"""
    try:
        data = get_weekly_attach_trend()
        return data
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/kpi/period_data")
async def get_period_data(period: str = "month", country: str = None, operator: str = None, year: int = None, month: int = None, week: int = None, quarter: int = None):
    """Endpoint pour obtenir les données par période"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_period_data avec period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}")
        
        # Si aucune année n'est spécifiée, utiliser l'année courante
        if not year:
            year = datetime.now().year
            print(f"Aucune année spécifiée, utilisation de l'année courante: {year}")
        
        # Utiliser la fonction appropriée selon la période sélectionnée
        if period == "week":
            from api.Kpi_service_clean import get_weekly_attach_trend
            data = get_weekly_attach_trend(country=country, operator=operator, year=year, week=week)
            if isinstance(data, dict) and 'data' in data:
                data = data['data']  # Extraire les données si elles sont dans un dictionnaire
        elif period == "month":
            from api.Kpi_service_clean import get_annual_trend
            data = get_annual_trend(country=country, operator=operator, year=year, month=month)
        elif period == "quarter":
            from api.Kpi_service_clean import get_quarterly_trend
            data = get_quarterly_trend(country=country, operator=operator, year=year, quarter=quarter)
        elif period == "year":
            from api.Kpi_service_clean import get_yearly_trend
            data = get_yearly_trend(country=country, operator=operator, year=year)
        else:
            # Par défaut, utiliser les données mensuelles
            from api.Kpi_service_clean import get_annual_trend
            data = get_annual_trend(country=country, operator=operator, year=year)
        
        # Si aucune donnée n'est retournée, renvoyer un tableau vide
        if not data:
            print(f"Aucune donnée disponible pour period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}")
            return []
            
        print(f"Données récupérées: {len(data)} entrées")
        return data
    except Exception as e:
        print(f"Erreur dans get_period_data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/kpi/http_period_data")
async def get_http_period_data(period: str = "month", country: str = None, operator: str = None, year: int = None, month: int = None, week: int = None, quarter: int = None):
    """Endpoint pour obtenir les données HTTP par période"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_http_period_data avec period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}")
        
        # Si aucune année n'est spécifiée, utiliser l'année courante
        if not year:
            year = datetime.now().year
            print(f"Aucune année spécifiée, utilisation de l'année courante: {year}")
        
        from api.Kpi_service_clean import get_http_period_data
        data = get_http_period_data(period=period, country=country, operator=operator, year=year, month=month, week=week, quarter=quarter)
        
        # Si aucune donnée n'est retournée, renvoyer un tableau vide
        if not data:
            print(f"Aucune donnée HTTP disponible pour period={period}, country={country}, operator={operator}, year={year}, month={month}, week={week}, quarter={quarter}")
            return []
            
        print(f"Données HTTP récupérées: {len(data)} entrées")
        return data
    except Exception as e:
        print(f"Erreur dans get_http_period_data: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload/csv")
async def upload_csv(file: UploadFile = File(...)):
    """Endpoint pour télécharger un fichier CSV"""
    try:
        # Créer le dossier data s'il n'existe pas
        data_dir = Path("data")
        data_dir.mkdir(exist_ok=True)
        
        # Sauvegarder le fichier
        file_path = data_dir / file.filename
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        return {
            "success": True,
            "message": "Fichier téléchargé avec succès",
            "filename": file.filename
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/analysis/run")
async def run_analysis():
    """Exécuter l'analyse Python et retourner les résultats"""
    try:
        # Vérifier si le module est disponible
        if analyze_roaming_data:
            # Exécuter l'analyse directement
            country_stats = analyze_roaming_data.main()
            chart_path = "../frontend/public/steering_chart.png"
            
            # Construire l'URL relative pour le frontend
            chart_url = "/steering_chart.png"
            
            # Préparer les données de réponse
            countries = [stat["a_location_country"] for stat in country_stats]
            
            success_rates = {}
            attachment_rates = {}
            
            for stat in country_stats:
                country = stat["a_location_country"]
                success_rates[country] = stat["attach_success_rate"]
                attachment_rates[country] = {
                    k: v for k, v in stat.items() 
                    if k.startswith("attache") and k.endswith("%")
                }
            
            return {
                "success": True,
                "countries": countries,
                "success_rates": success_rates,
                "attachment_rates": attachment_rates,
                "chart_url": chart_url
            }
        else:
            # Essayer d'exécuter le script en tant que processus externe
            script_path = Path(__file__).parent / "analyze_roaming_data.py"
            if script_path.exists():
                result = subprocess.run(
                    [sys.executable, str(script_path)],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # Essayer de charger les résultats du JSON généré
                    json_path = Path("../frontend/public/steering_data.json")
                    if not json_path.exists():
                        json_path = Path("public/steering_data.json")
                    
                    if json_path.exists():
                        with open(json_path, "r") as f:
                            country_stats = json.load(f)
                        
                        chart_url = "/steering_chart.png"
                        
                        # Préparer les données de réponse
                        countries = [stat["a_location_country"] for stat in country_stats]
                        
                        success_rates = {}
                        attachment_rates = {}
                        
                        for stat in country_stats:
                            country = stat["a_location_country"]
                            success_rates[country] = stat["attach_success_rate"]
                            attachment_rates[country] = {
                                k: v for k, v in stat.items() 
                                if k.startswith("attache") and k.endswith("%")
                            }
                        
                        return {
                            "success": True,
                            "countries": countries,
                            "success_rates": success_rates,
                            "attachment_rates": attachment_rates,
                            "chart_url": chart_url
                        }
                    else:
                        return {
                            "success": True,
                            "message": "Analyse terminée mais données non disponibles",
                            "stdout": result.stdout,
                            "stderr": result.stderr
                        }
                else:
                    return {
                        "success": False,
                        "message": "Erreur lors de l'exécution de l'analyse",
                        "stdout": result.stdout,
                        "stderr": result.stderr
                    }
            else:
                return {
                    "success": False,
                    "message": "Script d'analyse non disponible"
                }
    except Exception as e:
        return {
            "success": False,
            "message": f"Erreur lors de l'analyse: {str(e)}"
        }

@app.get("/api/steering/data")
async def get_steering_data():
    """Récupérer les données de steering depuis le JSON généré"""
    try:
        # Essayer de trouver le fichier JSON
        json_path = Path("../frontend/public/steering_data.json")
        if not json_path.exists():
            json_path = Path("public/steering_data.json")
        
        if json_path.exists():
            with open(json_path, "r") as f:
                data = json.load(f)
            return {"success": True, "data": data}
        else:
            # Si les données n'existent pas, exécuter l'analyse
            return await run_analysis()
    except Exception as e:
        return {"success": False, "message": str(e)}

@app.get("/api/steering_chart")
async def get_steering_chart():
    # Utilise un chemin absolu basé sur la racine du projet
    chart_path = os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "frontend", "public", "steering_chart.png"))
    if not os.path.exists(chart_path):
        return {"error": "Le graphique n'existe pas"}
    return FileResponse(chart_path, media_type="image/png")

@app.get("/api/failure_details")
async def failure_details_endpoint(
    verdict: str = Query("FAIL", description="Verdict du test (FAIL, SUCCESS, ÉCHECS, ECHECS)"),
    country: str = Query("all", description="Pays à filtrer"),
    operator: str = Query("all", description="Opérateur à filtrer")
):
    """Retourne les détails des échecs avec les colonnes spécifiques demandées"""
    try:
        print(f"\n[{datetime.now()}] Appel à /api/failure_details")
        print(f"Paramètres reçus: verdict={verdict}, country={country}, operator={operator}")
        
        # Normaliser le verdict
        verdict = verdict.upper()
        if verdict in ['ÉCHECS', 'ECHECS']:
            verdict = 'FAIL'
            print(f"Verdict normalisé de '{verdict}' à 'FAIL'")
        elif verdict not in ['FAIL', 'SUCCESS']:
            print(f"Verdict invalide reçu: {verdict}")
            return {
                "success": False,
                "message": "Verdict invalide. Utilisez FAIL, SUCCESS, ÉCHECS ou ECHECS.",
                "data": []
            }

        # Appeler la fonction de service
        from api.Kpi_service_clean import get_failure_details_extended
        print(f"Appel à get_failure_details_extended avec verdict={verdict}, country={country}, operator={operator}")
        result = get_failure_details_extended(verdict, country, operator)
        
        if not result:
            print("Aucune donnée trouvée dans get_failure_details_extended")
            return {
                "success": True,
                "message": "Aucune donnée trouvée",
                "data": []
            }
        
        # S'assurer que le format de réponse est cohérent
        if isinstance(result, dict) and 'data' in result:
            print(f"Données trouvées: {len(result['data'])} enregistrements")
            return {
                "success": True,
                "message": f"{len(result['data'])} enregistrements trouvés",
                "data": result['data']
            }
        else:
            print(f"Format de résultat inattendu: {type(result)}")
        return {
            "success": True,
                "message": "Données récupérées avec succès",
                "data": result if isinstance(result, list) else []
        }
        
    except Exception as e:
        print(f"Erreur dans failure_details_endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "success": False,
            "message": f"Erreur: {str(e)}",
            "data": []
        }

@app.get("/api/failure_causes")
async def failure_causes_endpoint(country: str = None, operator: str = None):
    """Retourne les causes d'échec avec filtrage optionnel par pays et opérateur"""
    try:
        from api.Kpi_service_clean import get_fail_causes
        data = get_fail_causes(country, operator)
        
        # Formater la réponse pour correspondre à ce qu'attend le frontend
        response = {
            "top_failures": {},
            "details": []
        }
        
        if data:
            # Agréger les causes d'échec
            causes_count = {}
            for item in data:
                cause = item.get('cause', 'Inconnu')
                count = item.get('count', 0)
                if cause in causes_count:
                    causes_count[cause] += count
                else:
                    causes_count[cause] = count
            
            response["top_failures"] = causes_count
            response["details"] = data
        
        return response
    except Exception as e:
        print(f"ERREUR dans failure_causes_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/failure_analysis")
async def failure_analysis_endpoint(country: str = None, operator: str = None):
    """Retourne l'analyse des échecs avec filtrage optionnel par pays et opérateur"""
    try:
        from api.Kpi_service_clean import get_failure_analysis_by_filter
        data = get_failure_analysis_by_filter(country, operator)
        
        if not data:
            return {
                "attachment_stats": {
                    "attache0": 0,
                    "attache1": 0,
                    "attache2": 0
                },
                "detailed_stats": {
                    "rejected_plmns": {},
                    "network_stats": {
                        "distribution": {}
                    }
                }
            }
        
        return data
    except Exception as e:
        print(f"ERREUR dans failure_analysis_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/success_rate")
async def success_rate_endpoint(country: str = None, operator: str = None):
    """Retourne les taux de succès avec filtrage optionnel par pays et opérateur"""
    try:
        from api.Kpi_service_clean import calculate_success_rate
        data = calculate_success_rate(country, operator)
        
        if not data:
            return {}
        
        return data
    except Exception as e:
        print(f"ERREUR dans success_rate_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/weekly_trends")
async def weekly_trends_endpoint(country: str = None, operator: str = None):
    """Retourne les tendances hebdomadaires avec filtrage optionnel par pays et opérateur"""
    try:
        from api.Kpi_service_clean import get_weekly_attach_trend
        data = get_weekly_attach_trend()
        
        # Filtrer les données si nécessaire
        if country or operator:
            filtered_data = []
            for item in data:
                if (not country or item.get('a_location_country') == country) and \
                   (not operator or item.get('a_UsedPLMNName') == operator):
                    filtered_data.append(item)
            return filtered_data
        
        return data
    except Exception as e:
        print(f"ERREUR dans weekly_trends_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/annual_trends")
async def annual_trends_endpoint(country: str = None, operator: str = None):
    """Retourne les tendances annuelles avec filtrage optionnel par pays et opérateur"""
    try:
        from api.Kpi_service_clean import get_annual_trend
        data = get_annual_trend()
        
        # Filtrer les données si nécessaire
        if country or operator:
            filtered_data = []
            for item in data:
                if (not country or item.get('a_location_country') == country) and \
                   (not operator or item.get('a_UsedPLMNName') == operator):
                    filtered_data.append(item)
            return filtered_data
        
        return data
    except Exception as e:
        print(f"ERREUR dans annual_trends_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/fail_causes")
async def fail_causes_endpoint(country: str = None, operator: str = None, verdict: str = None):
    """Retourne les causes détaillées d'échec avec les colonnes spécifiques demandées"""
    try:
        from api.Kpi_service_clean import get_failure_causes_by_country_operator
        
        # Si verdict n'est pas fourni, utiliser 'FAIL' par défaut
        if not verdict:
            verdict = 'FAIL'
        
        # Normaliser le verdict pour la cohérence
        verdict = verdict.upper()
        
        print(f"Appel à /api/fail_causes avec country={country}, operator={operator}, verdict={verdict}")
        
        # Utiliser la nouvelle fonction qui récupère toutes les colonnes demandées
        data = get_failure_causes_by_country_operator(country, operator, verdict)
        
        return data
    except Exception as e:
        print(f"ERREUR dans fail_causes_endpoint: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/operators")
async def get_operators(country: Optional[str] = None):
    """Retourne la liste des opérateurs disponibles dans la table steeringofroaming, avec un filtre optionnel par pays."""
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")

        cursor = connection.cursor(dictionary=True)
        
        params = []
        query = """
        SELECT DISTINCT a_UsedPLMNName as value
        FROM steeringofroaming 
        WHERE a_UsedPLMNName IS NOT NULL 
        AND TRIM(a_UsedPLMNName) != ''
        AND TCName = 'SteeringOfRoaming'
        """

        if country and country != 'all':
            query += " AND a_location_country = %s"
            params.append(country)

        query += " ORDER BY a_UsedPLMNName"
        
        print(f"Exécution de la requête SQL pour les opérateurs: {query} avec params: {params}")
        
        try:
            cursor.execute(query, tuple(params))
            raw_results = cursor.fetchall()
            print(f"Résultats bruts de la requête opérateurs: {raw_results}")
            operators = [{"value": row['value'], "label": row['value']} for row in raw_results]
            
            # Ajouter manuellement l'option pour les opérateurs NULL si des échecs non attribués existent
            # Pour l'instant, on l'ajoute systématiquement pour la visibilité
            operators.append({"value": "NULL", "label": "NULL (Échecs non attribués)"})
            
            print(f"Opérateurs formatés avant retour: {operators}")
            cursor.close()
            connection.close()
            return {"success": True, "data": operators}
        except Exception as e:
            print(f"Erreur lors de l'exécution de la requête pour les opérateurs: {e}")
            import traceback
            traceback.print_exc()
            if connection:
                connection.close()
            raise HTTPException(status_code=500, detail=f"Erreur de requête: {str(e)}")
    except Exception as e:
        print(f"Erreur lors de la récupération des opérateurs: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/service_chart")
async def get_service_chart():
    """Endpoint pour obtenir le graphique de répartition des services"""
    try:
        # Chercher le fichier dans le dossier temp
        temp_dir = tempfile.gettempdir()
        service_chart_path = os.path.join(temp_dir, "service_chart.png")
        
        if os.path.exists(service_chart_path):
            return FileResponse(service_chart_path, media_type="image/png")
        else:
            return {"error": "Le graphique de service n'existe pas"}
    except Exception as e:
        print(f"Erreur lors de la récupération du graphique de service: {e}")
        return {"error": str(e)}

@app.get("/api/kpi/week")
async def get_kpi_by_week(
    week: int = Query(..., description="Numéro de la semaine"),
    year: int = Query(..., description="Année"),
    country: str = Query(None, description="Pays à filtrer (optionnel)"),
    operator: str = Query(None, description="Opérateur à filtrer (optionnel)")
):
    """
    Endpoint pour obtenir les données KPI filtrées par semaine.
    Retourne les données de taux de succès par pays, taux d'attachement et performance réseau
    pour la semaine spécifiée.
    """
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor()
        
        # Calculer les dates de début et de fin de la semaine
        # La semaine ISO commence le lundi (1) et se termine le dimanche (7)
        first_day = datetime.strptime(f'{year}-W{week:02d}-1', "%Y-W%W-%w").date()
        last_day = first_day + timedelta(days=6)
        
        # Convertir en chaînes de caractères pour la requête SQL
        start_date = first_day.strftime('%Y-%m-%d')
        end_date = last_day.strftime('%Y-%m-%d')
        
        print(f"Filtrage par semaine {week} de {year}: du {start_date} au {end_date}")
        
        # 1. Requête pour les taux de succès par pays
        success_query = """
        SELECT 
            a_location_country as country,
            COUNT(*) as total_requests,
            SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) as successes,
            ROUND((SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            success_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            success_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        success_query += " GROUP BY a_location_country"
        
        cursor.execute(success_query, params)
        success_results = cursor.fetchall()
        
        # 2. Requête pour les taux d'attachement par pays
        attachment_query = """
        SELECT 
            a_location_country as country,
            COUNT(*) as total_tests,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 0 THEN 1 END) as attach0,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 1 THEN 1 END) as attach1,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 2 THEN 1 END) as attach2,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 3 THEN 1 END) as attach3,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 4 THEN 1 END) as attach4,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 5 THEN 1 END) as attach5,
            COUNT(CASE WHEN a_NrOfPlmnsRejected >= 6 THEN 1 END) as attach6
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            attachment_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            attachment_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        attachment_query += " GROUP BY a_location_country"
        
        cursor.execute(attachment_query, params)
        attachment_results = cursor.fetchall()
        
        # 3. Requête pour les performances réseau
        network_query = """
        SELECT 
            a_location_country as country,
            a_UsedPLMNName as operator,
            COUNT(*) as total_tests,
            AVG(CASE WHEN HttpDownloadDuration > 0 THEN HttpDownloadDuration ELSE NULL END) as avg_duration_sec,
            AVG(CASE WHEN HttpDownloadDataRate > 0 THEN HttpDownloadDataRate ELSE NULL END) as avg_data_rate_kbps
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
            AND HttpDownloadDuration IS NOT NULL
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            network_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            network_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        network_query += " GROUP BY a_location_country, a_UsedPLMNName"
        
        cursor.execute(network_query, params)
        network_results = cursor.fetchall()
        
        # Formater les résultats pour le taux de succès
        formatted_success = []
        for row in success_results:
            formatted_success.append({
                'country': row['country'],
                'success_rate': float(row['success_rate']),
                'total_requests': row['total_requests']
            })
        
        # Formater les résultats pour les taux d'attachement
        formatted_attachment = []
        for row in attachment_results:
            total = row['total_tests']
            attachment_distribution = {
                "0": (row['attach0'] / total * 100) if total > 0 else 0,
                "1": (row['attach1'] / total * 100) if total > 0 else 0,
                "2": (row['attach2'] / total * 100) if total > 0 else 0,
                "3": (row['attach3'] / total * 100) if total > 0 else 0,
                "4": (row['attach4'] / total * 100) if total > 0 else 0,
                "5": (row['attach5'] / total * 100) if total > 0 else 0,
                "6": (row['attach6'] / total * 100) if total > 0 else 0
            }
            
            # Calculer le taux de succès (somme des niveaux > 0)
            success_rate = sum(
                percentage for level, percentage in attachment_distribution.items() 
                if level != '0'
            )
            
            formatted_attachment.append({
                'country': row['country'],
                'attachment_distribution': attachment_distribution,
                'total_tests': total,
                'success_rate': round(success_rate, 2)
            })
        
        # Formater les résultats pour les performances réseau
        formatted_network = {
            'summary': {
                'avg_duration_sec': 0,
                'total_tests': 0,
                'total_countries': len(set([row['country'] for row in network_results]))
            },
            'by_country_operator': []
        }
        
        total_duration = 0
        total_tests = 0
        
        for row in network_results:
            formatted_network['by_country_operator'].append({
                'country': row['country'],
                'operator': row['operator'],
                'total_tests': row['total_tests'],
                'avg_duration_sec': float(row['avg_duration_sec']) if row['avg_duration_sec'] else 0,
                'avg_data_rate_kbps': float(row['avg_data_rate_kbps']) if row['avg_data_rate_kbps'] else 0,
                'avg_data_rate_mbps': float(row['avg_data_rate_kbps'] / 1000) if row['avg_data_rate_kbps'] else 0
            })
            
            total_duration += (float(row['avg_duration_sec']) * row['total_tests']) if row['avg_duration_sec'] else 0
            total_tests += row['total_tests']
        
        # Calculer la moyenne globale
        if total_tests > 0:
            formatted_network['summary']['avg_duration_sec'] = round(total_duration / total_tests, 2)
            formatted_network['summary']['total_tests'] = total_tests
        
        # Calculer les KPI d'attachement
        attachment_kpi = {
            'total_countries': len(formatted_attachment),
            'total_tests': sum(item['total_tests'] for item in formatted_attachment),
            'avg_success_rate': round(sum(item['success_rate'] for item in formatted_attachment) / len(formatted_attachment), 2) if formatted_attachment else 0
        }
        
        # Fermer la connexion
        cursor.close()
        connection.close()
        
        # Retourner toutes les données
        return {
            'success_by_country': formatted_success,
            'attachment_rates': formatted_attachment,
            'attachment_kpi': attachment_kpi,
            'network_performance': formatted_network,
            'period': {
                'type': 'week',
                'week': week,
                'year': year,
                'start_date': start_date,
                'end_date': end_date
            }
        }
        
    except Exception as e:
        print(f"Erreur dans get_kpi_by_week: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/kpi/month")
async def get_kpi_by_month(
    month: int = Query(..., description="Numéro du mois (1-12)"),
    year: int = Query(..., description="Année"),
    country: str = Query(None, description="Pays à filtrer (optionnel)"),
    operator: str = Query(None, description="Opérateur à filtrer (optionnel)")
):
    """
    Endpoint pour obtenir les données KPI filtrées par mois.
    Retourne les données de taux de succès par pays, taux d'attachement et performance réseau
    pour le mois spécifié.
    """
    try:
        connection = get_db_connection()
        if not connection:
            raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
        
        cursor = connection.cursor()
        
        # Calculer les dates de début et de fin du mois
        import calendar
        first_day = datetime(year, month, 1).date()
        last_day = datetime(year, month, calendar.monthrange(year, month)[1]).date()
        
        # Convertir en chaînes de caractères pour la requête SQL
        start_date = first_day.strftime('%Y-%m-%d')
        end_date = last_day.strftime('%Y-%m-%d')
        
        print(f"Filtrage par mois {month} de {year}: du {start_date} au {end_date}")
        
        # 1. Requête pour les taux de succès par pays
        success_query = """
        SELECT 
            a_location_country as country,
            COUNT(*) as total_requests,
            SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) as successes,
            ROUND((SUM(CASE WHEN Verdict = 'PASS' THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            success_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            success_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        success_query += " GROUP BY a_location_country"
        
        cursor.execute(success_query, params)
        success_results = cursor.fetchall()
        
        # 2. Requête pour les taux d'attachement par pays
        attachment_query = """
        SELECT 
            a_location_country as country,
            COUNT(*) as total_tests,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 0 THEN 1 END) as attach0,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 1 THEN 1 END) as attach1,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 2 THEN 1 END) as attach2,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 3 THEN 1 END) as attach3,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 4 THEN 1 END) as attach4,
            COUNT(CASE WHEN a_NrOfPlmnsRejected = 5 THEN 1 END) as attach5,
            COUNT(CASE WHEN a_NrOfPlmnsRejected >= 6 THEN 1 END) as attach6
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            attachment_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            attachment_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        attachment_query += " GROUP BY a_location_country"
        
        cursor.execute(attachment_query, params)
        attachment_results = cursor.fetchall()
        
        # 3. Requête pour les performances réseau
        network_query = """
        SELECT 
            a_location_country as country,
            a_UsedPLMNName as operator,
            COUNT(*) as total_tests,
            AVG(CASE WHEN HttpDownloadDuration > 0 THEN HttpDownloadDuration ELSE NULL END) as avg_duration_sec,
            AVG(CASE WHEN HttpDownloadDataRate > 0 THEN HttpDownloadDataRate ELSE NULL END) as avg_data_rate_kbps
        FROM steeringofroaming
        WHERE DATE(Timestamp) BETWEEN %s AND %s
            AND HttpDownloadDuration IS NOT NULL
        """
        
        params = [start_date, end_date]
        
        # Ajouter les filtres si spécifiés
        if country and country != 'all':
            network_query += " AND a_location_country = %s"
            params.append(country)
        
        if operator and operator != 'all':
            network_query += " AND a_UsedPLMNName = %s"
            params.append(operator)
        
        network_query += " GROUP BY a_location_country, a_UsedPLMNName"
        
        cursor.execute(network_query, params)
        network_results = cursor.fetchall()
        
        # Formater les résultats pour le taux de succès
        formatted_success = []
        for row in success_results:
            formatted_success.append({
                'country': row['country'],
                'success_rate': float(row['success_rate']),
                'total_requests': row['total_requests']
            })
        
        # Formater les résultats pour les taux d'attachement
        formatted_attachment = []
        for row in attachment_results:
            total = row['total_tests']
            attachment_distribution = {
                "0": (row['attach0'] / total * 100) if total > 0 else 0,
                "1": (row['attach1'] / total * 100) if total > 0 else 0,
                "2": (row['attach2'] / total * 100) if total > 0 else 0,
                "3": (row['attach3'] / total * 100) if total > 0 else 0,
                "4": (row['attach4'] / total * 100) if total > 0 else 0,
                "5": (row['attach5'] / total * 100) if total > 0 else 0,
                "6": (row['attach6'] / total * 100) if total > 0 else 0
            }
            
            # Calculer le taux de succès (somme des niveaux > 0)
            success_rate = sum(
                percentage for level, percentage in attachment_distribution.items() 
                if level != '0'
            )
            
            formatted_attachment.append({
                'country': row['country'],
                'attachment_distribution': attachment_distribution,
                'total_tests': total,
                'success_rate': round(success_rate, 2)
            })
        
        # Formater les résultats pour les performances réseau
        formatted_network = {
            'summary': {
                'avg_duration_sec': 0,
                'total_tests': 0,
                'total_countries': len(set([row['country'] for row in network_results]))
            },
            'by_country_operator': []
        }
        
        total_duration = 0
        total_tests = 0
        
        for row in network_results:
            formatted_network['by_country_operator'].append({
                'country': row['country'],
                'operator': row['operator'],
                'total_tests': row['total_tests'],
                'avg_duration_sec': float(row['avg_duration_sec']) if row['avg_duration_sec'] else 0,
                'avg_data_rate_kbps': float(row['avg_data_rate_kbps']) if row['avg_data_rate_kbps'] else 0,
                'avg_data_rate_mbps': float(row['avg_data_rate_kbps'] / 1000) if row['avg_data_rate_kbps'] else 0
            })
            
            total_duration += (float(row['avg_duration_sec']) * row['total_tests']) if row['avg_duration_sec'] else 0
            total_tests += row['total_tests']
        
        # Calculer la moyenne globale
        if total_tests > 0:
            formatted_network['summary']['avg_duration_sec'] = round(total_duration / total_tests, 2)
            formatted_network['summary']['total_tests'] = total_tests
        
        # Calculer les KPI d'attachement
        attachment_kpi = {
            'total_countries': len(formatted_attachment),
            'total_tests': sum(item['total_tests'] for item in formatted_attachment),
            'avg_success_rate': round(sum(item['success_rate'] for item in formatted_attachment) / len(formatted_attachment), 2) if formatted_attachment else 0
        }
        
        # Fermer la connexion
        cursor.close()
        connection.close()
        
        # Retourner toutes les données
        return {
            'success_by_country': formatted_success,
            'attachment_rates': formatted_attachment,
            'attachment_kpi': attachment_kpi,
            'network_performance': formatted_network,
            'period': {
                'type': 'month',
                'month': month,
                'year': year,
                'start_date': start_date,
                'end_date': end_date
            }
        }
        
    except Exception as e:
        print(f"Erreur dans get_kpi_by_month: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/network_performance_stats")
async def get_network_performance_stats_endpoint(country: str = None, operator: str = None):
    """Endpoint pour obtenir les statistiques de performance réseau"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_network_performance_stats_endpoint avec country={country}, operator={operator}")
        
        from api.Kpi_service_clean import get_network_performance_stats
        data = get_network_performance_stats()
        
        # Si aucune donnée n'est retournée, renvoyer un objet vide
        if not data:
            print("Aucune donnée de performance réseau disponible")
            return {}
            
        print(f"Données de performance réseau récupérées")
        return data
    except Exception as e:
        print(f"Erreur dans get_network_performance_stats_endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/http_download_performance")
async def get_http_download_performance_endpoint(country: str = None, operator: str = None):
    """Endpoint pour obtenir les performances de téléchargement HTTP"""
    try:
        print(f"\n[{datetime.now()}] Appel à get_http_download_performance_endpoint avec country={country}, operator={operator}")
        
        from api.Kpi_service_clean import get_http_download_performance
        data = get_http_download_performance(country, operator)
        
        # Si aucune donnée n'est retournée, renvoyer un objet vide
        if not data:
            print("Aucune donnée de performance HTTP disponible")
            return {}
            
        print(f"Données de performance HTTP récupérées")
        return data
    except Exception as e:
        print(f"Erreur dans get_http_download_performance_endpoint: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/network_type_distribution")
async def get_network_type_distribution(country: str = None, operator: str = None):
    """
    Endpoint pour obtenir la distribution des types de réseau (3G/4G) depuis la table steeringofroaming
    Filtre par TCName=SteeringOfRoaming et exclut les entrées avec Verdict=FAIL ou a_NetworkType vide
    """
    try:
        print(f"\n[{datetime.now()}] Appel à get_network_type_distribution avec country={country}, operator={operator}")
        
        # Connexion à la base de données
        conn = sqlite3.connect('roaming_kpi.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Construction de la requête SQL avec les filtres
        query = """
        SELECT 
            a_NetworkType as network_type, 
            a_location_country as country,
            a_location as location,
            a_UsedPLMNName as operator,
            COUNT(*) as count
        FROM steeringofroaming
        WHERE 
            TCName = 'SteeringOfRoaming' 
            AND a_NetworkType IS NOT NULL 
            AND a_NetworkType != '' 
            AND Verdict != 'FAIL'
        """
        
        params = []
        
        if country and country != 'all':
            query += " AND a_location_country = ? "
            params.append(country)
            
        if operator and operator != 'all':
            query += " AND a_UsedPLMNName = ? "
            params.append(operator)
            
        query += " GROUP BY network_type, country, location, operator"
        
        # Exécution de la requête
        cursor.execute(query, params)
        results = cursor.fetchall()
        
        # Organisation des données
        network_types = {}
        countries_by_network = {}
        
        for row in results:
            network_type = row['network_type']
            
            # Comptage des occurrences par type de réseau
            if network_type not in network_types:
                network_types[network_type] = 0
            network_types[network_type] += row['count']
            
            # Organisation des données par pays et opérateur pour chaque type de réseau
            if network_type not in countries_by_network:
                countries_by_network[network_type] = []
                
            countries_by_network[network_type].append({
                'country': row['country'],
                'location': row['location'],
                'operator': row['operator'],
                'count': row['count']
            })
        
        conn.close()
        
        return {
            "success": True,
            "message": f"Distribution des types de réseau récupérée avec succès",
            "data": {
                "network_types": network_types,
                "countries_by_network": countries_by_network
            }
        }
    except Exception as e:
        print(f"Erreur dans get_network_type_distribution: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=str(e))

# Monter les répertoires statiques
app.mount("/charts", StaticFiles(directory=str(charts_dir)), name="charts")

# Inclure les routes
app.include_router(roaming_routes)
app.include_router(kpi_routes)

# Gestionnaire d'erreurs global
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    print(f"Erreur sur {request.url.path}: {str(exc)}")  # Log l'erreur
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": str(exc),
            "path": request.url.path
        }
    )

# Point d'entrée pour uvicorn
if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    debug = os.getenv("API_DEBUG", "True").lower() == "true"
    
    print(f"[{datetime.now()}] Démarrage du serveur sur {host}:{port}")
    uvicorn.run("app:app", host=host, port=port, reload=debug) 