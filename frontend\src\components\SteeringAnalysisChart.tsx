import RefreshIcon from '@mui/icons-material/Refresh';
import { <PERSON>, <PERSON><PERSON>, Card, CardContent, Typography } from '@mui/material';
import React, { useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, Legend, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { getSteeringChartData } from '../services/kpiService';

interface SteeringData {
  country: string;
  success_rate: number;
  total_attempts: number;
  failures: number;
}

interface SteeringAnalysisChartProps {
  title?: string;
  height?: string;
}

const SteeringAnalysisChart: React.FC<SteeringAnalysisChartProps> = ({ title = "Analyse du Steering par Pays", height = "400px" }) => {
  const [data, setData] = useState<SteeringData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await getSteeringChartData();
      
      if (!result.success) {
        throw new Error(result.error || 'Erreur lors de la récupération des données');
      }
      
      setData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Une erreur est survenue');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // Préparer les données pour le graphique
  const chartData = data.map(item => ({
    country: item.country,
    successful: Math.round(item.total_attempts * (item.success_rate / 100)),
    failed: item.failures,
    total: item.total_attempts,
    successRate: item.success_rate
  })).sort((a, b) => b.total - a.total);

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6" component="div">
            {title}
          </Typography>
          <Button
            variant="contained"
            startIcon={<RefreshIcon />}
            onClick={fetchData}
            disabled={loading}
          >
            Rafraîchir
          </Button>
        </Box>

        {error && (
          <Typography color="error" gutterBottom>
            {error}
          </Typography>
        )}

        {loading ? (
          <Typography>Chargement des données...</Typography>
        ) : (
          <Box sx={{ width: '100%', height }}>
            <ResponsiveContainer>
              <BarChart
                data={chartData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="country" 
                  angle={-45}
                  textAnchor="end"
                  height={100}
                  interval={0}
                />
                <YAxis />
                <Tooltip 
                  formatter={(value: number) => [value, 'Tentatives']}
                  labelFormatter={(label) => `Pays: ${label}`}
                />
                <Legend />
                <Bar dataKey="successful" name="Tentatives réussies" stackId="a" fill="#4caf50" />
                <Bar dataKey="failed" name="Tentatives échouées" stackId="a" fill="#f44336" />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default SteeringAnalysisChart; 