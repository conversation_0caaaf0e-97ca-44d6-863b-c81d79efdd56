import React, { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import { FaAngle<PERSON>eft, FaAngleRight, FaBars, FaBell, FaChartBar, FaChartLine, FaCog, FaGlobe, FaHome, FaSignOutAlt, FaTimes } from 'react-icons/fa';
import { createBrowserRouter, createRoutesFromElements, Link, Navigate, Route, RouterProvider } from 'react-router-dom';

// Importation des pages et composants
import ConnectionTest from './components/ConnectionTest';
import ReportGenerator from './components/ReportGenerator';
import TokenTest from './components/TokenTest';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import DashboardPage from './pages/DashboardPage';
import HomePage from './pages/HomePage';
import InternationalRoaming from './pages/InternationalRoaming';
import LoginPage from './pages/LoginPage';
import RoamingMonitoring from './pages/RoamingMonitoring';
import RoamingSettings from './pages/RoamingSettings';
import RoamingVisualizationPage from './pages/RoamingVisualizationPage';
import TrendsPage from './pages/TrendsPage';
// Importation des nouvelles pages de tableau de bord
import HttpDashboardPage from './pages/HttpDashboardPage';
import NetworkDashboardPage from './pages/NetworkDashboardPage';
import SelectTablePage from './pages/SelectTablePage';
import SteeringDashboardPage from './pages/SteeringDashboardPage';
// Importation des nouvelles pages de tableau de bord

// Page de protection pour les routes authentifiées
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { status } = useAuth();
  
  // Afficher un indicateur de chargement pendant la vérification
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }
  
  // Rediriger vers la page de connexion si l'utilisateur n'est pas authentifié
  if (status === 'unauthenticated') {
    return <Navigate to="/login" replace />;
  }
  
  // Afficher le contenu si l'utilisateur est authentifié
  return <>{children}</>;
};

// Redirection automatique vers le dashboard pour les utilisateurs authentifiés
const PublicRoute = ({ children }: { children: React.ReactNode }) => {
  const { status } = useAuth();
  
  if (status === 'loading') {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }
  
  // Si l'utilisateur est authentifié et essaie d'accéder à une route publique,
  // le rediriger vers la page de sélection de table
  if (status === 'authenticated') {
    return <Navigate to="/select-table" replace />;
  }
  
  // Sinon afficher le contenu de la route publique
  return <>{children}</>;
};

// Composant de mise en page avec navigation
const Layout = ({ children }: { children: React.ReactNode }) => {
  const { status, logout, user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [windowWidth, setWindowWidth] = useState<number>(window.innerWidth);


  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(windowWidth);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  if (status !== 'authenticated') return <>{children}</>;
  
  const handleLogout = async () => {
    try {
      await logout();
      // La redirection sera gérée par le listener onAuthStateChanged
    } catch (error) {
      console.error('Erreur lors de la déconnexion:', error);
    }
  };
  
  const toggleSidebar = () => {
    // Sur mobile: ouvrir/fermer la sidebar
    if (windowWidth < 768) {
      setSidebarOpen(!sidebarOpen);
    } else {
      // Sur desktop: collapser/décollapser la sidebar
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  return (
    <div className="flex h-screen bg-gray-100 relative">
      {/* Bouton hamburger pour mobile */}
      <button 
        onClick={toggleSidebar}
        className="md:hidden fixed top-4 left-4 z-20 p-2 rounded-md bg-primary-700 text-white"
        aria-label="Toggle menu"
      >
        {sidebarOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
      </button>
      
      {/* Bouton pour réduire la sidebar sur desktop */}
      <button
        onClick={toggleSidebar}
        className="hidden md:block fixed top-4 left-4 z-20 p-2 rounded-md bg-primary-700 text-white ml-64 transform transition-all duration-300 ease-in-out"
        style={{ marginLeft: sidebarCollapsed ? '4.5rem' : '16rem' }}
        aria-label="Collapse sidebar"
      >
        {sidebarCollapsed ? <FaAngleRight size={20} /> : <FaAngleLeft size={20} />}
      </button>
      
      {/* Sidebar de navigation - responsive */}
      <div 
        className={`transform transition-all duration-300 ease-in-out fixed md:static inset-y-0 left-0 bg-primary-800 text-white z-10 overflow-y-auto ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full md:translate-x-0'
        } ${
          sidebarCollapsed ? 'w-16 md:w-16' : 'w-64'
        }`}
      >
        <div className={`p-4 flex justify-between items-center ${sidebarCollapsed ? 'flex-col' : ''}`}>
          {sidebarCollapsed ? (
            <h1 className="text-2xl font-bold">GR</h1>
          ) : (
            <h1 className="text-2xl font-bold">Gestion Roaming</h1>
          )}
          <button 
            onClick={toggleSidebar}
            className="md:hidden text-white p-1 rounded hover:bg-primary-700"
            aria-label="Fermer le menu"
          >
            <FaTimes size={16} />
          </button>
        </div>
        
        {user && !sidebarCollapsed && (
          <div className="px-4 text-sm opacity-80">
            Connecté en tant que {user.name}
            {user.email && (
              <div className="text-xs opacity-60 mt-1">{user.email}</div>
            )}
          </div>
        )}
        
        <nav className="mt-6">
          <ul>
            <li>
              <Link 
                to="/select-table" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Tableau de bord"
              >
                <FaHome className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Tableau de bord</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/trends" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Tendances"
              >
                <FaChartLine className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Tendances</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/monitoring" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Surveillance"
              >
                <FaBell className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Surveillance</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/international" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="International"
              >
                <FaGlobe className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>International</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/reports" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Rapports"
              >
                <FaChartBar className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Rapports</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/visualization" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Visualisation"
              >
                <FaChartLine className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Visualisation</span>}
              </Link>
            </li>
            <li>
              <Link 
                to="/settings" 
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 hover:bg-primary-700`}
                onClick={() => windowWidth < 768 && setSidebarOpen(false)}
                title="Paramètres"
              >
                <FaCog className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Paramètres</span>}
              </Link>
            </li>
            <li>
              <button 
                onClick={handleLogout}
                className={`flex ${sidebarCollapsed ? 'justify-center' : 'items-center'} px-4 py-3 w-full text-left hover:bg-primary-700 text-red-300`}
                title="Déconnexion"
              >
                <FaSignOutAlt className={sidebarCollapsed ? '' : 'mr-3'} />
                {!sidebarCollapsed && <span>Déconnexion</span>}
              </button>
            </li>
          </ul>
        </nav>
      </div>
      
      {/* Overlay pour fermer le menu sur mobile quand ouvert */}
      {sidebarOpen && windowWidth < 768 && (
        <div 
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-0"
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
      
      {/* Contenu principal - ajustement de marge pour sidebar collapsée */}
      <div 
        className={`flex-1 overflow-auto transition-all duration-300 ease-in-out`}
        style={{ 
          marginLeft: windowWidth >= 768 && sidebarCollapsed ? '4rem' : '0'
        }}
      >
        {children}
      </div>
    </div>
  );
};

function AppContent() {
  const router = createBrowserRouter(
    createRoutesFromElements(
      <>
        <Route path="/" element={
          <PublicRoute>
            <HomePage />
          </PublicRoute>
        } />
        <Route path="/login" element={
          <PublicRoute>
            <LoginPage />
          </PublicRoute>
        } />
        {/* Nouvelle route pour la page de sélection de table */}
        <Route path="/select-table" element={
          <ProtectedRoute>
            <Layout>
              <SelectTablePage />
            </Layout>
          </ProtectedRoute>
        } />
        {/* Route existante du tableau de bord général */}
        <Route path="/dashboard" element={
          <ProtectedRoute>
            <Layout>
              <DashboardPage />
            </Layout>
          </ProtectedRoute>
        } />
        {/* Nouvelles routes pour les tableaux de bord spécifiques */}
        <Route path="/dashboard/steering" element={
          <ProtectedRoute>
            <Layout>
              <SteeringDashboardPage />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/dashboard/http" element={
          <ProtectedRoute>
            <Layout>
              <HttpDashboardPage />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/dashboard/network" element={
          <ProtectedRoute>
            <Layout>
              <NetworkDashboardPage />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/trends" element={
          <ProtectedRoute>
            <Layout>
              <TrendsPage />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/monitoring" element={
          <ProtectedRoute>
            <Layout>
              <RoamingMonitoring />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/international" element={
          <ProtectedRoute>
            <Layout>
              <InternationalRoaming />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/reports" element={
          <ProtectedRoute>
            <Layout>
              <ReportGenerator />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/visualization" element={
          <ProtectedRoute>
            <Layout>
              <RoamingVisualizationPage />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/settings" element={
          <ProtectedRoute>
            <Layout>
              <RoamingSettings />
            </Layout>
          </ProtectedRoute>
        } />
        <Route path="/token-test" element={
          <ProtectedRoute>
            <Layout>
              <TokenTest />
            </Layout>
          </ProtectedRoute>
        } />
        {/* Rediriger vers la page de sélection au lieu du tableau de bord général */}
        <Route path="*" element={<Navigate to="/select-table" replace />} />
      </>
    ),
    {
      basename: import.meta.env.BASE_URL,
    }
  );

  return (
    <>
      <RouterProvider router={router} />
      <ConnectionTest />
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 3000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            style: {
              background: '#4CAF50',
              color: '#fff',
            },
          },
          error: {
            duration: 4000,
            style: {
              background: '#F44336',
              color: '#fff',
            },
          },
        }}
      />
    </>
  );
}

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
