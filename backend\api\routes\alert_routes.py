from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy import create_engine, text, func, desc
from sqlalchemy.orm import Session
from datetime import datetime, timedelta
from typing import List, Optional
from pydantic import BaseModel
import pandas as pd
from ..get_db_connection import get_db_connection

router = APIRouter()

# Modèle Pydantic pour les alertes
class Alert(BaseModel):
    country: str
    success_rate: float
    failure_rate: float
    type_alert: str
    severity: str
    description: str
    date: str

@router.get("/api/alerts", response_model=List[Alert])
async def get_alerts():
    """
    Récupère les alertes automatiques basées sur les critères suivants:
    - Taux de succès inférieur à 70% par pays
    - Augmentation des échecs de plus de 30% par rapport à la moyenne des 7 derniers jours
    """
    engine = get_db_connection()
    if not engine:
        raise HTTPException(status_code=500, detail="Impossible de se connecter à la base de données")
    
    alerts = []
    
    try:
        with engine.connect() as connection:
            # 1. Alertes pour les pays avec un taux de succès inférieur à 70%
            # Utiliser la même requête que celle utilisée dans le dashboard pour garantir la cohérence
            low_success_query = text("""
                SELECT 
                    a_location_country as country,
                    COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) as success_count,
                    COUNT(*) as total_count,
                    ROUND(COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
                    ROUND(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as failure_rate,
                    COUNT(CASE WHEN (Verdict = 'SUCCESS' OR Verdict = 'PASS') AND TCName = 'SteeringOfRoaming' THEN 1 END) as steering_success,
                    COUNT(CASE WHEN TCName = 'SteeringOfRoaming' THEN 1 END) as steering_total
                FROM steeringofroaming
                WHERE
                    TCName = 'SteeringOfRoaming'
                    AND 
                    a_location_country IS NOT NULL 
                    AND a_location_country != ''
                    AND Timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY a_location_country
                HAVING 
                    total_count > 10  -- Ignorer les pays avec trop peu de données
                    AND success_rate < 70
                ORDER BY success_rate ASC
            """)
            
            try:
                low_success_results = connection.execute(low_success_query).fetchall()
                
                for row in low_success_results:
                    country = row.country
                    success_rate = float(row.success_rate)
                    failure_rate = float(row.failure_rate)
                    total_count = row.total_count
                    
                    # Ne générer une alerte que si nous avons suffisamment de données
                    if total_count < 10:
                        continue
                    
                    # Déterminer la sévérité en fonction du taux de succès
                    severity = "high" if success_rate < 50 else "medium" if success_rate < 60 else "low"
                    
                    alerts.append(Alert(
                        country=country,
                        success_rate=success_rate,
                        failure_rate=failure_rate,
                        type_alert="low_success_rate",
                        severity=severity,
                        description=f"Taux de succès critique de {success_rate}% en {country} (sur {total_count} tests)",
                        date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ))
            except Exception as e:
                print(f"Erreur lors de la récupération des alertes de taux de succès faible: {str(e)}")
                import traceback
                traceback.print_exc()
            
            # 2. Alertes pour les pays avec une augmentation des échecs
            # Comparer les données récentes avec les données historiques
            try:
                spike_query = text("""
                    WITH recent_data AS (
                        SELECT 
                            a_location_country as country,
                            COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) as recent_failures,
                            COUNT(*) as recent_total,
                            ROUND(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as recent_failure_rate
                        FROM steeringofroaming
                        WHERE 
                            a_location_country IS NOT NULL 
                            AND a_location_country != ''
                            AND Timestamp >= DATE_SUB(NOW(), INTERVAL 1 DAY)
                        GROUP BY a_location_country
                        HAVING recent_total > 5  -- Ignorer les pays avec trop peu de données récentes
                    ),
                    historical_data AS (
                        SELECT 
                            a_location_country as country,
                            COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) as historical_failures,
                            COUNT(*) as historical_total,
                            ROUND(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as historical_failure_rate
                        FROM steeringofroaming
                        WHERE 
                            a_location_country IS NOT NULL 
                            AND a_location_country != ''
                            AND Timestamp BETWEEN DATE_SUB(NOW(), INTERVAL 8 DAY) AND DATE_SUB(NOW(), INTERVAL 1 DAY)
                        GROUP BY a_location_country
                        HAVING historical_total > 20  -- Ignorer les pays avec trop peu de données historiques
                    )
                    SELECT 
                        r.country,
                        r.recent_failures,
                        r.recent_total,
                        r.recent_failure_rate,
                        h.historical_failures,
                        h.historical_total,
                        h.historical_failure_rate,
                        ROUND(100 - r.recent_failure_rate, 2) as success_rate,
                        r.recent_failure_rate as failure_rate,
                        ROUND(((r.recent_failure_rate - h.historical_failure_rate) / NULLIF(h.historical_failure_rate, 0)) * 100, 2) as increase_percentage
                    FROM 
                        recent_data r
                    JOIN 
                        historical_data h ON r.country = h.country
                    WHERE 
                        r.recent_failure_rate > h.historical_failure_rate
                        AND ((r.recent_failure_rate - h.historical_failure_rate) / NULLIF(h.historical_failure_rate, 0)) * 100 > 30
                    ORDER BY 
                        increase_percentage DESC
                """)
                
                spike_results = connection.execute(spike_query).fetchall()
                
                for row in spike_results:
                    country = row.country
                    success_rate = float(row.success_rate)
                    failure_rate = float(row.failure_rate)
                    increase_percentage = float(row.increase_percentage)
                    recent_total = row.recent_total
                    
                    # Déterminer la sévérité en fonction de l'augmentation des échecs
                    severity = "high" if increase_percentage > 100 else "medium" if increase_percentage > 50 else "low"
                    
                    alerts.append(Alert(
                        country=country,
                        success_rate=success_rate,
                        failure_rate=failure_rate,
                        type_alert="sudden_failure_increase",
                        severity=severity,
                        description=f"Augmentation de {increase_percentage}% des échecs en {country} (sur {recent_total} tests récents)",
                        date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    ))
            except Exception as e:
                print(f"Erreur lors de la récupération des alertes d'augmentation d'échecs: {str(e)}")
                import traceback
                traceback.print_exc()
                
                # En cas d'erreur, essayer une requête plus simple pour les pays avec un taux d'échec élevé
                try:
                    high_failure_query = text("""
                        SELECT 
                            a_location_country as country,
                            COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) as success_count,
                            COUNT(*) as total_count,
                            ROUND(COUNT(CASE WHEN Verdict = 'SUCCESS' OR Verdict = 'PASS' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate,
                            ROUND(COUNT(CASE WHEN Verdict = 'FAIL' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as failure_rate
                        FROM steeringofroaming
                        WHERE 
                            a_location_country IS NOT NULL 
                            AND a_location_country != ''
                            AND Timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                        GROUP BY a_location_country
                        HAVING 
                            total_count > 20  -- Ignorer les pays avec trop peu de données
                            AND failure_rate > 30  -- Pays avec plus de 30% d'échecs
                        ORDER BY failure_rate DESC
                        LIMIT 5  -- Limiter aux 5 pays les plus problématiques
                    """)
                    
                    high_failure_results = connection.execute(high_failure_query).fetchall()
                    
                    for row in high_failure_results:
                        country = row.country
                        success_rate = float(row.success_rate)
                        failure_rate = float(row.failure_rate)
                        total_count = row.total_count
                        
                        # Déterminer la sévérité en fonction du taux d'échec
                        severity = "high" if failure_rate > 50 else "medium" if failure_rate > 40 else "low"
                        
                        alerts.append(Alert(
                            country=country,
                            success_rate=success_rate,
                            failure_rate=failure_rate,
                            type_alert="high_failure_rate",
                            severity=severity,
                            description=f"Taux d'échec élevé de {failure_rate}% en {country} (sur {total_count} tests)",
                            date=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        ))
                except Exception as e:
                    print(f"Erreur lors de la récupération des alertes d'échec élevé: {str(e)}")
                    import traceback
                    traceback.print_exc()
    
    except Exception as e:
        print(f"Erreur lors de la récupération des alertes: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Erreur lors de la récupération des alertes: {str(e)}")
    
    # Si aucune alerte n'est trouvée, retourner une liste vide
    return alerts 