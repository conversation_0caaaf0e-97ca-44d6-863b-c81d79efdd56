CREATE TABLE SteeringOfRoaming (
    TestcaseId BIGINT,
    OrderId BIGINT,
    TCName TEXT,
    Timestamp DATETIME(3),
    Verdict TEXT,
    TestDefinitionPath TEXT,
    User TEXT,
    UserGroup TEXT,
    errorId INT,
    errorText TEXT,
    TCTestDefinitionId BIGINT,
    errorSideCountry TEXT,
    errorSideId INT,
    errorSideLocation TEXT,
    errorSideNumber TEXT,
    errorSidePlmn TEXT,
    errorSideProbe TEXT,
    errorSideRxLevel TEXT,
    errorSideUsedPLMNName TEXT,
    errorState TEXT,
    errorStateId INT,
    Completed TINYINT,
    Failure TINYINT,
    Incomplete TINYINT,
    L3_Flag TEXT,
    ServiceType TEXT,
    Success TINYINT,
    a_EcNO DECIMAL(5,1),
    a_HappyEyeBallSelectedIPVersion TEXT,
    a_IP_Version TEXT,
    a_IPv4_Used TEXT,
    a_IPv6_Used TEXT,
    a_InitialLupReqTime DATETIME(3),
    a_LTE_RgAttachDuration TEXT,
    a_LupAcceptDuration TIME(3),
    a_LupAcceptDuration_All_VPLMN TIME(3),
    a_LupDuration TIME(3),
    a_LupMode TEXT,
    a_LupRejectDuration TIME(3),
    a_NrOfLupRejects_All_VPLMN INT,
    a_NrOfLupRequests INT,
    a_NrOfLupRequests_All_VPLMN INT,
    a_NrOfPlmnsRejected INT,
    a_OverallNrOfLupRequests INT,
    a_RejectCauses TEXT,
    a_RejectedPLMNs TEXT,
    a_SimAuthenticationAfterAttach TINYINT,
    a_SimAuthenticationAfterLup TINYINT,
    a_TAC TEXT,
    a_UsedPLMNNameShort TEXT,
    a_VPLMN_registered TEXT,
    b_EcNO DECIMAL(5,1),
    b_HappyEyeBallSelectedIPVersion TEXT,
    b_IP_Version TEXT,
    b_IPv4_Used TEXT,
    b_IPv6_Used TEXT,
    b_InitialLupReqTime DATETIME(3),
    b_LTE_RgAttachDuration TEXT,
    b_LupAcceptDuration TIME(3),
    b_LupDuration TIME(3),
    b_LupMode TEXT,
    b_LupRejectDuration TIME(3),
    b_NrOfLupRequests INT,
    b_NrOfPlmnsRejected INT,
    b_OverallNrOfLupRequests INT,
    b_RejectCauses TEXT,
    b_RejectedPLMNs TEXT,
    b_SimAuthenticationAfterAttach TINYINT,
    b_SimAuthenticationAfterLup TINYINT,
    b_TAC TEXT,
    b_UsedPLMNNameShort TEXT,
    c_EcNO DECIMAL(5,1),
    c_InitialLupReqTime DATETIME(3),
    c_LTE_RgAttachDuration TEXT,
    c_LupAcceptDuration TIME(3),
    c_LupDuration TIME(3),
    c_LupMode TEXT,
    c_LupRejectDuration TIME(3),
    c_NrOfLupRequests INT,
    c_NrOfPlmnsRejected INT,
    c_OverallNrOfLupRequests INT,
    c_RejectCauses TEXT,
    c_RejectedPLMNs TEXT,
    c_UsedPLMNNameShort TEXT,
    d_EcNO DECIMAL(5,1),
    d_InitialLupReqTime DATETIME(3),
    d_LTE_RgAttachDuration TEXT,
    d_LupAcceptDuration TIME(3),
    d_LupDuration TIME(3),
    d_LupMode TEXT,
    d_LupRejectDuration TIME(3),
    d_NrOfLupRequests INT,
    d_NrOfPlmnsRejected INT,
    d_OverallNrOfLupRequests INT,
    d_RejectCauses TEXT,
    d_RejectedPLMNs TEXT,
    d_UsedPLMNNameShort TEXT,
    CauseText_L3 TEXT,
    CauseValue_L3 INT,
    TCDuration TIME(3),
    TestId BIGINT,
    TestrunId BIGINT,
    a_5QI_dedicated_L3 TEXT,
    a_5QI_default_L3 TEXT,
    a_LTE_Freq TEXT,
    b_5QI_dedicated_L3 TEXT,
    b_5QI_default_L3 TEXT,
    b_LTE_Freq TEXT,
    c_LTE_Freq TEXT,
    d_LTE_Freq TEXT,
    DayOfMonth INT,
    DayOfWeek TEXT,
    HourOfDay INT,
    MonthOfYear INT,
    TimeZone TEXT,
    WeekOfYear INT,
    Distance TEXT,
    ExecutionHost TEXT,
    ExecutionId BIGINT,
    ExternalNumber TEXT,
    ExternalNumberOrURI TEXT,
    a_5G_NSA_availability TEXT,
    a_5G_NSA_used TEXT,
    a_ATR_Mobile TEXT,
    a_ATR_SimEmu TEXT,
    a_CI TEXT,
    a_DCNR_restricted TEXT,
    a_EcIo DECIMAL(5,1),
    a_LAC TEXT,
    a_LTE_Band TEXT,
    a_LTE_Bandwidth TEXT,
    a_MMEName TEXT,
    a_NID TEXT,
    a_NRI_cs TEXT,
    a_NRI_ps TEXT,
    a_NR_Band TEXT,
    a_NR_DL_Bandwidth TEXT,
    a_NR_RSRP DECIMAL(5,1),
    a_NR_RSRQ DECIMAL(5,1),
    a_NR_SINR DECIMAL(5,1),
    a_NR_pCI TEXT,
    a_NSSAI TEXT,
    a_RAC TEXT,
    a_RSCP DECIMAL(5,1),
    a_RSRP DECIMAL(5,1),
    a_RxLevel DECIMAL(5,1),
    a_SID TEXT,
    a_SIM_AuthenticationEnd DATETIME(3),
    a_SIM_AuthenticationStart DATETIME(3),
    a_TADIG TEXT,
    a_UsedPLMN TEXT,
    a_UsedPLMNName TEXT,
    a_imsi TEXT,
    a_location TEXT,
    a_location_country TEXT,
    a_number TEXT,
    a_plmn TEXT,
    a_plmnShort TEXT,
    a_probe TEXT,
    a_uuCqiAverage TEXT,
    a_uuCqiSamples TEXT,
    a_uuPppHsdpaUsed TEXT,
    a_uuPppHsupaUsed TEXT,
    b_5G_NSA_availability TEXT,
    b_5G_NSA_used TEXT,
    b_ATR_Mobile TEXT,
    b_ATR_SimEmu TEXT,
    b_CI TEXT,
    b_DCNR_restricted TEXT,
    b_EcIo DECIMAL(5,1),
    b_LAC TEXT,
    b_LTE_Band TEXT,
    b_LTE_Bandwidth TEXT,
    b_MMEName TEXT,
    b_NID TEXT,
    b_NRI_cs TEXT,
    b_NRI_ps TEXT,
    b_NR_Band TEXT,
    b_NR_DL_Bandwidth TEXT,
    b_NR_RSRP DECIMAL(5,1),
    b_NR_RSRQ DECIMAL(5,1),
    b_NR_SINR DECIMAL(5,1),
    b_NR_pCI TEXT,
    b_NSSAI TEXT,
    b_RAC TEXT,
    b_RSCP DECIMAL(5,1),
    b_RSRP DECIMAL(5,1),
    b_RxLevel DECIMAL(5,1),
    b_SID TEXT,
    b_SIM_AuthenticationEnd DATETIME(3),
    b_SIM_AuthenticationStart DATETIME(3),
    b_TADIG TEXT,
    b_UsedPLMN TEXT,
    b_UsedPLMNName TEXT,
    b_imsi TEXT,
    b_location TEXT,
    b_location_country TEXT,
    b_number TEXT,
    b_plmn TEXT,
    b_plmnShort TEXT,
    b_probe TEXT,
    b_uuCqiAverage TEXT,
    b_uuCqiSamples TEXT,
    b_uuPppHsdpaUsed TEXT,
    b_uuPppHsupaUsed TEXT,
    c_ATR_Mobile TEXT,
    c_ATR_SimEmu TEXT,
    c_CI TEXT,
    c_DCNR_restricted TEXT,
    c_EcIo DECIMAL(5,1),
    c_LAC TEXT,
    c_LTE_Band TEXT,
    c_LTE_Bandwidth TEXT,
    c_MMEName TEXT,
    c_NID TEXT,
    c_NRI_cs TEXT,
    c_NRI_ps TEXT,
    c_NR_Band TEXT,
    c_NR_DL_Bandwidth TEXT,
    c_NR_RSRP DECIMAL(5,1),
    c_NR_RSRQ DECIMAL(5,1),
    c_NR_SINR DECIMAL(5,1),
    c_NR_pCI TEXT,
    c_RAC TEXT,
    c_RSCP DECIMAL(5,1),
    c_RSRP DECIMAL(5,1),
    c_RxLevel DECIMAL(5,1),
    c_SID TEXT,
    c_SIM_AuthenticationEnd DATETIME(3),
    c_SIM_AuthenticationStart DATETIME(3),
    c_TADIG TEXT,
    c_UsedPLMN TEXT,
    c_UsedPLMNName TEXT,
    c_imsi TEXT,
    c_location TEXT,
    c_number TEXT,
    c_plmn TEXT,
    c_plmnShort TEXT,
    c_probe TEXT,
    d_ATR_Mobile TEXT,
    d_ATR_SimEmu TEXT,
    d_CI TEXT,
    d_DCNR_restricted TEXT,
    d_EcIo DECIMAL(5,1),
    d_LAC TEXT,
    d_LTE_Band TEXT,
    d_LTE_Bandwidth TEXT,
    d_MMEName TEXT,
    d_NID TEXT,
    d_NRI_cs TEXT,
    d_NRI_ps TEXT,
    d_NR_Band TEXT,
    d_NR_DL_Bandwidth TEXT,
    d_NR_RSRP DECIMAL(5,1),
    d_NR_RSRQ DECIMAL(5,1),
    d_NR_SINR DECIMAL(5,1),
    d_NR_pCI TEXT,
    d_RAC TEXT,
    d_RSCP DECIMAL(5,1),
    d_RSRP DECIMAL(5,1),
    d_RxLevel DECIMAL(5,1),
    d_SID TEXT,
    d_SIM_AuthenticationEnd DATETIME(3),
    d_SIM_AuthenticationStart DATETIME(3),
    d_TADIG TEXT,
    d_UsedPLMN TEXT,
    d_UsedPLMNName TEXT,
    d_imsi TEXT,
    d_location TEXT,
    d_number TEXT,
    d_plmn TEXT,
    d_plmnShort TEXT,
    d_probe TEXT,
    unitsMaxTimeOffset DECIMAL(5,1),
    Description TEXT,
    a_NetworkType TEXT,
    b_NetworkType TEXT,
    c_NetworkType TEXT,
    d_NetworkType TEXT,
    a_hlr TEXT,
    b_hlr TEXT,
    c_hlr TEXT,
    d_hlr TEXT,
    Valid TINYINT,
    a_mobileType TEXT,
    a_type TEXT,
    b_mobileType TEXT,
    b_type TEXT,
    c_mobileType TEXT,
    d_mobileType TEXT,
    a_UnitGPS TEXT,
    b_UnitGPS TEXT,
    c_UnitGPS TEXT,
    d_UnitGPS TEXT,
    a_SearchedPLMN TEXT,
    a_SearchedPLMNName TEXT,
    a_SearchedPLMNNameShort TEXT,
    b_SearchedPLMN TEXT,
    b_SearchedPLMNName TEXT,
    b_SearchedPLMNNameShort TEXT,
    c_SearchedPLMN TEXT,
    c_SearchedPLMNName TEXT,
    c_SearchedPLMNNameShort TEXT,
    d_SearchedPLMN TEXT,
    d_SearchedPLMNName TEXT,
    d_SearchedPLMNNameShort TEXT,
    GRP INT,
    GlobalResourceCount INT,
    a_GlobalUsedPLMN TEXT,
    b_GlobalUsedPLMN TEXT,
    c_GlobalUsedPLMN TEXT,
    d_GlobalUsedPLMN TEXT,
    RecordId BIGINT,
    InsertId BIGINT
);