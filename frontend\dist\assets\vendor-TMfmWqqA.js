function ac(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const l in r)if(l!=="default"&&!(l in e)){const i=Object.getOwnPropertyDescriptor(r,l);i&&Object.defineProperty(e,l,i.get?i:{enumerable:!0,get:()=>r[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}var ry=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function sc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ly(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var l=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,l.get?l:{enumerable:!0,get:function(){return e[r]}})}),n}var cc={exports:{}},K={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fl=Symbol.for("react.element"),np=Symbol.for("react.portal"),rp=Symbol.for("react.fragment"),lp=Symbol.for("react.strict_mode"),ip=Symbol.for("react.profiler"),op=Symbol.for("react.provider"),up=Symbol.for("react.context"),ap=Symbol.for("react.forward_ref"),sp=Symbol.for("react.suspense"),cp=Symbol.for("react.memo"),fp=Symbol.for("react.lazy"),Da=Symbol.iterator;function dp(e){return e===null||typeof e!="object"?null:(e=Da&&e[Da]||e["@@iterator"],typeof e=="function"?e:null)}var fc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},dc=Object.assign,pc={};function fr(e,t,n){this.props=e,this.context=t,this.refs=pc,this.updater=n||fc}fr.prototype.isReactComponent={};fr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};fr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hc(){}hc.prototype=fr.prototype;function Eu(e,t,n){this.props=e,this.context=t,this.refs=pc,this.updater=n||fc}var ku=Eu.prototype=new hc;ku.constructor=Eu;dc(ku,fr.prototype);ku.isPureReactComponent=!0;var Ma=Array.isArray,mc=Object.prototype.hasOwnProperty,xu={current:null},vc={key:!0,ref:!0,__self:!0,__source:!0};function yc(e,t,n){var r,l={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)mc.call(t,r)&&!vc.hasOwnProperty(r)&&(l[r]=t[r]);var u=arguments.length-2;if(u===1)l.children=n;else if(1<u){for(var a=Array(u),s=0;s<u;s++)a[s]=arguments[s+2];l.children=a}if(e&&e.defaultProps)for(r in u=e.defaultProps,u)l[r]===void 0&&(l[r]=u[r]);return{$$typeof:fl,type:e,key:i,ref:o,props:l,_owner:xu.current}}function pp(e,t){return{$$typeof:fl,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Cu(e){return typeof e=="object"&&e!==null&&e.$$typeof===fl}function hp(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var za=/\/+/g;function Ji(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hp(""+e.key):t.toString(36)}function Vl(e,t,n,r,l){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case fl:case np:o=!0}}if(o)return o=e,l=l(o),e=r===""?"."+Ji(o,0):r,Ma(l)?(n="",e!=null&&(n=e.replace(za,"$&/")+"/"),Vl(l,t,n,"",function(s){return s})):l!=null&&(Cu(l)&&(l=pp(l,n+(!l.key||o&&o.key===l.key?"":(""+l.key).replace(za,"$&/")+"/")+e)),t.push(l)),1;if(o=0,r=r===""?".":r+":",Ma(e))for(var u=0;u<e.length;u++){i=e[u];var a=r+Ji(i,u);o+=Vl(i,t,n,a,l)}else if(a=dp(e),typeof a=="function")for(e=a.call(e),u=0;!(i=e.next()).done;)i=i.value,a=r+Ji(i,u++),o+=Vl(i,t,n,a,l);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function Cl(e,t,n){if(e==null)return e;var r=[],l=0;return Vl(e,r,"","",function(i){return t.call(n,i,l++)}),r}function mp(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ue={current:null},Hl={transition:null},vp={ReactCurrentDispatcher:Ue,ReactCurrentBatchConfig:Hl,ReactCurrentOwner:xu};function gc(){throw Error("act(...) is not supported in production builds of React.")}K.Children={map:Cl,forEach:function(e,t,n){Cl(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Cl(e,function(){t++}),t},toArray:function(e){return Cl(e,function(t){return t})||[]},only:function(e){if(!Cu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};K.Component=fr;K.Fragment=rp;K.Profiler=ip;K.PureComponent=Eu;K.StrictMode=lp;K.Suspense=sp;K.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=vp;K.act=gc;K.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=dc({},e.props),l=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=xu.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(a in t)mc.call(t,a)&&!vc.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&u!==void 0?u[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){u=Array(a);for(var s=0;s<a;s++)u[s]=arguments[s+2];r.children=u}return{$$typeof:fl,type:e.type,key:l,ref:i,props:r,_owner:o}};K.createContext=function(e){return e={$$typeof:up,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:op,_context:e},e.Consumer=e};K.createElement=yc;K.createFactory=function(e){var t=yc.bind(null,e);return t.type=e,t};K.createRef=function(){return{current:null}};K.forwardRef=function(e){return{$$typeof:ap,render:e}};K.isValidElement=Cu;K.lazy=function(e){return{$$typeof:fp,_payload:{_status:-1,_result:e},_init:mp}};K.memo=function(e,t){return{$$typeof:cp,type:e,compare:t===void 0?null:t}};K.startTransition=function(e){var t=Hl.transition;Hl.transition={};try{e()}finally{Hl.transition=t}};K.unstable_act=gc;K.useCallback=function(e,t){return Ue.current.useCallback(e,t)};K.useContext=function(e){return Ue.current.useContext(e)};K.useDebugValue=function(){};K.useDeferredValue=function(e){return Ue.current.useDeferredValue(e)};K.useEffect=function(e,t){return Ue.current.useEffect(e,t)};K.useId=function(){return Ue.current.useId()};K.useImperativeHandle=function(e,t,n){return Ue.current.useImperativeHandle(e,t,n)};K.useInsertionEffect=function(e,t){return Ue.current.useInsertionEffect(e,t)};K.useLayoutEffect=function(e,t){return Ue.current.useLayoutEffect(e,t)};K.useMemo=function(e,t){return Ue.current.useMemo(e,t)};K.useReducer=function(e,t,n){return Ue.current.useReducer(e,t,n)};K.useRef=function(e){return Ue.current.useRef(e)};K.useState=function(e){return Ue.current.useState(e)};K.useSyncExternalStore=function(e,t,n){return Ue.current.useSyncExternalStore(e,t,n)};K.useTransition=function(){return Ue.current.useTransition()};K.version="18.3.1";cc.exports=K;var R=cc.exports;const yp=sc(R),gp=ac({__proto__:null,default:yp},[R]);var wc={exports:{}},qe={},Sc={exports:{}},Ec={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,$){var H=T.length;T.push($);e:for(;0<H;){var ee=H-1>>>1,te=T[ee];if(0<l(te,$))T[ee]=$,T[H]=te,H=ee;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var $=T[0],H=T.pop();if(H!==$){T[0]=H;e:for(var ee=0,te=T.length,at=te>>>1;ee<at;){var Ke=2*(ee+1)-1,ze=T[Ke],Oe=Ke+1,et=T[Oe];if(0>l(ze,H))Oe<te&&0>l(et,ze)?(T[ee]=et,T[Oe]=H,ee=Oe):(T[ee]=ze,T[Ke]=H,ee=Ke);else if(Oe<te&&0>l(et,H))T[ee]=et,T[Oe]=H,ee=Oe;else break e}}return $}function l(T,$){var H=T.sortIndex-$.sortIndex;return H!==0?H:T.id-$.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,u=o.now();e.unstable_now=function(){return o.now()-u}}var a=[],s=[],d=1,f=null,h=3,w=!1,k=!1,E=!1,D=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,c=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function v(T){for(var $=n(s);$!==null;){if($.callback===null)r(s);else if($.startTime<=T)r(s),$.sortIndex=$.expirationTime,t(a,$);else break;$=n(s)}}function x(T){if(E=!1,v(T),!k)if(n(a)!==null)k=!0,It(L);else{var $=n(s);$!==null&&jt(x,$.startTime-T)}}function L(T,$){k=!1,E&&(E=!1,m(P),P=-1),w=!0;var H=h;try{for(v($),f=n(a);f!==null&&(!(f.expirationTime>$)||T&&!b());){var ee=f.callback;if(typeof ee=="function"){f.callback=null,h=f.priorityLevel;var te=ee(f.expirationTime<=$);$=e.unstable_now(),typeof te=="function"?f.callback=te:f===n(a)&&r(a),v($)}else r(a);f=n(a)}if(f!==null)var at=!0;else{var Ke=n(s);Ke!==null&&jt(x,Ke.startTime-$),at=!1}return at}finally{f=null,h=H,w=!1}}var O=!1,y=null,P=-1,B=5,M=-1;function b(){return!(e.unstable_now()-M<B)}function ne(){if(y!==null){var T=e.unstable_now();M=T;var $=!0;try{$=y(!0,T)}finally{$?we():(O=!1,y=null)}}else O=!1}var we;if(typeof c=="function")we=function(){c(ne)};else if(typeof MessageChannel<"u"){var Ce=new MessageChannel,ut=Ce.port2;Ce.port1.onmessage=ne,we=function(){ut.postMessage(null)}}else we=function(){D(ne,0)};function It(T){y=T,O||(O=!0,we())}function jt(T,$){P=D(function(){T(e.unstable_now())},$)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){k||w||(k=!0,It(L))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):B=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(T){switch(h){case 1:case 2:case 3:var $=3;break;default:$=h}var H=h;h=$;try{return T()}finally{h=H}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,$){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var H=h;h=T;try{return $()}finally{h=H}},e.unstable_scheduleCallback=function(T,$,H){var ee=e.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?ee+H:ee):H=ee,T){case 1:var te=-1;break;case 2:te=250;break;case 5:te=**********;break;case 4:te=1e4;break;default:te=5e3}return te=H+te,T={id:d++,callback:$,priorityLevel:T,startTime:H,expirationTime:te,sortIndex:-1},H>ee?(T.sortIndex=H,t(s,T),n(a)===null&&T===n(s)&&(E?(m(P),P=-1):E=!0,jt(x,H-ee))):(T.sortIndex=te,t(a,T),k||w||(k=!0,It(L))),T},e.unstable_shouldYield=b,e.unstable_wrapCallback=function(T){var $=h;return function(){var H=h;h=$;try{return T.apply(this,arguments)}finally{h=H}}}})(Ec);Sc.exports=Ec;var wp=Sc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sp=R,Ze=wp;function _(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var kc=new Set,Qr={};function Dn(e,t){rr(e,t),rr(e+"Capture",t)}function rr(e,t){for(Qr[e]=t,e=0;e<t.length;e++)kc.add(t[e])}var Dt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),_o=Object.prototype.hasOwnProperty,Ep=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Oa={},Fa={};function kp(e){return _o.call(Fa,e)?!0:_o.call(Oa,e)?!1:Ep.test(e)?Fa[e]=!0:(Oa[e]=!0,!1)}function xp(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Cp(e,t,n,r){if(t===null||typeof t>"u"||xp(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ae(e,t,n,r,l,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=l,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var Le={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Le[e]=new Ae(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Le[t]=new Ae(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Le[e]=new Ae(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Le[e]=new Ae(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Le[e]=new Ae(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Le[e]=new Ae(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Le[e]=new Ae(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Le[e]=new Ae(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Le[e]=new Ae(e,5,!1,e.toLowerCase(),null,!1,!1)});var Pu=/[\-:]([a-z])/g;function _u(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Pu,_u);Le[t]=new Ae(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Pu,_u);Le[t]=new Ae(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Pu,_u);Le[t]=new Ae(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Le[e]=new Ae(e,1,!1,e.toLowerCase(),null,!1,!1)});Le.xlinkHref=new Ae("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Le[e]=new Ae(e,1,!1,e.toLowerCase(),null,!0,!0)});function Ru(e,t,n,r){var l=Le.hasOwnProperty(t)?Le[t]:null;(l!==null?l.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Cp(t,n,l,r)&&(n=null),r||l===null?kp(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):l.mustUseProperty?e[l.propertyName]=n===null?l.type===3?!1:"":n:(t=l.attributeName,r=l.attributeNamespace,n===null?e.removeAttribute(t):(l=l.type,n=l===3||l===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Ft=Sp.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Pl=Symbol.for("react.element"),An=Symbol.for("react.portal"),Bn=Symbol.for("react.fragment"),Lu=Symbol.for("react.strict_mode"),Ro=Symbol.for("react.profiler"),xc=Symbol.for("react.provider"),Cc=Symbol.for("react.context"),Tu=Symbol.for("react.forward_ref"),Lo=Symbol.for("react.suspense"),To=Symbol.for("react.suspense_list"),Nu=Symbol.for("react.memo"),Vt=Symbol.for("react.lazy"),Pc=Symbol.for("react.offscreen"),Ia=Symbol.iterator;function Sr(e){return e===null||typeof e!="object"?null:(e=Ia&&e[Ia]||e["@@iterator"],typeof e=="function"?e:null)}var fe=Object.assign,Zi;function Dr(e){if(Zi===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Zi=t&&t[1]||""}return`
`+Zi+e}var qi=!1;function bi(e,t){if(!e||qi)return"";qi=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var r=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&typeof s.stack=="string"){for(var l=s.stack.split(`
`),i=r.stack.split(`
`),o=l.length-1,u=i.length-1;1<=o&&0<=u&&l[o]!==i[u];)u--;for(;1<=o&&0<=u;o--,u--)if(l[o]!==i[u]){if(o!==1||u!==1)do if(o--,u--,0>u||l[o]!==i[u]){var a=`
`+l[o].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=o&&0<=u);break}}}finally{qi=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Dr(e):""}function Pp(e){switch(e.tag){case 5:return Dr(e.type);case 16:return Dr("Lazy");case 13:return Dr("Suspense");case 19:return Dr("SuspenseList");case 0:case 2:case 15:return e=bi(e.type,!1),e;case 11:return e=bi(e.type.render,!1),e;case 1:return e=bi(e.type,!0),e;default:return""}}function No(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Bn:return"Fragment";case An:return"Portal";case Ro:return"Profiler";case Lu:return"StrictMode";case Lo:return"Suspense";case To:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Cc:return(e.displayName||"Context")+".Consumer";case xc:return(e._context.displayName||"Context")+".Provider";case Tu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Nu:return t=e.displayName||null,t!==null?t:No(e.type)||"Memo";case Vt:t=e._payload,e=e._init;try{return No(e(t))}catch{}}return null}function _p(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return No(t);case 8:return t===Lu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function rn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function _c(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Rp(e){var t=_c(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var l=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function _l(e){e._valueTracker||(e._valueTracker=Rp(e))}function Rc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=_c(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ti(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Do(e,t){var n=t.checked;return fe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function ja(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=rn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Lc(e,t){t=t.checked,t!=null&&Ru(e,"checked",t,!1)}function Mo(e,t){Lc(e,t);var n=rn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?zo(e,t.type,n):t.hasOwnProperty("defaultValue")&&zo(e,t.type,rn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Ua(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function zo(e,t,n){(t!=="number"||ti(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Mr=Array.isArray;function Zn(e,t,n,r){if(e=e.options,t){t={};for(var l=0;l<n.length;l++)t["$"+n[l]]=!0;for(n=0;n<e.length;n++)l=t.hasOwnProperty("$"+e[n].value),e[n].selected!==l&&(e[n].selected=l),l&&r&&(e[n].defaultSelected=!0)}else{for(n=""+rn(n),t=null,l=0;l<e.length;l++){if(e[l].value===n){e[l].selected=!0,r&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Oo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(_(91));return fe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Aa(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(_(92));if(Mr(n)){if(1<n.length)throw Error(_(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:rn(n)}}function Tc(e,t){var n=rn(t.value),r=rn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Ba(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Nc(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Fo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Nc(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Rl,Dc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,l){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Rl=Rl||document.createElement("div"),Rl.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Rl.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Kr(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ir={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Lp=["Webkit","ms","Moz","O"];Object.keys(Ir).forEach(function(e){Lp.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ir[t]=Ir[e]})});function Mc(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ir.hasOwnProperty(e)&&Ir[e]?(""+t).trim():t+"px"}function zc(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,l=Mc(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,l):e[n]=l}}var Tp=fe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Io(e,t){if(t){if(Tp[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(_(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(_(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(_(61))}if(t.style!=null&&typeof t.style!="object")throw Error(_(62))}}function jo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Uo=null;function Du(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ao=null,qn=null,bn=null;function $a(e){if(e=hl(e)){if(typeof Ao!="function")throw Error(_(280));var t=e.stateNode;t&&(t=Mi(t),Ao(e.stateNode,e.type,t))}}function Oc(e){qn?bn?bn.push(e):bn=[e]:qn=e}function Fc(){if(qn){var e=qn,t=bn;if(bn=qn=null,$a(e),t)for(e=0;e<t.length;e++)$a(t[e])}}function Ic(e,t){return e(t)}function jc(){}var eo=!1;function Uc(e,t,n){if(eo)return e(t,n);eo=!0;try{return Ic(e,t,n)}finally{eo=!1,(qn!==null||bn!==null)&&(jc(),Fc())}}function Yr(e,t){var n=e.stateNode;if(n===null)return null;var r=Mi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(_(231,t,typeof n));return n}var Bo=!1;if(Dt)try{var Er={};Object.defineProperty(Er,"passive",{get:function(){Bo=!0}}),window.addEventListener("test",Er,Er),window.removeEventListener("test",Er,Er)}catch{Bo=!1}function Np(e,t,n,r,l,i,o,u,a){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(d){this.onError(d)}}var jr=!1,ni=null,ri=!1,$o=null,Dp={onError:function(e){jr=!0,ni=e}};function Mp(e,t,n,r,l,i,o,u,a){jr=!1,ni=null,Np.apply(Dp,arguments)}function zp(e,t,n,r,l,i,o,u,a){if(Mp.apply(this,arguments),jr){if(jr){var s=ni;jr=!1,ni=null}else throw Error(_(198));ri||(ri=!0,$o=s)}}function Mn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ac(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Va(e){if(Mn(e)!==e)throw Error(_(188))}function Op(e){var t=e.alternate;if(!t){if(t=Mn(e),t===null)throw Error(_(188));return t!==e?null:e}for(var n=e,r=t;;){var l=n.return;if(l===null)break;var i=l.alternate;if(i===null){if(r=l.return,r!==null){n=r;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return Va(l),e;if(i===r)return Va(l),t;i=i.sibling}throw Error(_(188))}if(n.return!==r.return)n=l,r=i;else{for(var o=!1,u=l.child;u;){if(u===n){o=!0,n=l,r=i;break}if(u===r){o=!0,r=l,n=i;break}u=u.sibling}if(!o){for(u=i.child;u;){if(u===n){o=!0,n=i,r=l;break}if(u===r){o=!0,r=i,n=l;break}u=u.sibling}if(!o)throw Error(_(189))}}if(n.alternate!==r)throw Error(_(190))}if(n.tag!==3)throw Error(_(188));return n.stateNode.current===n?e:t}function Bc(e){return e=Op(e),e!==null?$c(e):null}function $c(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$c(e);if(t!==null)return t;e=e.sibling}return null}var Vc=Ze.unstable_scheduleCallback,Ha=Ze.unstable_cancelCallback,Fp=Ze.unstable_shouldYield,Ip=Ze.unstable_requestPaint,me=Ze.unstable_now,jp=Ze.unstable_getCurrentPriorityLevel,Mu=Ze.unstable_ImmediatePriority,Hc=Ze.unstable_UserBlockingPriority,li=Ze.unstable_NormalPriority,Up=Ze.unstable_LowPriority,Wc=Ze.unstable_IdlePriority,Li=null,Et=null;function Ap(e){if(Et&&typeof Et.onCommitFiberRoot=="function")try{Et.onCommitFiberRoot(Li,e,void 0,(e.current.flags&128)===128)}catch{}}var ht=Math.clz32?Math.clz32:Vp,Bp=Math.log,$p=Math.LN2;function Vp(e){return e>>>=0,e===0?32:31-(Bp(e)/$p|0)|0}var Ll=64,Tl=4194304;function zr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ii(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,l=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var u=o&~l;u!==0?r=zr(u):(i&=o,i!==0&&(r=zr(i)))}else o=n&~l,o!==0?r=zr(o):i!==0&&(r=zr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&l)&&(l=r&-r,i=t&-t,l>=i||l===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-ht(t),l=1<<n,r|=e[n],t&=~l;return r}function Hp(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Wp(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-ht(i),u=1<<o,a=l[o];a===-1?(!(u&n)||u&r)&&(l[o]=Hp(u,t)):a<=t&&(e.expiredLanes|=u),i&=~u}}function Vo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Qc(){var e=Ll;return Ll<<=1,!(Ll&4194240)&&(Ll=64),e}function to(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function dl(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-ht(t),e[t]=n}function Qp(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var l=31-ht(n),i=1<<l;t[l]=0,r[l]=-1,e[l]=-1,n&=~i}}function zu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ht(n),l=1<<r;l&t|e[r]&t&&(e[r]|=t),n&=~l}}var q=0;function Kc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Yc,Ou,Xc,Gc,Jc,Ho=!1,Nl=[],Gt=null,Jt=null,Zt=null,Xr=new Map,Gr=new Map,Wt=[],Kp="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Wa(e,t){switch(e){case"focusin":case"focusout":Gt=null;break;case"dragenter":case"dragleave":Jt=null;break;case"mouseover":case"mouseout":Zt=null;break;case"pointerover":case"pointerout":Xr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Gr.delete(t.pointerId)}}function kr(e,t,n,r,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[l]},t!==null&&(t=hl(t),t!==null&&Ou(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Yp(e,t,n,r,l){switch(t){case"focusin":return Gt=kr(Gt,e,t,n,r,l),!0;case"dragenter":return Jt=kr(Jt,e,t,n,r,l),!0;case"mouseover":return Zt=kr(Zt,e,t,n,r,l),!0;case"pointerover":var i=l.pointerId;return Xr.set(i,kr(Xr.get(i)||null,e,t,n,r,l)),!0;case"gotpointercapture":return i=l.pointerId,Gr.set(i,kr(Gr.get(i)||null,e,t,n,r,l)),!0}return!1}function Zc(e){var t=wn(e.target);if(t!==null){var n=Mn(t);if(n!==null){if(t=n.tag,t===13){if(t=Ac(n),t!==null){e.blockedOn=t,Jc(e.priority,function(){Xc(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Wl(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Wo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Uo=r,n.target.dispatchEvent(r),Uo=null}else return t=hl(n),t!==null&&Ou(t),e.blockedOn=n,!1;t.shift()}return!0}function Qa(e,t,n){Wl(e)&&n.delete(t)}function Xp(){Ho=!1,Gt!==null&&Wl(Gt)&&(Gt=null),Jt!==null&&Wl(Jt)&&(Jt=null),Zt!==null&&Wl(Zt)&&(Zt=null),Xr.forEach(Qa),Gr.forEach(Qa)}function xr(e,t){e.blockedOn===t&&(e.blockedOn=null,Ho||(Ho=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,Xp)))}function Jr(e){function t(l){return xr(l,e)}if(0<Nl.length){xr(Nl[0],e);for(var n=1;n<Nl.length;n++){var r=Nl[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Gt!==null&&xr(Gt,e),Jt!==null&&xr(Jt,e),Zt!==null&&xr(Zt,e),Xr.forEach(t),Gr.forEach(t),n=0;n<Wt.length;n++)r=Wt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Wt.length&&(n=Wt[0],n.blockedOn===null);)Zc(n),n.blockedOn===null&&Wt.shift()}var er=Ft.ReactCurrentBatchConfig,oi=!0;function Gp(e,t,n,r){var l=q,i=er.transition;er.transition=null;try{q=1,Fu(e,t,n,r)}finally{q=l,er.transition=i}}function Jp(e,t,n,r){var l=q,i=er.transition;er.transition=null;try{q=4,Fu(e,t,n,r)}finally{q=l,er.transition=i}}function Fu(e,t,n,r){if(oi){var l=Wo(e,t,n,r);if(l===null)fo(e,t,r,ui,n),Wa(e,r);else if(Yp(l,e,t,n,r))r.stopPropagation();else if(Wa(e,r),t&4&&-1<Kp.indexOf(e)){for(;l!==null;){var i=hl(l);if(i!==null&&Yc(i),i=Wo(e,t,n,r),i===null&&fo(e,t,r,ui,n),i===l)break;l=i}l!==null&&r.stopPropagation()}else fo(e,t,r,null,n)}}var ui=null;function Wo(e,t,n,r){if(ui=null,e=Du(r),e=wn(e),e!==null)if(t=Mn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ac(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ui=e,null}function qc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(jp()){case Mu:return 1;case Hc:return 4;case li:case Up:return 16;case Wc:return 536870912;default:return 16}default:return 16}}var Kt=null,Iu=null,Ql=null;function bc(){if(Ql)return Ql;var e,t=Iu,n=t.length,r,l="value"in Kt?Kt.value:Kt.textContent,i=l.length;for(e=0;e<n&&t[e]===l[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===l[i-r];r++);return Ql=l.slice(e,1<r?1-r:void 0)}function Kl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Dl(){return!0}function Ka(){return!1}function be(e){function t(n,r,l,i,o){this._reactName=n,this._targetInst=l,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var u in e)e.hasOwnProperty(u)&&(n=e[u],this[u]=n?n(i):i[u]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Dl:Ka,this.isPropagationStopped=Ka,this}return fe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Dl)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Dl)},persist:function(){},isPersistent:Dl}),t}var dr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},ju=be(dr),pl=fe({},dr,{view:0,detail:0}),Zp=be(pl),no,ro,Cr,Ti=fe({},pl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Uu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Cr&&(Cr&&e.type==="mousemove"?(no=e.screenX-Cr.screenX,ro=e.screenY-Cr.screenY):ro=no=0,Cr=e),no)},movementY:function(e){return"movementY"in e?e.movementY:ro}}),Ya=be(Ti),qp=fe({},Ti,{dataTransfer:0}),bp=be(qp),eh=fe({},pl,{relatedTarget:0}),lo=be(eh),th=fe({},dr,{animationName:0,elapsedTime:0,pseudoElement:0}),nh=be(th),rh=fe({},dr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),lh=be(rh),ih=fe({},dr,{data:0}),Xa=be(ih),oh={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},uh={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},ah={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function sh(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=ah[e])?!!t[e]:!1}function Uu(){return sh}var ch=fe({},pl,{key:function(e){if(e.key){var t=oh[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Kl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?uh[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Uu,charCode:function(e){return e.type==="keypress"?Kl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Kl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),fh=be(ch),dh=fe({},Ti,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ga=be(dh),ph=fe({},pl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Uu}),hh=be(ph),mh=fe({},dr,{propertyName:0,elapsedTime:0,pseudoElement:0}),vh=be(mh),yh=fe({},Ti,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),gh=be(yh),wh=[9,13,27,32],Au=Dt&&"CompositionEvent"in window,Ur=null;Dt&&"documentMode"in document&&(Ur=document.documentMode);var Sh=Dt&&"TextEvent"in window&&!Ur,ef=Dt&&(!Au||Ur&&8<Ur&&11>=Ur),Ja=" ",Za=!1;function tf(e,t){switch(e){case"keyup":return wh.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function nf(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var $n=!1;function Eh(e,t){switch(e){case"compositionend":return nf(t);case"keypress":return t.which!==32?null:(Za=!0,Ja);case"textInput":return e=t.data,e===Ja&&Za?null:e;default:return null}}function kh(e,t){if($n)return e==="compositionend"||!Au&&tf(e,t)?(e=bc(),Ql=Iu=Kt=null,$n=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ef&&t.locale!=="ko"?null:t.data;default:return null}}var xh={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qa(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!xh[e.type]:t==="textarea"}function rf(e,t,n,r){Oc(r),t=ai(t,"onChange"),0<t.length&&(n=new ju("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Ar=null,Zr=null;function Ch(e){mf(e,0)}function Ni(e){var t=Wn(e);if(Rc(t))return e}function Ph(e,t){if(e==="change")return t}var lf=!1;if(Dt){var io;if(Dt){var oo="oninput"in document;if(!oo){var ba=document.createElement("div");ba.setAttribute("oninput","return;"),oo=typeof ba.oninput=="function"}io=oo}else io=!1;lf=io&&(!document.documentMode||9<document.documentMode)}function es(){Ar&&(Ar.detachEvent("onpropertychange",of),Zr=Ar=null)}function of(e){if(e.propertyName==="value"&&Ni(Zr)){var t=[];rf(t,Zr,e,Du(e)),Uc(Ch,t)}}function _h(e,t,n){e==="focusin"?(es(),Ar=t,Zr=n,Ar.attachEvent("onpropertychange",of)):e==="focusout"&&es()}function Rh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ni(Zr)}function Lh(e,t){if(e==="click")return Ni(t)}function Th(e,t){if(e==="input"||e==="change")return Ni(t)}function Nh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var vt=typeof Object.is=="function"?Object.is:Nh;function qr(e,t){if(vt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var l=n[r];if(!_o.call(t,l)||!vt(e[l],t[l]))return!1}return!0}function ts(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ns(e,t){var n=ts(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ts(n)}}function uf(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?uf(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function af(){for(var e=window,t=ti();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ti(e.document)}return t}function Bu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Dh(e){var t=af(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&uf(n.ownerDocument.documentElement,n)){if(r!==null&&Bu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=n.textContent.length,i=Math.min(r.start,l);r=r.end===void 0?i:Math.min(r.end,l),!e.extend&&i>r&&(l=r,r=i,i=l),l=ns(n,i);var o=ns(n,r);l&&o&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Mh=Dt&&"documentMode"in document&&11>=document.documentMode,Vn=null,Qo=null,Br=null,Ko=!1;function rs(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ko||Vn==null||Vn!==ti(r)||(r=Vn,"selectionStart"in r&&Bu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Br&&qr(Br,r)||(Br=r,r=ai(Qo,"onSelect"),0<r.length&&(t=new ju("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Vn)))}function Ml(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Hn={animationend:Ml("Animation","AnimationEnd"),animationiteration:Ml("Animation","AnimationIteration"),animationstart:Ml("Animation","AnimationStart"),transitionend:Ml("Transition","TransitionEnd")},uo={},sf={};Dt&&(sf=document.createElement("div").style,"AnimationEvent"in window||(delete Hn.animationend.animation,delete Hn.animationiteration.animation,delete Hn.animationstart.animation),"TransitionEvent"in window||delete Hn.transitionend.transition);function Di(e){if(uo[e])return uo[e];if(!Hn[e])return e;var t=Hn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sf)return uo[e]=t[n];return e}var cf=Di("animationend"),ff=Di("animationiteration"),df=Di("animationstart"),pf=Di("transitionend"),hf=new Map,ls="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function on(e,t){hf.set(e,t),Dn(t,[e])}for(var ao=0;ao<ls.length;ao++){var so=ls[ao],zh=so.toLowerCase(),Oh=so[0].toUpperCase()+so.slice(1);on(zh,"on"+Oh)}on(cf,"onAnimationEnd");on(ff,"onAnimationIteration");on(df,"onAnimationStart");on("dblclick","onDoubleClick");on("focusin","onFocus");on("focusout","onBlur");on(pf,"onTransitionEnd");rr("onMouseEnter",["mouseout","mouseover"]);rr("onMouseLeave",["mouseout","mouseover"]);rr("onPointerEnter",["pointerout","pointerover"]);rr("onPointerLeave",["pointerout","pointerover"]);Dn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Dn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Dn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Dn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Dn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Or="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fh=new Set("cancel close invalid load scroll toggle".split(" ").concat(Or));function is(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,zp(r,t,void 0,e),e.currentTarget=null}function mf(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],l=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var u=r[o],a=u.instance,s=u.currentTarget;if(u=u.listener,a!==i&&l.isPropagationStopped())break e;is(l,u,s),i=a}else for(o=0;o<r.length;o++){if(u=r[o],a=u.instance,s=u.currentTarget,u=u.listener,a!==i&&l.isPropagationStopped())break e;is(l,u,s),i=a}}}if(ri)throw e=$o,ri=!1,$o=null,e}function ie(e,t){var n=t[Zo];n===void 0&&(n=t[Zo]=new Set);var r=e+"__bubble";n.has(r)||(vf(t,e,2,!1),n.add(r))}function co(e,t,n){var r=0;t&&(r|=4),vf(n,e,r,t)}var zl="_reactListening"+Math.random().toString(36).slice(2);function br(e){if(!e[zl]){e[zl]=!0,kc.forEach(function(n){n!=="selectionchange"&&(Fh.has(n)||co(n,!1,e),co(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zl]||(t[zl]=!0,co("selectionchange",!1,t))}}function vf(e,t,n,r){switch(qc(t)){case 1:var l=Gp;break;case 4:l=Jp;break;default:l=Fu}n=l.bind(null,t,n,e),l=void 0,!Bo||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),r?l!==void 0?e.addEventListener(t,n,{capture:!0,passive:l}):e.addEventListener(t,n,!0):l!==void 0?e.addEventListener(t,n,{passive:l}):e.addEventListener(t,n,!1)}function fo(e,t,n,r,l){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var u=r.stateNode.containerInfo;if(u===l||u.nodeType===8&&u.parentNode===l)break;if(o===4)for(o=r.return;o!==null;){var a=o.tag;if((a===3||a===4)&&(a=o.stateNode.containerInfo,a===l||a.nodeType===8&&a.parentNode===l))return;o=o.return}for(;u!==null;){if(o=wn(u),o===null)return;if(a=o.tag,a===5||a===6){r=i=o;continue e}u=u.parentNode}}r=r.return}Uc(function(){var s=i,d=Du(n),f=[];e:{var h=hf.get(e);if(h!==void 0){var w=ju,k=e;switch(e){case"keypress":if(Kl(n)===0)break e;case"keydown":case"keyup":w=fh;break;case"focusin":k="focus",w=lo;break;case"focusout":k="blur",w=lo;break;case"beforeblur":case"afterblur":w=lo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":w=Ya;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":w=bp;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":w=hh;break;case cf:case ff:case df:w=nh;break;case pf:w=vh;break;case"scroll":w=Zp;break;case"wheel":w=gh;break;case"copy":case"cut":case"paste":w=lh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":w=Ga}var E=(t&4)!==0,D=!E&&e==="scroll",m=E?h!==null?h+"Capture":null:h;E=[];for(var c=s,v;c!==null;){v=c;var x=v.stateNode;if(v.tag===5&&x!==null&&(v=x,m!==null&&(x=Yr(c,m),x!=null&&E.push(el(c,x,v)))),D)break;c=c.return}0<E.length&&(h=new w(h,k,null,n,d),f.push({event:h,listeners:E}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",w=e==="mouseout"||e==="pointerout",h&&n!==Uo&&(k=n.relatedTarget||n.fromElement)&&(wn(k)||k[Mt]))break e;if((w||h)&&(h=d.window===d?d:(h=d.ownerDocument)?h.defaultView||h.parentWindow:window,w?(k=n.relatedTarget||n.toElement,w=s,k=k?wn(k):null,k!==null&&(D=Mn(k),k!==D||k.tag!==5&&k.tag!==6)&&(k=null)):(w=null,k=s),w!==k)){if(E=Ya,x="onMouseLeave",m="onMouseEnter",c="mouse",(e==="pointerout"||e==="pointerover")&&(E=Ga,x="onPointerLeave",m="onPointerEnter",c="pointer"),D=w==null?h:Wn(w),v=k==null?h:Wn(k),h=new E(x,c+"leave",w,n,d),h.target=D,h.relatedTarget=v,x=null,wn(d)===s&&(E=new E(m,c+"enter",k,n,d),E.target=v,E.relatedTarget=D,x=E),D=x,w&&k)t:{for(E=w,m=k,c=0,v=E;v;v=jn(v))c++;for(v=0,x=m;x;x=jn(x))v++;for(;0<c-v;)E=jn(E),c--;for(;0<v-c;)m=jn(m),v--;for(;c--;){if(E===m||m!==null&&E===m.alternate)break t;E=jn(E),m=jn(m)}E=null}else E=null;w!==null&&os(f,h,w,E,!1),k!==null&&D!==null&&os(f,D,k,E,!0)}}e:{if(h=s?Wn(s):window,w=h.nodeName&&h.nodeName.toLowerCase(),w==="select"||w==="input"&&h.type==="file")var L=Ph;else if(qa(h))if(lf)L=Th;else{L=Rh;var O=_h}else(w=h.nodeName)&&w.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(L=Lh);if(L&&(L=L(e,s))){rf(f,L,n,d);break e}O&&O(e,h,s),e==="focusout"&&(O=h._wrapperState)&&O.controlled&&h.type==="number"&&zo(h,"number",h.value)}switch(O=s?Wn(s):window,e){case"focusin":(qa(O)||O.contentEditable==="true")&&(Vn=O,Qo=s,Br=null);break;case"focusout":Br=Qo=Vn=null;break;case"mousedown":Ko=!0;break;case"contextmenu":case"mouseup":case"dragend":Ko=!1,rs(f,n,d);break;case"selectionchange":if(Mh)break;case"keydown":case"keyup":rs(f,n,d)}var y;if(Au)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else $n?tf(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(ef&&n.locale!=="ko"&&($n||P!=="onCompositionStart"?P==="onCompositionEnd"&&$n&&(y=bc()):(Kt=d,Iu="value"in Kt?Kt.value:Kt.textContent,$n=!0)),O=ai(s,P),0<O.length&&(P=new Xa(P,e,null,n,d),f.push({event:P,listeners:O}),y?P.data=y:(y=nf(n),y!==null&&(P.data=y)))),(y=Sh?Eh(e,n):kh(e,n))&&(s=ai(s,"onBeforeInput"),0<s.length&&(d=new Xa("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:s}),d.data=y))}mf(f,t)})}function el(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ai(e,t){for(var n=t+"Capture",r=[];e!==null;){var l=e,i=l.stateNode;l.tag===5&&i!==null&&(l=i,i=Yr(e,n),i!=null&&r.unshift(el(e,i,l)),i=Yr(e,t),i!=null&&r.push(el(e,i,l))),e=e.return}return r}function jn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function os(e,t,n,r,l){for(var i=t._reactName,o=[];n!==null&&n!==r;){var u=n,a=u.alternate,s=u.stateNode;if(a!==null&&a===r)break;u.tag===5&&s!==null&&(u=s,l?(a=Yr(n,i),a!=null&&o.unshift(el(n,a,u))):l||(a=Yr(n,i),a!=null&&o.push(el(n,a,u)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Ih=/\r\n?/g,jh=/\u0000|\uFFFD/g;function us(e){return(typeof e=="string"?e:""+e).replace(Ih,`
`).replace(jh,"")}function Ol(e,t,n){if(t=us(t),us(e)!==t&&n)throw Error(_(425))}function si(){}var Yo=null,Xo=null;function Go(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Jo=typeof setTimeout=="function"?setTimeout:void 0,Uh=typeof clearTimeout=="function"?clearTimeout:void 0,as=typeof Promise=="function"?Promise:void 0,Ah=typeof queueMicrotask=="function"?queueMicrotask:typeof as<"u"?function(e){return as.resolve(null).then(e).catch(Bh)}:Jo;function Bh(e){setTimeout(function(){throw e})}function po(e,t){var n=t,r=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&l.nodeType===8)if(n=l.data,n==="/$"){if(r===0){e.removeChild(l),Jr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=l}while(n);Jr(t)}function qt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ss(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var pr=Math.random().toString(36).slice(2),St="__reactFiber$"+pr,tl="__reactProps$"+pr,Mt="__reactContainer$"+pr,Zo="__reactEvents$"+pr,$h="__reactListeners$"+pr,Vh="__reactHandles$"+pr;function wn(e){var t=e[St];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[St]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=ss(e);e!==null;){if(n=e[St])return n;e=ss(e)}return t}e=n,n=e.parentNode}return null}function hl(e){return e=e[St]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Wn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(_(33))}function Mi(e){return e[tl]||null}var qo=[],Qn=-1;function un(e){return{current:e}}function oe(e){0>Qn||(e.current=qo[Qn],qo[Qn]=null,Qn--)}function le(e,t){Qn++,qo[Qn]=e.current,e.current=t}var ln={},Me=un(ln),He=un(!1),Pn=ln;function lr(e,t){var n=e.type.contextTypes;if(!n)return ln;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var l={},i;for(i in n)l[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function We(e){return e=e.childContextTypes,e!=null}function ci(){oe(He),oe(Me)}function cs(e,t,n){if(Me.current!==ln)throw Error(_(168));le(Me,t),le(He,n)}function yf(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var l in r)if(!(l in t))throw Error(_(108,_p(e)||"Unknown",l));return fe({},n,r)}function fi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ln,Pn=Me.current,le(Me,e),le(He,He.current),!0}function fs(e,t,n){var r=e.stateNode;if(!r)throw Error(_(169));n?(e=yf(e,t,Pn),r.__reactInternalMemoizedMergedChildContext=e,oe(He),oe(Me),le(Me,e)):oe(He),le(He,n)}var _t=null,zi=!1,ho=!1;function gf(e){_t===null?_t=[e]:_t.push(e)}function Hh(e){zi=!0,gf(e)}function an(){if(!ho&&_t!==null){ho=!0;var e=0,t=q;try{var n=_t;for(q=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}_t=null,zi=!1}catch(l){throw _t!==null&&(_t=_t.slice(e+1)),Vc(Mu,an),l}finally{q=t,ho=!1}}return null}var Kn=[],Yn=0,di=null,pi=0,tt=[],nt=0,_n=null,Rt=1,Lt="";function mn(e,t){Kn[Yn++]=pi,Kn[Yn++]=di,di=e,pi=t}function wf(e,t,n){tt[nt++]=Rt,tt[nt++]=Lt,tt[nt++]=_n,_n=e;var r=Rt;e=Lt;var l=32-ht(r)-1;r&=~(1<<l),n+=1;var i=32-ht(t)+l;if(30<i){var o=l-l%5;i=(r&(1<<o)-1).toString(32),r>>=o,l-=o,Rt=1<<32-ht(t)+l|n<<l|r,Lt=i+e}else Rt=1<<i|n<<l|r,Lt=e}function $u(e){e.return!==null&&(mn(e,1),wf(e,1,0))}function Vu(e){for(;e===di;)di=Kn[--Yn],Kn[Yn]=null,pi=Kn[--Yn],Kn[Yn]=null;for(;e===_n;)_n=tt[--nt],tt[nt]=null,Lt=tt[--nt],tt[nt]=null,Rt=tt[--nt],tt[nt]=null}var Je=null,Ge=null,ae=!1,pt=null;function Sf(e,t){var n=rt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ds(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Je=e,Ge=qt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Je=e,Ge=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=_n!==null?{id:Rt,overflow:Lt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=rt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Je=e,Ge=null,!0):!1;default:return!1}}function bo(e){return(e.mode&1)!==0&&(e.flags&128)===0}function eu(e){if(ae){var t=Ge;if(t){var n=t;if(!ds(e,t)){if(bo(e))throw Error(_(418));t=qt(n.nextSibling);var r=Je;t&&ds(e,t)?Sf(r,n):(e.flags=e.flags&-4097|2,ae=!1,Je=e)}}else{if(bo(e))throw Error(_(418));e.flags=e.flags&-4097|2,ae=!1,Je=e}}}function ps(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Je=e}function Fl(e){if(e!==Je)return!1;if(!ae)return ps(e),ae=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Go(e.type,e.memoizedProps)),t&&(t=Ge)){if(bo(e))throw Ef(),Error(_(418));for(;t;)Sf(e,t),t=qt(t.nextSibling)}if(ps(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ge=qt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ge=null}}else Ge=Je?qt(e.stateNode.nextSibling):null;return!0}function Ef(){for(var e=Ge;e;)e=qt(e.nextSibling)}function ir(){Ge=Je=null,ae=!1}function Hu(e){pt===null?pt=[e]:pt.push(e)}var Wh=Ft.ReactCurrentBatchConfig;function Pr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(_(309));var r=n.stateNode}if(!r)throw Error(_(147,e));var l=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var u=l.refs;o===null?delete u[i]:u[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(_(284));if(!n._owner)throw Error(_(290,e))}return e}function Il(e,t){throw e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function hs(e){var t=e._init;return t(e._payload)}function kf(e){function t(m,c){if(e){var v=m.deletions;v===null?(m.deletions=[c],m.flags|=16):v.push(c)}}function n(m,c){if(!e)return null;for(;c!==null;)t(m,c),c=c.sibling;return null}function r(m,c){for(m=new Map;c!==null;)c.key!==null?m.set(c.key,c):m.set(c.index,c),c=c.sibling;return m}function l(m,c){return m=nn(m,c),m.index=0,m.sibling=null,m}function i(m,c,v){return m.index=v,e?(v=m.alternate,v!==null?(v=v.index,v<c?(m.flags|=2,c):v):(m.flags|=2,c)):(m.flags|=1048576,c)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function u(m,c,v,x){return c===null||c.tag!==6?(c=Eo(v,m.mode,x),c.return=m,c):(c=l(c,v),c.return=m,c)}function a(m,c,v,x){var L=v.type;return L===Bn?d(m,c,v.props.children,x,v.key):c!==null&&(c.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Vt&&hs(L)===c.type)?(x=l(c,v.props),x.ref=Pr(m,c,v),x.return=m,x):(x=bl(v.type,v.key,v.props,null,m.mode,x),x.ref=Pr(m,c,v),x.return=m,x)}function s(m,c,v,x){return c===null||c.tag!==4||c.stateNode.containerInfo!==v.containerInfo||c.stateNode.implementation!==v.implementation?(c=ko(v,m.mode,x),c.return=m,c):(c=l(c,v.children||[]),c.return=m,c)}function d(m,c,v,x,L){return c===null||c.tag!==7?(c=Cn(v,m.mode,x,L),c.return=m,c):(c=l(c,v),c.return=m,c)}function f(m,c,v){if(typeof c=="string"&&c!==""||typeof c=="number")return c=Eo(""+c,m.mode,v),c.return=m,c;if(typeof c=="object"&&c!==null){switch(c.$$typeof){case Pl:return v=bl(c.type,c.key,c.props,null,m.mode,v),v.ref=Pr(m,null,c),v.return=m,v;case An:return c=ko(c,m.mode,v),c.return=m,c;case Vt:var x=c._init;return f(m,x(c._payload),v)}if(Mr(c)||Sr(c))return c=Cn(c,m.mode,v,null),c.return=m,c;Il(m,c)}return null}function h(m,c,v,x){var L=c!==null?c.key:null;if(typeof v=="string"&&v!==""||typeof v=="number")return L!==null?null:u(m,c,""+v,x);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Pl:return v.key===L?a(m,c,v,x):null;case An:return v.key===L?s(m,c,v,x):null;case Vt:return L=v._init,h(m,c,L(v._payload),x)}if(Mr(v)||Sr(v))return L!==null?null:d(m,c,v,x,null);Il(m,v)}return null}function w(m,c,v,x,L){if(typeof x=="string"&&x!==""||typeof x=="number")return m=m.get(v)||null,u(c,m,""+x,L);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Pl:return m=m.get(x.key===null?v:x.key)||null,a(c,m,x,L);case An:return m=m.get(x.key===null?v:x.key)||null,s(c,m,x,L);case Vt:var O=x._init;return w(m,c,v,O(x._payload),L)}if(Mr(x)||Sr(x))return m=m.get(v)||null,d(c,m,x,L,null);Il(c,x)}return null}function k(m,c,v,x){for(var L=null,O=null,y=c,P=c=0,B=null;y!==null&&P<v.length;P++){y.index>P?(B=y,y=null):B=y.sibling;var M=h(m,y,v[P],x);if(M===null){y===null&&(y=B);break}e&&y&&M.alternate===null&&t(m,y),c=i(M,c,P),O===null?L=M:O.sibling=M,O=M,y=B}if(P===v.length)return n(m,y),ae&&mn(m,P),L;if(y===null){for(;P<v.length;P++)y=f(m,v[P],x),y!==null&&(c=i(y,c,P),O===null?L=y:O.sibling=y,O=y);return ae&&mn(m,P),L}for(y=r(m,y);P<v.length;P++)B=w(y,m,P,v[P],x),B!==null&&(e&&B.alternate!==null&&y.delete(B.key===null?P:B.key),c=i(B,c,P),O===null?L=B:O.sibling=B,O=B);return e&&y.forEach(function(b){return t(m,b)}),ae&&mn(m,P),L}function E(m,c,v,x){var L=Sr(v);if(typeof L!="function")throw Error(_(150));if(v=L.call(v),v==null)throw Error(_(151));for(var O=L=null,y=c,P=c=0,B=null,M=v.next();y!==null&&!M.done;P++,M=v.next()){y.index>P?(B=y,y=null):B=y.sibling;var b=h(m,y,M.value,x);if(b===null){y===null&&(y=B);break}e&&y&&b.alternate===null&&t(m,y),c=i(b,c,P),O===null?L=b:O.sibling=b,O=b,y=B}if(M.done)return n(m,y),ae&&mn(m,P),L;if(y===null){for(;!M.done;P++,M=v.next())M=f(m,M.value,x),M!==null&&(c=i(M,c,P),O===null?L=M:O.sibling=M,O=M);return ae&&mn(m,P),L}for(y=r(m,y);!M.done;P++,M=v.next())M=w(y,m,P,M.value,x),M!==null&&(e&&M.alternate!==null&&y.delete(M.key===null?P:M.key),c=i(M,c,P),O===null?L=M:O.sibling=M,O=M);return e&&y.forEach(function(ne){return t(m,ne)}),ae&&mn(m,P),L}function D(m,c,v,x){if(typeof v=="object"&&v!==null&&v.type===Bn&&v.key===null&&(v=v.props.children),typeof v=="object"&&v!==null){switch(v.$$typeof){case Pl:e:{for(var L=v.key,O=c;O!==null;){if(O.key===L){if(L=v.type,L===Bn){if(O.tag===7){n(m,O.sibling),c=l(O,v.props.children),c.return=m,m=c;break e}}else if(O.elementType===L||typeof L=="object"&&L!==null&&L.$$typeof===Vt&&hs(L)===O.type){n(m,O.sibling),c=l(O,v.props),c.ref=Pr(m,O,v),c.return=m,m=c;break e}n(m,O);break}else t(m,O);O=O.sibling}v.type===Bn?(c=Cn(v.props.children,m.mode,x,v.key),c.return=m,m=c):(x=bl(v.type,v.key,v.props,null,m.mode,x),x.ref=Pr(m,c,v),x.return=m,m=x)}return o(m);case An:e:{for(O=v.key;c!==null;){if(c.key===O)if(c.tag===4&&c.stateNode.containerInfo===v.containerInfo&&c.stateNode.implementation===v.implementation){n(m,c.sibling),c=l(c,v.children||[]),c.return=m,m=c;break e}else{n(m,c);break}else t(m,c);c=c.sibling}c=ko(v,m.mode,x),c.return=m,m=c}return o(m);case Vt:return O=v._init,D(m,c,O(v._payload),x)}if(Mr(v))return k(m,c,v,x);if(Sr(v))return E(m,c,v,x);Il(m,v)}return typeof v=="string"&&v!==""||typeof v=="number"?(v=""+v,c!==null&&c.tag===6?(n(m,c.sibling),c=l(c,v),c.return=m,m=c):(n(m,c),c=Eo(v,m.mode,x),c.return=m,m=c),o(m)):n(m,c)}return D}var or=kf(!0),xf=kf(!1),hi=un(null),mi=null,Xn=null,Wu=null;function Qu(){Wu=Xn=mi=null}function Ku(e){var t=hi.current;oe(hi),e._currentValue=t}function tu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function tr(e,t){mi=e,Wu=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ve=!0),e.firstContext=null)}function it(e){var t=e._currentValue;if(Wu!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(mi===null)throw Error(_(308));Xn=e,mi.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var Sn=null;function Yu(e){Sn===null?Sn=[e]:Sn.push(e)}function Cf(e,t,n,r){var l=t.interleaved;return l===null?(n.next=n,Yu(t)):(n.next=l.next,l.next=n),t.interleaved=n,zt(e,r)}function zt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Ht=!1;function Xu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Pf(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var l=r.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),r.pending=t,zt(e,n)}return l=r.interleaved,l===null?(t.next=t,Yu(r)):(t.next=l.next,l.next=t),r.interleaved=t,zt(e,n)}function Yl(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zu(e,n)}}function ms(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var l=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?l=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?l=i=t:i=i.next=t}else l=i=t;n={baseState:r.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function vi(e,t,n,r){var l=e.updateQueue;Ht=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,u=l.shared.pending;if(u!==null){l.shared.pending=null;var a=u,s=a.next;a.next=null,o===null?i=s:o.next=s,o=a;var d=e.alternate;d!==null&&(d=d.updateQueue,u=d.lastBaseUpdate,u!==o&&(u===null?d.firstBaseUpdate=s:u.next=s,d.lastBaseUpdate=a))}if(i!==null){var f=l.baseState;o=0,d=s=a=null,u=i;do{var h=u.lane,w=u.eventTime;if((r&h)===h){d!==null&&(d=d.next={eventTime:w,lane:0,tag:u.tag,payload:u.payload,callback:u.callback,next:null});e:{var k=e,E=u;switch(h=t,w=n,E.tag){case 1:if(k=E.payload,typeof k=="function"){f=k.call(w,f,h);break e}f=k;break e;case 3:k.flags=k.flags&-65537|128;case 0:if(k=E.payload,h=typeof k=="function"?k.call(w,f,h):k,h==null)break e;f=fe({},f,h);break e;case 2:Ht=!0}}u.callback!==null&&u.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[u]:h.push(u))}else w={eventTime:w,lane:h,tag:u.tag,payload:u.payload,callback:u.callback,next:null},d===null?(s=d=w,a=f):d=d.next=w,o|=h;if(u=u.next,u===null){if(u=l.shared.pending,u===null)break;h=u,u=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(!0);if(d===null&&(a=f),l.baseState=a,l.firstBaseUpdate=s,l.lastBaseUpdate=d,t=l.shared.interleaved,t!==null){l=t;do o|=l.lane,l=l.next;while(l!==t)}else i===null&&(l.shared.lanes=0);Ln|=o,e.lanes=o,e.memoizedState=f}}function vs(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],l=r.callback;if(l!==null){if(r.callback=null,r=n,typeof l!="function")throw Error(_(191,l));l.call(r)}}}var ml={},kt=un(ml),nl=un(ml),rl=un(ml);function En(e){if(e===ml)throw Error(_(174));return e}function Gu(e,t){switch(le(rl,t),le(nl,e),le(kt,ml),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Fo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Fo(t,e)}oe(kt),le(kt,t)}function ur(){oe(kt),oe(nl),oe(rl)}function _f(e){En(rl.current);var t=En(kt.current),n=Fo(t,e.type);t!==n&&(le(nl,e),le(kt,n))}function Ju(e){nl.current===e&&(oe(kt),oe(nl))}var se=un(0);function yi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var mo=[];function Zu(){for(var e=0;e<mo.length;e++)mo[e]._workInProgressVersionPrimary=null;mo.length=0}var Xl=Ft.ReactCurrentDispatcher,vo=Ft.ReactCurrentBatchConfig,Rn=0,ce=null,Se=null,ke=null,gi=!1,$r=!1,ll=0,Qh=0;function Te(){throw Error(_(321))}function qu(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!vt(e[n],t[n]))return!1;return!0}function bu(e,t,n,r,l,i){if(Rn=i,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Xl.current=e===null||e.memoizedState===null?Gh:Jh,e=n(r,l),$r){i=0;do{if($r=!1,ll=0,25<=i)throw Error(_(301));i+=1,ke=Se=null,t.updateQueue=null,Xl.current=Zh,e=n(r,l)}while($r)}if(Xl.current=wi,t=Se!==null&&Se.next!==null,Rn=0,ke=Se=ce=null,gi=!1,t)throw Error(_(300));return e}function ea(){var e=ll!==0;return ll=0,e}function wt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?ce.memoizedState=ke=e:ke=ke.next=e,ke}function ot(){if(Se===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Se.next;var t=ke===null?ce.memoizedState:ke.next;if(t!==null)ke=t,Se=e;else{if(e===null)throw Error(_(310));Se=e,e={memoizedState:Se.memoizedState,baseState:Se.baseState,baseQueue:Se.baseQueue,queue:Se.queue,next:null},ke===null?ce.memoizedState=ke=e:ke=ke.next=e}return ke}function il(e,t){return typeof t=="function"?t(e):t}function yo(e){var t=ot(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=Se,l=r.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}r.baseQueue=l=i,n.pending=null}if(l!==null){i=l.next,r=r.baseState;var u=o=null,a=null,s=i;do{var d=s.lane;if((Rn&d)===d)a!==null&&(a=a.next={lane:0,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null}),r=s.hasEagerState?s.eagerState:e(r,s.action);else{var f={lane:d,action:s.action,hasEagerState:s.hasEagerState,eagerState:s.eagerState,next:null};a===null?(u=a=f,o=r):a=a.next=f,ce.lanes|=d,Ln|=d}s=s.next}while(s!==null&&s!==i);a===null?o=r:a.next=u,vt(r,t.memoizedState)||(Ve=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){l=e;do i=l.lane,ce.lanes|=i,Ln|=i,l=l.next;while(l!==e)}else l===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function go(e){var t=ot(),n=t.queue;if(n===null)throw Error(_(311));n.lastRenderedReducer=e;var r=n.dispatch,l=n.pending,i=t.memoizedState;if(l!==null){n.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);vt(i,t.memoizedState)||(Ve=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Rf(){}function Lf(e,t){var n=ce,r=ot(),l=t(),i=!vt(r.memoizedState,l);if(i&&(r.memoizedState=l,Ve=!0),r=r.queue,ta(Df.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||ke!==null&&ke.memoizedState.tag&1){if(n.flags|=2048,ol(9,Nf.bind(null,n,r,l,t),void 0,null),xe===null)throw Error(_(349));Rn&30||Tf(n,t,l)}return l}function Tf(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Nf(e,t,n,r){t.value=n,t.getSnapshot=r,Mf(t)&&zf(e)}function Df(e,t,n){return n(function(){Mf(t)&&zf(e)})}function Mf(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!vt(e,n)}catch{return!0}}function zf(e){var t=zt(e,1);t!==null&&mt(t,e,1,-1)}function ys(e){var t=wt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:il,lastRenderedState:e},t.queue=e,e=e.dispatch=Xh.bind(null,ce,e),[t.memoizedState,e]}function ol(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Of(){return ot().memoizedState}function Gl(e,t,n,r){var l=wt();ce.flags|=e,l.memoizedState=ol(1|t,n,void 0,r===void 0?null:r)}function Oi(e,t,n,r){var l=ot();r=r===void 0?null:r;var i=void 0;if(Se!==null){var o=Se.memoizedState;if(i=o.destroy,r!==null&&qu(r,o.deps)){l.memoizedState=ol(t,n,i,r);return}}ce.flags|=e,l.memoizedState=ol(1|t,n,i,r)}function gs(e,t){return Gl(8390656,8,e,t)}function ta(e,t){return Oi(2048,8,e,t)}function Ff(e,t){return Oi(4,2,e,t)}function If(e,t){return Oi(4,4,e,t)}function jf(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Uf(e,t,n){return n=n!=null?n.concat([e]):null,Oi(4,4,jf.bind(null,t,e),n)}function na(){}function Af(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&qu(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Bf(e,t){var n=ot();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&qu(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function $f(e,t,n){return Rn&21?(vt(n,t)||(n=Qc(),ce.lanes|=n,Ln|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ve=!0),e.memoizedState=n)}function Kh(e,t){var n=q;q=n!==0&&4>n?n:4,e(!0);var r=vo.transition;vo.transition={};try{e(!1),t()}finally{q=n,vo.transition=r}}function Vf(){return ot().memoizedState}function Yh(e,t,n){var r=tn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hf(e))Wf(t,n);else if(n=Cf(e,t,n,r),n!==null){var l=je();mt(n,e,r,l),Qf(n,t,r)}}function Xh(e,t,n){var r=tn(e),l={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hf(e))Wf(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,u=i(o,n);if(l.hasEagerState=!0,l.eagerState=u,vt(u,o)){var a=t.interleaved;a===null?(l.next=l,Yu(t)):(l.next=a.next,a.next=l),t.interleaved=l;return}}catch{}finally{}n=Cf(e,t,l,r),n!==null&&(l=je(),mt(n,e,r,l),Qf(n,t,r))}}function Hf(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function Wf(e,t){$r=gi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qf(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,zu(e,n)}}var wi={readContext:it,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},Gh={readContext:it,useCallback:function(e,t){return wt().memoizedState=[e,t===void 0?null:t],e},useContext:it,useEffect:gs,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Gl(4194308,4,jf.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Gl(4194308,4,e,t)},useInsertionEffect:function(e,t){return Gl(4,2,e,t)},useMemo:function(e,t){var n=wt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=wt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Yh.bind(null,ce,e),[r.memoizedState,e]},useRef:function(e){var t=wt();return e={current:e},t.memoizedState=e},useState:ys,useDebugValue:na,useDeferredValue:function(e){return wt().memoizedState=e},useTransition:function(){var e=ys(!1),t=e[0];return e=Kh.bind(null,e[1]),wt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ce,l=wt();if(ae){if(n===void 0)throw Error(_(407));n=n()}else{if(n=t(),xe===null)throw Error(_(349));Rn&30||Tf(r,t,n)}l.memoizedState=n;var i={value:n,getSnapshot:t};return l.queue=i,gs(Df.bind(null,r,i,e),[e]),r.flags|=2048,ol(9,Nf.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=wt(),t=xe.identifierPrefix;if(ae){var n=Lt,r=Rt;n=(r&~(1<<32-ht(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ll++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Qh++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Jh={readContext:it,useCallback:Af,useContext:it,useEffect:ta,useImperativeHandle:Uf,useInsertionEffect:Ff,useLayoutEffect:If,useMemo:Bf,useReducer:yo,useRef:Of,useState:function(){return yo(il)},useDebugValue:na,useDeferredValue:function(e){var t=ot();return $f(t,Se.memoizedState,e)},useTransition:function(){var e=yo(il)[0],t=ot().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Lf,useId:Vf,unstable_isNewReconciler:!1},Zh={readContext:it,useCallback:Af,useContext:it,useEffect:ta,useImperativeHandle:Uf,useInsertionEffect:Ff,useLayoutEffect:If,useMemo:Bf,useReducer:go,useRef:Of,useState:function(){return go(il)},useDebugValue:na,useDeferredValue:function(e){var t=ot();return Se===null?t.memoizedState=e:$f(t,Se.memoizedState,e)},useTransition:function(){var e=go(il)[0],t=ot().memoizedState;return[e,t]},useMutableSource:Rf,useSyncExternalStore:Lf,useId:Vf,unstable_isNewReconciler:!1};function ct(e,t){if(e&&e.defaultProps){t=fe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function nu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:fe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Fi={isMounted:function(e){return(e=e._reactInternals)?Mn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=je(),l=tn(e),i=Tt(r,l);i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,l),t!==null&&(mt(t,e,l,r),Yl(t,e,l))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=je(),l=tn(e),i=Tt(r,l);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bt(e,i,l),t!==null&&(mt(t,e,l,r),Yl(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=je(),r=tn(e),l=Tt(n,r);l.tag=2,t!=null&&(l.callback=t),t=bt(e,l,r),t!==null&&(mt(t,e,r,n),Yl(t,e,r))}};function ws(e,t,n,r,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!qr(n,r)||!qr(l,i):!0}function Kf(e,t,n){var r=!1,l=ln,i=t.contextType;return typeof i=="object"&&i!==null?i=it(i):(l=We(t)?Pn:Me.current,r=t.contextTypes,i=(r=r!=null)?lr(e,l):ln),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Fi,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=i),t}function Ss(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Fi.enqueueReplaceState(t,t.state,null)}function ru(e,t,n,r){var l=e.stateNode;l.props=n,l.state=e.memoizedState,l.refs={},Xu(e);var i=t.contextType;typeof i=="object"&&i!==null?l.context=it(i):(i=We(t)?Pn:Me.current,l.context=lr(e,i)),l.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(nu(e,t,i,n),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Fi.enqueueReplaceState(l,l.state,null),vi(e,n,l,r),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function ar(e,t){try{var n="",r=t;do n+=Pp(r),r=r.return;while(r);var l=n}catch(i){l=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:l,digest:null}}function wo(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function lu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var qh=typeof WeakMap=="function"?WeakMap:Map;function Yf(e,t,n){n=Tt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ei||(Ei=!0,hu=r),lu(e,t)},n}function Xf(e,t,n){n=Tt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var l=t.value;n.payload=function(){return r(l)},n.callback=function(){lu(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){lu(e,t),typeof r!="function"&&(en===null?en=new Set([this]):en.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Es(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new qh;var l=new Set;r.set(t,l)}else l=r.get(t),l===void 0&&(l=new Set,r.set(t,l));l.has(n)||(l.add(n),e=dm.bind(null,e,t,n),t.then(e,e))}function ks(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xs(e,t,n,r,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Tt(-1,1),t.tag=2,bt(n,t,1))),n.lanes|=1),e)}var bh=Ft.ReactCurrentOwner,Ve=!1;function Ie(e,t,n,r){t.child=e===null?xf(t,null,n,r):or(t,e.child,n,r)}function Cs(e,t,n,r,l){n=n.render;var i=t.ref;return tr(t,l),r=bu(e,t,n,r,i,l),n=ea(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ot(e,t,l)):(ae&&n&&$u(t),t.flags|=1,Ie(e,t,r,l),t.child)}function Ps(e,t,n,r,l){if(e===null){var i=n.type;return typeof i=="function"&&!ca(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Gf(e,t,i,r,l)):(e=bl(n.type,null,r,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&l)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:qr,n(o,r)&&e.ref===t.ref)return Ot(e,t,l)}return t.flags|=1,e=nn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Gf(e,t,n,r,l){if(e!==null){var i=e.memoizedProps;if(qr(i,r)&&e.ref===t.ref)if(Ve=!1,t.pendingProps=r=i,(e.lanes&l)!==0)e.flags&131072&&(Ve=!0);else return t.lanes=e.lanes,Ot(e,t,l)}return iu(e,t,n,r,l)}function Jf(e,t,n){var r=t.pendingProps,l=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},le(Jn,Ye),Ye|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,le(Jn,Ye),Ye|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,le(Jn,Ye),Ye|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,le(Jn,Ye),Ye|=r;return Ie(e,t,l,n),t.child}function Zf(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function iu(e,t,n,r,l){var i=We(n)?Pn:Me.current;return i=lr(t,i),tr(t,l),n=bu(e,t,n,r,i,l),r=ea(),e!==null&&!Ve?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,Ot(e,t,l)):(ae&&r&&$u(t),t.flags|=1,Ie(e,t,n,l),t.child)}function _s(e,t,n,r,l){if(We(n)){var i=!0;fi(t)}else i=!1;if(tr(t,l),t.stateNode===null)Jl(e,t),Kf(t,n,r),ru(t,n,r,l),r=!0;else if(e===null){var o=t.stateNode,u=t.memoizedProps;o.props=u;var a=o.context,s=n.contextType;typeof s=="object"&&s!==null?s=it(s):(s=We(n)?Pn:Me.current,s=lr(t,s));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof o.getSnapshotBeforeUpdate=="function";f||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(u!==r||a!==s)&&Ss(t,o,r,s),Ht=!1;var h=t.memoizedState;o.state=h,vi(t,r,o,l),a=t.memoizedState,u!==r||h!==a||He.current||Ht?(typeof d=="function"&&(nu(t,n,d,r),a=t.memoizedState),(u=Ht||ws(t,n,u,r,h,a,s))?(f||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),o.props=r,o.state=a,o.context=s,r=u):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Pf(e,t),u=t.memoizedProps,s=t.type===t.elementType?u:ct(t.type,u),o.props=s,f=t.pendingProps,h=o.context,a=n.contextType,typeof a=="object"&&a!==null?a=it(a):(a=We(n)?Pn:Me.current,a=lr(t,a));var w=n.getDerivedStateFromProps;(d=typeof w=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(u!==f||h!==a)&&Ss(t,o,r,a),Ht=!1,h=t.memoizedState,o.state=h,vi(t,r,o,l);var k=t.memoizedState;u!==f||h!==k||He.current||Ht?(typeof w=="function"&&(nu(t,n,w,r),k=t.memoizedState),(s=Ht||ws(t,n,s,r,h,k,a)||!1)?(d||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,k,a),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,k,a)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=k),o.props=r,o.state=k,o.context=a,r=s):(typeof o.componentDidUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||u===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return ou(e,t,n,r,i,l)}function ou(e,t,n,r,l,i){Zf(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return l&&fs(t,n,!1),Ot(e,t,i);r=t.stateNode,bh.current=t;var u=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=or(t,e.child,null,i),t.child=or(t,null,u,i)):Ie(e,t,u,i),t.memoizedState=r.state,l&&fs(t,n,!0),t.child}function qf(e){var t=e.stateNode;t.pendingContext?cs(e,t.pendingContext,t.pendingContext!==t.context):t.context&&cs(e,t.context,!1),Gu(e,t.containerInfo)}function Rs(e,t,n,r,l){return ir(),Hu(l),t.flags|=256,Ie(e,t,n,r),t.child}var uu={dehydrated:null,treeContext:null,retryLane:0};function au(e){return{baseLanes:e,cachePool:null,transitions:null}}function bf(e,t,n){var r=t.pendingProps,l=se.current,i=!1,o=(t.flags&128)!==0,u;if((u=o)||(u=e!==null&&e.memoizedState===null?!1:(l&2)!==0),u?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),le(se,l&1),e===null)return eu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Ui(o,r,0,null),e=Cn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=au(n),t.memoizedState=uu,e):ra(t,o));if(l=e.memoizedState,l!==null&&(u=l.dehydrated,u!==null))return em(e,t,o,r,u,l,n);if(i){i=r.fallback,o=t.mode,l=e.child,u=l.sibling;var a={mode:"hidden",children:r.children};return!(o&1)&&t.child!==l?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=nn(l,a),r.subtreeFlags=l.subtreeFlags&14680064),u!==null?i=nn(u,i):(i=Cn(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?au(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=uu,r}return i=e.child,e=i.sibling,r=nn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function ra(e,t){return t=Ui({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function jl(e,t,n,r){return r!==null&&Hu(r),or(t,e.child,null,n),e=ra(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function em(e,t,n,r,l,i,o){if(n)return t.flags&256?(t.flags&=-257,r=wo(Error(_(422))),jl(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,l=t.mode,r=Ui({mode:"visible",children:r.children},l,0,null),i=Cn(i,l,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&or(t,e.child,null,o),t.child.memoizedState=au(o),t.memoizedState=uu,i);if(!(t.mode&1))return jl(e,t,o,null);if(l.data==="$!"){if(r=l.nextSibling&&l.nextSibling.dataset,r)var u=r.dgst;return r=u,i=Error(_(419)),r=wo(i,r,void 0),jl(e,t,o,r)}if(u=(o&e.childLanes)!==0,Ve||u){if(r=xe,r!==null){switch(o&-o){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(r.suspendedLanes|o)?0:l,l!==0&&l!==i.retryLane&&(i.retryLane=l,zt(e,l),mt(r,e,l,-1))}return sa(),r=wo(Error(_(421))),jl(e,t,o,r)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=pm.bind(null,e),l._reactRetry=t,null):(e=i.treeContext,Ge=qt(l.nextSibling),Je=t,ae=!0,pt=null,e!==null&&(tt[nt++]=Rt,tt[nt++]=Lt,tt[nt++]=_n,Rt=e.id,Lt=e.overflow,_n=t),t=ra(t,r.children),t.flags|=4096,t)}function Ls(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),tu(e.return,t,n)}function So(e,t,n,r,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=l)}function ed(e,t,n){var r=t.pendingProps,l=r.revealOrder,i=r.tail;if(Ie(e,t,r.children,n),r=se.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Ls(e,n,t);else if(e.tag===19)Ls(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(le(se,r),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(n=t.child,l=null;n!==null;)e=n.alternate,e!==null&&yi(e)===null&&(l=n),n=n.sibling;n=l,n===null?(l=t.child,t.child=null):(l=n.sibling,n.sibling=null),So(t,!1,l,n,i);break;case"backwards":for(n=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&yi(e)===null){t.child=l;break}e=l.sibling,l.sibling=n,n=l,l=e}So(t,!0,n,null,i);break;case"together":So(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Jl(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ot(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Ln|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,n=nn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=nn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function tm(e,t,n){switch(t.tag){case 3:qf(t),ir();break;case 5:_f(t);break;case 1:We(t.type)&&fi(t);break;case 4:Gu(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,l=t.memoizedProps.value;le(hi,r._currentValue),r._currentValue=l;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(le(se,se.current&1),t.flags|=128,null):n&t.child.childLanes?bf(e,t,n):(le(se,se.current&1),e=Ot(e,t,n),e!==null?e.sibling:null);le(se,se.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return ed(e,t,n);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),le(se,se.current),r)break;return null;case 22:case 23:return t.lanes=0,Jf(e,t,n)}return Ot(e,t,n)}var td,su,nd,rd;td=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};su=function(){};nd=function(e,t,n,r){var l=e.memoizedProps;if(l!==r){e=t.stateNode,En(kt.current);var i=null;switch(n){case"input":l=Do(e,l),r=Do(e,r),i=[];break;case"select":l=fe({},l,{value:void 0}),r=fe({},r,{value:void 0}),i=[];break;case"textarea":l=Oo(e,l),r=Oo(e,r),i=[];break;default:typeof l.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=si)}Io(n,r);var o;n=null;for(s in l)if(!r.hasOwnProperty(s)&&l.hasOwnProperty(s)&&l[s]!=null)if(s==="style"){var u=l[s];for(o in u)u.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else s!=="dangerouslySetInnerHTML"&&s!=="children"&&s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Qr.hasOwnProperty(s)?i||(i=[]):(i=i||[]).push(s,null));for(s in r){var a=r[s];if(u=l!=null?l[s]:void 0,r.hasOwnProperty(s)&&a!==u&&(a!=null||u!=null))if(s==="style")if(u){for(o in u)!u.hasOwnProperty(o)||a&&a.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in a)a.hasOwnProperty(o)&&u[o]!==a[o]&&(n||(n={}),n[o]=a[o])}else n||(i||(i=[]),i.push(s,n)),n=a;else s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,u=u?u.__html:void 0,a!=null&&u!==a&&(i=i||[]).push(s,a)):s==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(s,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&(Qr.hasOwnProperty(s)?(a!=null&&s==="onScroll"&&ie("scroll",e),i||u===a||(i=[])):(i=i||[]).push(s,a))}n&&(i=i||[]).push("style",n);var s=i;(t.updateQueue=s)&&(t.flags|=4)}};rd=function(e,t,n,r){n!==r&&(t.flags|=4)};function _r(e,t){if(!ae)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ne(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags&14680064,r|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)n|=l.lanes|l.childLanes,r|=l.subtreeFlags,r|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function nm(e,t,n){var r=t.pendingProps;switch(Vu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ne(t),null;case 1:return We(t.type)&&ci(),Ne(t),null;case 3:return r=t.stateNode,ur(),oe(He),oe(Me),Zu(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Fl(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,pt!==null&&(yu(pt),pt=null))),su(e,t),Ne(t),null;case 5:Ju(t);var l=En(rl.current);if(n=t.type,e!==null&&t.stateNode!=null)nd(e,t,n,r,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(_(166));return Ne(t),null}if(e=En(kt.current),Fl(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[St]=t,r[tl]=i,e=(t.mode&1)!==0,n){case"dialog":ie("cancel",r),ie("close",r);break;case"iframe":case"object":case"embed":ie("load",r);break;case"video":case"audio":for(l=0;l<Or.length;l++)ie(Or[l],r);break;case"source":ie("error",r);break;case"img":case"image":case"link":ie("error",r),ie("load",r);break;case"details":ie("toggle",r);break;case"input":ja(r,i),ie("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},ie("invalid",r);break;case"textarea":Aa(r,i),ie("invalid",r)}Io(n,i),l=null;for(var o in i)if(i.hasOwnProperty(o)){var u=i[o];o==="children"?typeof u=="string"?r.textContent!==u&&(i.suppressHydrationWarning!==!0&&Ol(r.textContent,u,e),l=["children",u]):typeof u=="number"&&r.textContent!==""+u&&(i.suppressHydrationWarning!==!0&&Ol(r.textContent,u,e),l=["children",""+u]):Qr.hasOwnProperty(o)&&u!=null&&o==="onScroll"&&ie("scroll",r)}switch(n){case"input":_l(r),Ua(r,i,!0);break;case"textarea":_l(r),Ba(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=si)}r=l,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Nc(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[St]=t,e[tl]=r,td(e,t,!1,!1),t.stateNode=e;e:{switch(o=jo(n,r),n){case"dialog":ie("cancel",e),ie("close",e),l=r;break;case"iframe":case"object":case"embed":ie("load",e),l=r;break;case"video":case"audio":for(l=0;l<Or.length;l++)ie(Or[l],e);l=r;break;case"source":ie("error",e),l=r;break;case"img":case"image":case"link":ie("error",e),ie("load",e),l=r;break;case"details":ie("toggle",e),l=r;break;case"input":ja(e,r),l=Do(e,r),ie("invalid",e);break;case"option":l=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},l=fe({},r,{value:void 0}),ie("invalid",e);break;case"textarea":Aa(e,r),l=Oo(e,r),ie("invalid",e);break;default:l=r}Io(n,l),u=l;for(i in u)if(u.hasOwnProperty(i)){var a=u[i];i==="style"?zc(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Dc(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Kr(e,a):typeof a=="number"&&Kr(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Qr.hasOwnProperty(i)?a!=null&&i==="onScroll"&&ie("scroll",e):a!=null&&Ru(e,i,a,o))}switch(n){case"input":_l(e),Ua(e,r,!1);break;case"textarea":_l(e),Ba(e);break;case"option":r.value!=null&&e.setAttribute("value",""+rn(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Zn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Zn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=si)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ne(t),null;case 6:if(e&&t.stateNode!=null)rd(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(_(166));if(n=En(rl.current),En(kt.current),Fl(t)){if(r=t.stateNode,n=t.memoizedProps,r[St]=t,(i=r.nodeValue!==n)&&(e=Je,e!==null))switch(e.tag){case 3:Ol(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ol(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[St]=t,t.stateNode=r}return Ne(t),null;case 13:if(oe(se),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ae&&Ge!==null&&t.mode&1&&!(t.flags&128))Ef(),ir(),t.flags|=98560,i=!1;else if(i=Fl(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(_(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(_(317));i[St]=t}else ir(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ne(t),i=!1}else pt!==null&&(yu(pt),pt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||se.current&1?Ee===0&&(Ee=3):sa())),t.updateQueue!==null&&(t.flags|=4),Ne(t),null);case 4:return ur(),su(e,t),e===null&&br(t.stateNode.containerInfo),Ne(t),null;case 10:return Ku(t.type._context),Ne(t),null;case 17:return We(t.type)&&ci(),Ne(t),null;case 19:if(oe(se),i=t.memoizedState,i===null)return Ne(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)_r(i,!1);else{if(Ee!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=yi(e),o!==null){for(t.flags|=128,_r(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return le(se,se.current&1|2),t.child}e=e.sibling}i.tail!==null&&me()>sr&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304)}else{if(!r)if(e=yi(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),_r(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!ae)return Ne(t),null}else 2*me()-i.renderingStartTime>sr&&n!==1073741824&&(t.flags|=128,r=!0,_r(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=me(),t.sibling=null,n=se.current,le(se,r?n&1|2:n&1),t):(Ne(t),null);case 22:case 23:return aa(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ye&1073741824&&(Ne(t),t.subtreeFlags&6&&(t.flags|=8192)):Ne(t),null;case 24:return null;case 25:return null}throw Error(_(156,t.tag))}function rm(e,t){switch(Vu(t),t.tag){case 1:return We(t.type)&&ci(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ur(),oe(He),oe(Me),Zu(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Ju(t),null;case 13:if(oe(se),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));ir()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return oe(se),null;case 4:return ur(),null;case 10:return Ku(t.type._context),null;case 22:case 23:return aa(),null;case 24:return null;default:return null}}var Ul=!1,De=!1,lm=typeof WeakSet=="function"?WeakSet:Set,z=null;function Gn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){he(e,t,r)}else n.current=null}function cu(e,t,n){try{n()}catch(r){he(e,t,r)}}var Ts=!1;function im(e,t){if(Yo=oi,e=af(),Bu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var l=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,u=-1,a=-1,s=0,d=0,f=e,h=null;t:for(;;){for(var w;f!==n||l!==0&&f.nodeType!==3||(u=o+l),f!==i||r!==0&&f.nodeType!==3||(a=o+r),f.nodeType===3&&(o+=f.nodeValue.length),(w=f.firstChild)!==null;)h=f,f=w;for(;;){if(f===e)break t;if(h===n&&++s===l&&(u=o),h===i&&++d===r&&(a=o),(w=f.nextSibling)!==null)break;f=h,h=f.parentNode}f=w}n=u===-1||a===-1?null:{start:u,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xo={focusedElem:e,selectionRange:n},oi=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var k=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(k!==null){var E=k.memoizedProps,D=k.memoizedState,m=t.stateNode,c=m.getSnapshotBeforeUpdate(t.elementType===t.type?E:ct(t.type,E),D);m.__reactInternalSnapshotBeforeUpdate=c}break;case 3:var v=t.stateNode.containerInfo;v.nodeType===1?v.textContent="":v.nodeType===9&&v.documentElement&&v.removeChild(v.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(_(163))}}catch(x){he(t,t.return,x)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return k=Ts,Ts=!1,k}function Vr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var l=r=r.next;do{if((l.tag&e)===e){var i=l.destroy;l.destroy=void 0,i!==void 0&&cu(t,n,i)}l=l.next}while(l!==r)}}function Ii(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function fu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ld(e){var t=e.alternate;t!==null&&(e.alternate=null,ld(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[St],delete t[tl],delete t[Zo],delete t[$h],delete t[Vh])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function id(e){return e.tag===5||e.tag===3||e.tag===4}function Ns(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||id(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function du(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=si));else if(r!==4&&(e=e.child,e!==null))for(du(e,t,n),e=e.sibling;e!==null;)du(e,t,n),e=e.sibling}function pu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(pu(e,t,n),e=e.sibling;e!==null;)pu(e,t,n),e=e.sibling}var _e=null,ft=!1;function Bt(e,t,n){for(n=n.child;n!==null;)od(e,t,n),n=n.sibling}function od(e,t,n){if(Et&&typeof Et.onCommitFiberUnmount=="function")try{Et.onCommitFiberUnmount(Li,n)}catch{}switch(n.tag){case 5:De||Gn(n,t);case 6:var r=_e,l=ft;_e=null,Bt(e,t,n),_e=r,ft=l,_e!==null&&(ft?(e=_e,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):_e.removeChild(n.stateNode));break;case 18:_e!==null&&(ft?(e=_e,n=n.stateNode,e.nodeType===8?po(e.parentNode,n):e.nodeType===1&&po(e,n),Jr(e)):po(_e,n.stateNode));break;case 4:r=_e,l=ft,_e=n.stateNode.containerInfo,ft=!0,Bt(e,t,n),_e=r,ft=l;break;case 0:case 11:case 14:case 15:if(!De&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){l=r=r.next;do{var i=l,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&cu(n,t,o),l=l.next}while(l!==r)}Bt(e,t,n);break;case 1:if(!De&&(Gn(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(u){he(n,t,u)}Bt(e,t,n);break;case 21:Bt(e,t,n);break;case 22:n.mode&1?(De=(r=De)||n.memoizedState!==null,Bt(e,t,n),De=r):Bt(e,t,n);break;default:Bt(e,t,n)}}function Ds(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new lm),t.forEach(function(r){var l=hm.bind(null,e,r);n.has(r)||(n.add(r),r.then(l,l))})}}function st(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var l=n[r];try{var i=e,o=t,u=o;e:for(;u!==null;){switch(u.tag){case 5:_e=u.stateNode,ft=!1;break e;case 3:_e=u.stateNode.containerInfo,ft=!0;break e;case 4:_e=u.stateNode.containerInfo,ft=!0;break e}u=u.return}if(_e===null)throw Error(_(160));od(i,o,l),_e=null,ft=!1;var a=l.alternate;a!==null&&(a.return=null),l.return=null}catch(s){he(l,t,s)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ud(t,e),t=t.sibling}function ud(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(st(t,e),gt(e),r&4){try{Vr(3,e,e.return),Ii(3,e)}catch(E){he(e,e.return,E)}try{Vr(5,e,e.return)}catch(E){he(e,e.return,E)}}break;case 1:st(t,e),gt(e),r&512&&n!==null&&Gn(n,n.return);break;case 5:if(st(t,e),gt(e),r&512&&n!==null&&Gn(n,n.return),e.flags&32){var l=e.stateNode;try{Kr(l,"")}catch(E){he(e,e.return,E)}}if(r&4&&(l=e.stateNode,l!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,u=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{u==="input"&&i.type==="radio"&&i.name!=null&&Lc(l,i),jo(u,o);var s=jo(u,i);for(o=0;o<a.length;o+=2){var d=a[o],f=a[o+1];d==="style"?zc(l,f):d==="dangerouslySetInnerHTML"?Dc(l,f):d==="children"?Kr(l,f):Ru(l,d,f,s)}switch(u){case"input":Mo(l,i);break;case"textarea":Tc(l,i);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!i.multiple;var w=i.value;w!=null?Zn(l,!!i.multiple,w,!1):h!==!!i.multiple&&(i.defaultValue!=null?Zn(l,!!i.multiple,i.defaultValue,!0):Zn(l,!!i.multiple,i.multiple?[]:"",!1))}l[tl]=i}catch(E){he(e,e.return,E)}}break;case 6:if(st(t,e),gt(e),r&4){if(e.stateNode===null)throw Error(_(162));l=e.stateNode,i=e.memoizedProps;try{l.nodeValue=i}catch(E){he(e,e.return,E)}}break;case 3:if(st(t,e),gt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Jr(t.containerInfo)}catch(E){he(e,e.return,E)}break;case 4:st(t,e),gt(e);break;case 13:st(t,e),gt(e),l=e.child,l.flags&8192&&(i=l.memoizedState!==null,l.stateNode.isHidden=i,!i||l.alternate!==null&&l.alternate.memoizedState!==null||(oa=me())),r&4&&Ds(e);break;case 22:if(d=n!==null&&n.memoizedState!==null,e.mode&1?(De=(s=De)||d,st(t,e),De=s):st(t,e),gt(e),r&8192){if(s=e.memoizedState!==null,(e.stateNode.isHidden=s)&&!d&&e.mode&1)for(z=e,d=e.child;d!==null;){for(f=z=d;z!==null;){switch(h=z,w=h.child,h.tag){case 0:case 11:case 14:case 15:Vr(4,h,h.return);break;case 1:Gn(h,h.return);var k=h.stateNode;if(typeof k.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,k.props=t.memoizedProps,k.state=t.memoizedState,k.componentWillUnmount()}catch(E){he(r,n,E)}}break;case 5:Gn(h,h.return);break;case 22:if(h.memoizedState!==null){zs(f);continue}}w!==null?(w.return=h,z=w):zs(f)}d=d.sibling}e:for(d=null,f=e;;){if(f.tag===5){if(d===null){d=f;try{l=f.stateNode,s?(i=l.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(u=f.stateNode,a=f.memoizedProps.style,o=a!=null&&a.hasOwnProperty("display")?a.display:null,u.style.display=Mc("display",o))}catch(E){he(e,e.return,E)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=s?"":f.memoizedProps}catch(E){he(e,e.return,E)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:st(t,e),gt(e),r&4&&Ds(e);break;case 21:break;default:st(t,e),gt(e)}}function gt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(id(n)){var r=n;break e}n=n.return}throw Error(_(160))}switch(r.tag){case 5:var l=r.stateNode;r.flags&32&&(Kr(l,""),r.flags&=-33);var i=Ns(e);pu(e,i,l);break;case 3:case 4:var o=r.stateNode.containerInfo,u=Ns(e);du(e,u,o);break;default:throw Error(_(161))}}catch(a){he(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function om(e,t,n){z=e,ad(e)}function ad(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var l=z,i=l.child;if(l.tag===22&&r){var o=l.memoizedState!==null||Ul;if(!o){var u=l.alternate,a=u!==null&&u.memoizedState!==null||De;u=Ul;var s=De;if(Ul=o,(De=a)&&!s)for(z=l;z!==null;)o=z,a=o.child,o.tag===22&&o.memoizedState!==null?Os(l):a!==null?(a.return=o,z=a):Os(l);for(;i!==null;)z=i,ad(i),i=i.sibling;z=l,Ul=u,De=s}Ms(e)}else l.subtreeFlags&8772&&i!==null?(i.return=l,z=i):Ms(e)}}function Ms(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:De||Ii(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!De)if(n===null)r.componentDidMount();else{var l=t.elementType===t.type?n.memoizedProps:ct(t.type,n.memoizedProps);r.componentDidUpdate(l,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&vs(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}vs(t,o,n)}break;case 5:var u=t.stateNode;if(n===null&&t.flags&4){n=u;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var s=t.alternate;if(s!==null){var d=s.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Jr(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(_(163))}De||t.flags&512&&fu(t)}catch(h){he(t,t.return,h)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function zs(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function Os(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Ii(4,t)}catch(a){he(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var l=t.return;try{r.componentDidMount()}catch(a){he(t,l,a)}}var i=t.return;try{fu(t)}catch(a){he(t,i,a)}break;case 5:var o=t.return;try{fu(t)}catch(a){he(t,o,a)}}}catch(a){he(t,t.return,a)}if(t===e){z=null;break}var u=t.sibling;if(u!==null){u.return=t.return,z=u;break}z=t.return}}var um=Math.ceil,Si=Ft.ReactCurrentDispatcher,la=Ft.ReactCurrentOwner,lt=Ft.ReactCurrentBatchConfig,X=0,xe=null,ge=null,Re=0,Ye=0,Jn=un(0),Ee=0,ul=null,Ln=0,ji=0,ia=0,Hr=null,$e=null,oa=0,sr=1/0,Pt=null,Ei=!1,hu=null,en=null,Al=!1,Yt=null,ki=0,Wr=0,mu=null,Zl=-1,ql=0;function je(){return X&6?me():Zl!==-1?Zl:Zl=me()}function tn(e){return e.mode&1?X&2&&Re!==0?Re&-Re:Wh.transition!==null?(ql===0&&(ql=Qc()),ql):(e=q,e!==0||(e=window.event,e=e===void 0?16:qc(e.type)),e):1}function mt(e,t,n,r){if(50<Wr)throw Wr=0,mu=null,Error(_(185));dl(e,n,r),(!(X&2)||e!==xe)&&(e===xe&&(!(X&2)&&(ji|=n),Ee===4&&Qt(e,Re)),Qe(e,r),n===1&&X===0&&!(t.mode&1)&&(sr=me()+500,zi&&an()))}function Qe(e,t){var n=e.callbackNode;Wp(e,t);var r=ii(e,e===xe?Re:0);if(r===0)n!==null&&Ha(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ha(n),t===1)e.tag===0?Hh(Fs.bind(null,e)):gf(Fs.bind(null,e)),Ah(function(){!(X&6)&&an()}),n=null;else{switch(Kc(r)){case 1:n=Mu;break;case 4:n=Hc;break;case 16:n=li;break;case 536870912:n=Wc;break;default:n=li}n=vd(n,sd.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function sd(e,t){if(Zl=-1,ql=0,X&6)throw Error(_(327));var n=e.callbackNode;if(nr()&&e.callbackNode!==n)return null;var r=ii(e,e===xe?Re:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=xi(e,r);else{t=r;var l=X;X|=2;var i=fd();(xe!==e||Re!==t)&&(Pt=null,sr=me()+500,xn(e,t));do try{cm();break}catch(u){cd(e,u)}while(!0);Qu(),Si.current=i,X=l,ge!==null?t=0:(xe=null,Re=0,t=Ee)}if(t!==0){if(t===2&&(l=Vo(e),l!==0&&(r=l,t=vu(e,l))),t===1)throw n=ul,xn(e,0),Qt(e,r),Qe(e,me()),n;if(t===6)Qt(e,r);else{if(l=e.current.alternate,!(r&30)&&!am(l)&&(t=xi(e,r),t===2&&(i=Vo(e),i!==0&&(r=i,t=vu(e,i))),t===1))throw n=ul,xn(e,0),Qt(e,r),Qe(e,me()),n;switch(e.finishedWork=l,e.finishedLanes=r,t){case 0:case 1:throw Error(_(345));case 2:vn(e,$e,Pt);break;case 3:if(Qt(e,r),(r&130023424)===r&&(t=oa+500-me(),10<t)){if(ii(e,0)!==0)break;if(l=e.suspendedLanes,(l&r)!==r){je(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=Jo(vn.bind(null,e,$e,Pt),t);break}vn(e,$e,Pt);break;case 4:if(Qt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,l=-1;0<r;){var o=31-ht(r);i=1<<o,o=t[o],o>l&&(l=o),r&=~i}if(r=l,r=me()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*um(r/1960))-r,10<r){e.timeoutHandle=Jo(vn.bind(null,e,$e,Pt),r);break}vn(e,$e,Pt);break;case 5:vn(e,$e,Pt);break;default:throw Error(_(329))}}}return Qe(e,me()),e.callbackNode===n?sd.bind(null,e):null}function vu(e,t){var n=Hr;return e.current.memoizedState.isDehydrated&&(xn(e,t).flags|=256),e=xi(e,t),e!==2&&(t=$e,$e=n,t!==null&&yu(t)),e}function yu(e){$e===null?$e=e:$e.push.apply($e,e)}function am(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var l=n[r],i=l.getSnapshot;l=l.value;try{if(!vt(i(),l))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Qt(e,t){for(t&=~ia,t&=~ji,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ht(t),r=1<<n;e[n]=-1,t&=~r}}function Fs(e){if(X&6)throw Error(_(327));nr();var t=ii(e,0);if(!(t&1))return Qe(e,me()),null;var n=xi(e,t);if(e.tag!==0&&n===2){var r=Vo(e);r!==0&&(t=r,n=vu(e,r))}if(n===1)throw n=ul,xn(e,0),Qt(e,t),Qe(e,me()),n;if(n===6)throw Error(_(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,vn(e,$e,Pt),Qe(e,me()),null}function ua(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(sr=me()+500,zi&&an())}}function Tn(e){Yt!==null&&Yt.tag===0&&!(X&6)&&nr();var t=X;X|=1;var n=lt.transition,r=q;try{if(lt.transition=null,q=1,e)return e()}finally{q=r,lt.transition=n,X=t,!(X&6)&&an()}}function aa(){Ye=Jn.current,oe(Jn)}function xn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Uh(n)),ge!==null)for(n=ge.return;n!==null;){var r=n;switch(Vu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ci();break;case 3:ur(),oe(He),oe(Me),Zu();break;case 5:Ju(r);break;case 4:ur();break;case 13:oe(se);break;case 19:oe(se);break;case 10:Ku(r.type._context);break;case 22:case 23:aa()}n=n.return}if(xe=e,ge=e=nn(e.current,null),Re=Ye=t,Ee=0,ul=null,ia=ji=Ln=0,$e=Hr=null,Sn!==null){for(t=0;t<Sn.length;t++)if(n=Sn[t],r=n.interleaved,r!==null){n.interleaved=null;var l=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=l,r.next=o}n.pending=r}Sn=null}return e}function cd(e,t){do{var n=ge;try{if(Qu(),Xl.current=wi,gi){for(var r=ce.memoizedState;r!==null;){var l=r.queue;l!==null&&(l.pending=null),r=r.next}gi=!1}if(Rn=0,ke=Se=ce=null,$r=!1,ll=0,la.current=null,n===null||n.return===null){Ee=1,ul=t,ge=null;break}e:{var i=e,o=n.return,u=n,a=t;if(t=Re,u.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var s=a,d=u,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var h=d.alternate;h?(d.updateQueue=h.updateQueue,d.memoizedState=h.memoizedState,d.lanes=h.lanes):(d.updateQueue=null,d.memoizedState=null)}var w=ks(o);if(w!==null){w.flags&=-257,xs(w,o,u,i,t),w.mode&1&&Es(i,s,t),t=w,a=s;var k=t.updateQueue;if(k===null){var E=new Set;E.add(a),t.updateQueue=E}else k.add(a);break e}else{if(!(t&1)){Es(i,s,t),sa();break e}a=Error(_(426))}}else if(ae&&u.mode&1){var D=ks(o);if(D!==null){!(D.flags&65536)&&(D.flags|=256),xs(D,o,u,i,t),Hu(ar(a,u));break e}}i=a=ar(a,u),Ee!==4&&(Ee=2),Hr===null?Hr=[i]:Hr.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=Yf(i,a,t);ms(i,m);break e;case 1:u=a;var c=i.type,v=i.stateNode;if(!(i.flags&128)&&(typeof c.getDerivedStateFromError=="function"||v!==null&&typeof v.componentDidCatch=="function"&&(en===null||!en.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t;var x=Xf(i,u,t);ms(i,x);break e}}i=i.return}while(i!==null)}pd(n)}catch(L){t=L,ge===n&&n!==null&&(ge=n=n.return);continue}break}while(!0)}function fd(){var e=Si.current;return Si.current=wi,e===null?wi:e}function sa(){(Ee===0||Ee===3||Ee===2)&&(Ee=4),xe===null||!(Ln&268435455)&&!(ji&268435455)||Qt(xe,Re)}function xi(e,t){var n=X;X|=2;var r=fd();(xe!==e||Re!==t)&&(Pt=null,xn(e,t));do try{sm();break}catch(l){cd(e,l)}while(!0);if(Qu(),X=n,Si.current=r,ge!==null)throw Error(_(261));return xe=null,Re=0,Ee}function sm(){for(;ge!==null;)dd(ge)}function cm(){for(;ge!==null&&!Fp();)dd(ge)}function dd(e){var t=md(e.alternate,e,Ye);e.memoizedProps=e.pendingProps,t===null?pd(e):ge=t,la.current=null}function pd(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=rm(n,t),n!==null){n.flags&=32767,ge=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ee=6,ge=null;return}}else if(n=nm(n,t,Ye),n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);Ee===0&&(Ee=5)}function vn(e,t,n){var r=q,l=lt.transition;try{lt.transition=null,q=1,fm(e,t,n,r)}finally{lt.transition=l,q=r}return null}function fm(e,t,n,r){do nr();while(Yt!==null);if(X&6)throw Error(_(327));n=e.finishedWork;var l=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(_(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Qp(e,i),e===xe&&(ge=xe=null,Re=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Al||(Al=!0,vd(li,function(){return nr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=lt.transition,lt.transition=null;var o=q;q=1;var u=X;X|=4,la.current=null,im(e,n),ud(n,e),Dh(Xo),oi=!!Yo,Xo=Yo=null,e.current=n,om(n),Ip(),X=u,q=o,lt.transition=i}else e.current=n;if(Al&&(Al=!1,Yt=e,ki=l),i=e.pendingLanes,i===0&&(en=null),Ap(n.stateNode),Qe(e,me()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)l=t[n],r(l.value,{componentStack:l.stack,digest:l.digest});if(Ei)throw Ei=!1,e=hu,hu=null,e;return ki&1&&e.tag!==0&&nr(),i=e.pendingLanes,i&1?e===mu?Wr++:(Wr=0,mu=e):Wr=0,an(),null}function nr(){if(Yt!==null){var e=Kc(ki),t=lt.transition,n=q;try{if(lt.transition=null,q=16>e?16:e,Yt===null)var r=!1;else{if(e=Yt,Yt=null,ki=0,X&6)throw Error(_(331));var l=X;for(X|=4,z=e.current;z!==null;){var i=z,o=i.child;if(z.flags&16){var u=i.deletions;if(u!==null){for(var a=0;a<u.length;a++){var s=u[a];for(z=s;z!==null;){var d=z;switch(d.tag){case 0:case 11:case 15:Vr(8,d,i)}var f=d.child;if(f!==null)f.return=d,z=f;else for(;z!==null;){d=z;var h=d.sibling,w=d.return;if(ld(d),d===s){z=null;break}if(h!==null){h.return=w,z=h;break}z=w}}}var k=i.alternate;if(k!==null){var E=k.child;if(E!==null){k.child=null;do{var D=E.sibling;E.sibling=null,E=D}while(E!==null)}}z=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,z=o;else e:for(;z!==null;){if(i=z,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Vr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,z=m;break e}z=i.return}}var c=e.current;for(z=c;z!==null;){o=z;var v=o.child;if(o.subtreeFlags&2064&&v!==null)v.return=o,z=v;else e:for(o=c;z!==null;){if(u=z,u.flags&2048)try{switch(u.tag){case 0:case 11:case 15:Ii(9,u)}}catch(L){he(u,u.return,L)}if(u===o){z=null;break e}var x=u.sibling;if(x!==null){x.return=u.return,z=x;break e}z=u.return}}if(X=l,an(),Et&&typeof Et.onPostCommitFiberRoot=="function")try{Et.onPostCommitFiberRoot(Li,e)}catch{}r=!0}return r}finally{q=n,lt.transition=t}}return!1}function Is(e,t,n){t=ar(n,t),t=Yf(e,t,1),e=bt(e,t,1),t=je(),e!==null&&(dl(e,1,t),Qe(e,t))}function he(e,t,n){if(e.tag===3)Is(e,e,n);else for(;t!==null;){if(t.tag===3){Is(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(en===null||!en.has(r))){e=ar(n,e),e=Xf(t,e,1),t=bt(t,e,1),e=je(),t!==null&&(dl(t,1,e),Qe(t,e));break}}t=t.return}}function dm(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=je(),e.pingedLanes|=e.suspendedLanes&n,xe===e&&(Re&n)===n&&(Ee===4||Ee===3&&(Re&130023424)===Re&&500>me()-oa?xn(e,0):ia|=n),Qe(e,t)}function hd(e,t){t===0&&(e.mode&1?(t=Tl,Tl<<=1,!(Tl&130023424)&&(Tl=4194304)):t=1);var n=je();e=zt(e,t),e!==null&&(dl(e,t,n),Qe(e,n))}function pm(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),hd(e,n)}function hm(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,l=e.memoizedState;l!==null&&(n=l.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(_(314))}r!==null&&r.delete(t),hd(e,n)}var md;md=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||He.current)Ve=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ve=!1,tm(e,t,n);Ve=!!(e.flags&131072)}else Ve=!1,ae&&t.flags&1048576&&wf(t,pi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Jl(e,t),e=t.pendingProps;var l=lr(t,Me.current);tr(t,n),l=bu(null,t,r,e,l,n);var i=ea();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,We(r)?(i=!0,fi(t)):i=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,Xu(t),l.updater=Fi,t.stateNode=l,l._reactInternals=t,ru(t,r,e,n),t=ou(null,t,r,!0,i,n)):(t.tag=0,ae&&i&&$u(t),Ie(null,t,l,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Jl(e,t),e=t.pendingProps,l=r._init,r=l(r._payload),t.type=r,l=t.tag=vm(r),e=ct(r,e),l){case 0:t=iu(null,t,r,e,n);break e;case 1:t=_s(null,t,r,e,n);break e;case 11:t=Cs(null,t,r,e,n);break e;case 14:t=Ps(null,t,r,ct(r.type,e),n);break e}throw Error(_(306,r,""))}return t;case 0:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ct(r,l),iu(e,t,r,l,n);case 1:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ct(r,l),_s(e,t,r,l,n);case 3:e:{if(qf(t),e===null)throw Error(_(387));r=t.pendingProps,i=t.memoizedState,l=i.element,Pf(e,t),vi(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){l=ar(Error(_(423)),t),t=Rs(e,t,r,n,l);break e}else if(r!==l){l=ar(Error(_(424)),t),t=Rs(e,t,r,n,l);break e}else for(Ge=qt(t.stateNode.containerInfo.firstChild),Je=t,ae=!0,pt=null,n=xf(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(ir(),r===l){t=Ot(e,t,n);break e}Ie(e,t,r,n)}t=t.child}return t;case 5:return _f(t),e===null&&eu(t),r=t.type,l=t.pendingProps,i=e!==null?e.memoizedProps:null,o=l.children,Go(r,l)?o=null:i!==null&&Go(r,i)&&(t.flags|=32),Zf(e,t),Ie(e,t,o,n),t.child;case 6:return e===null&&eu(t),null;case 13:return bf(e,t,n);case 4:return Gu(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=or(t,null,r,n):Ie(e,t,r,n),t.child;case 11:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ct(r,l),Cs(e,t,r,l,n);case 7:return Ie(e,t,t.pendingProps,n),t.child;case 8:return Ie(e,t,t.pendingProps.children,n),t.child;case 12:return Ie(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,l=t.pendingProps,i=t.memoizedProps,o=l.value,le(hi,r._currentValue),r._currentValue=o,i!==null)if(vt(i.value,o)){if(i.children===l.children&&!He.current){t=Ot(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var u=i.dependencies;if(u!==null){o=i.child;for(var a=u.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Tt(-1,n&-n),a.tag=2;var s=i.updateQueue;if(s!==null){s=s.shared;var d=s.pending;d===null?a.next=a:(a.next=d.next,d.next=a),s.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),tu(i.return,n,t),u.lanes|=n;break}a=a.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(_(341));o.lanes|=n,u=o.alternate,u!==null&&(u.lanes|=n),tu(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}Ie(e,t,l.children,n),t=t.child}return t;case 9:return l=t.type,r=t.pendingProps.children,tr(t,n),l=it(l),r=r(l),t.flags|=1,Ie(e,t,r,n),t.child;case 14:return r=t.type,l=ct(r,t.pendingProps),l=ct(r.type,l),Ps(e,t,r,l,n);case 15:return Gf(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,l=t.pendingProps,l=t.elementType===r?l:ct(r,l),Jl(e,t),t.tag=1,We(r)?(e=!0,fi(t)):e=!1,tr(t,n),Kf(t,r,l),ru(t,r,l,n),ou(null,t,r,!0,e,n);case 19:return ed(e,t,n);case 22:return Jf(e,t,n)}throw Error(_(156,t.tag))};function vd(e,t){return Vc(e,t)}function mm(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function rt(e,t,n,r){return new mm(e,t,n,r)}function ca(e){return e=e.prototype,!(!e||!e.isReactComponent)}function vm(e){if(typeof e=="function")return ca(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Tu)return 11;if(e===Nu)return 14}return 2}function nn(e,t){var n=e.alternate;return n===null?(n=rt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function bl(e,t,n,r,l,i){var o=2;if(r=e,typeof e=="function")ca(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case Bn:return Cn(n.children,l,i,t);case Lu:o=8,l|=8;break;case Ro:return e=rt(12,n,t,l|2),e.elementType=Ro,e.lanes=i,e;case Lo:return e=rt(13,n,t,l),e.elementType=Lo,e.lanes=i,e;case To:return e=rt(19,n,t,l),e.elementType=To,e.lanes=i,e;case Pc:return Ui(n,l,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case xc:o=10;break e;case Cc:o=9;break e;case Tu:o=11;break e;case Nu:o=14;break e;case Vt:o=16,r=null;break e}throw Error(_(130,e==null?e:typeof e,""))}return t=rt(o,n,t,l),t.elementType=e,t.type=r,t.lanes=i,t}function Cn(e,t,n,r){return e=rt(7,e,r,t),e.lanes=n,e}function Ui(e,t,n,r){return e=rt(22,e,r,t),e.elementType=Pc,e.lanes=n,e.stateNode={isHidden:!1},e}function Eo(e,t,n){return e=rt(6,e,null,t),e.lanes=n,e}function ko(e,t,n){return t=rt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function ym(e,t,n,r,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=to(0),this.expirationTimes=to(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=to(0),this.identifierPrefix=r,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function fa(e,t,n,r,l,i,o,u,a){return e=new ym(e,t,n,u,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=rt(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Xu(i),e}function gm(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:An,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function yd(e){if(!e)return ln;e=e._reactInternals;e:{if(Mn(e)!==e||e.tag!==1)throw Error(_(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(We(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(_(171))}if(e.tag===1){var n=e.type;if(We(n))return yf(e,n,t)}return t}function gd(e,t,n,r,l,i,o,u,a){return e=fa(n,r,!0,e,l,i,o,u,a),e.context=yd(null),n=e.current,r=je(),l=tn(n),i=Tt(r,l),i.callback=t??null,bt(n,i,l),e.current.lanes=l,dl(e,l,r),Qe(e,r),e}function Ai(e,t,n,r){var l=t.current,i=je(),o=tn(l);return n=yd(n),t.context===null?t.context=n:t.pendingContext=n,t=Tt(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bt(l,t,o),e!==null&&(mt(e,l,o,i),Yl(e,l,o)),o}function Ci(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function js(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function da(e,t){js(e,t),(e=e.alternate)&&js(e,t)}function wm(){return null}var wd=typeof reportError=="function"?reportError:function(e){console.error(e)};function pa(e){this._internalRoot=e}Bi.prototype.render=pa.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));Ai(e,t,null,null)};Bi.prototype.unmount=pa.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Tn(function(){Ai(null,e,null,null)}),t[Mt]=null}};function Bi(e){this._internalRoot=e}Bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=Gc();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Wt.length&&t!==0&&t<Wt[n].priority;n++);Wt.splice(n,0,e),n===0&&Zc(e)}};function ha(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $i(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Us(){}function Sm(e,t,n,r,l){if(l){if(typeof r=="function"){var i=r;r=function(){var s=Ci(o);i.call(s)}}var o=gd(t,r,e,0,null,!1,!1,"",Us);return e._reactRootContainer=o,e[Mt]=o.current,br(e.nodeType===8?e.parentNode:e),Tn(),o}for(;l=e.lastChild;)e.removeChild(l);if(typeof r=="function"){var u=r;r=function(){var s=Ci(a);u.call(s)}}var a=fa(e,0,!1,null,null,!1,!1,"",Us);return e._reactRootContainer=a,e[Mt]=a.current,br(e.nodeType===8?e.parentNode:e),Tn(function(){Ai(t,a,n,r)}),a}function Vi(e,t,n,r,l){var i=n._reactRootContainer;if(i){var o=i;if(typeof l=="function"){var u=l;l=function(){var a=Ci(o);u.call(a)}}Ai(t,o,e,l)}else o=Sm(n,t,e,l,r);return Ci(o)}Yc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=zr(t.pendingLanes);n!==0&&(zu(t,n|1),Qe(t,me()),!(X&6)&&(sr=me()+500,an()))}break;case 13:Tn(function(){var r=zt(e,1);if(r!==null){var l=je();mt(r,e,1,l)}}),da(e,1)}};Ou=function(e){if(e.tag===13){var t=zt(e,134217728);if(t!==null){var n=je();mt(t,e,134217728,n)}da(e,134217728)}};Xc=function(e){if(e.tag===13){var t=tn(e),n=zt(e,t);if(n!==null){var r=je();mt(n,e,t,r)}da(e,t)}};Gc=function(){return q};Jc=function(e,t){var n=q;try{return q=e,t()}finally{q=n}};Ao=function(e,t,n){switch(t){case"input":if(Mo(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var l=Mi(r);if(!l)throw Error(_(90));Rc(r),Mo(r,l)}}}break;case"textarea":Tc(e,n);break;case"select":t=n.value,t!=null&&Zn(e,!!n.multiple,t,!1)}};Ic=ua;jc=Tn;var Em={usingClientEntryPoint:!1,Events:[hl,Wn,Mi,Oc,Fc,ua]},Rr={findFiberByHostInstance:wn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},km={bundleType:Rr.bundleType,version:Rr.version,rendererPackageName:Rr.rendererPackageName,rendererConfig:Rr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Ft.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Bc(e),e===null?null:e.stateNode},findFiberByHostInstance:Rr.findFiberByHostInstance||wm,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Bl=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Bl.isDisabled&&Bl.supportsFiber)try{Li=Bl.inject(km),Et=Bl}catch{}}qe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Em;qe.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ha(t))throw Error(_(200));return gm(e,t,null,n)};qe.createRoot=function(e,t){if(!ha(e))throw Error(_(299));var n=!1,r="",l=wd;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=fa(e,1,!1,null,null,n,!1,r,l),e[Mt]=t.current,br(e.nodeType===8?e.parentNode:e),new pa(t)};qe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=Bc(t),e=e===null?null:e.stateNode,e};qe.flushSync=function(e){return Tn(e)};qe.hydrate=function(e,t,n){if(!$i(t))throw Error(_(200));return Vi(null,e,t,!0,n)};qe.hydrateRoot=function(e,t,n){if(!ha(e))throw Error(_(405));var r=n!=null&&n.hydratedSources||null,l=!1,i="",o=wd;if(n!=null&&(n.unstable_strictMode===!0&&(l=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=gd(t,null,e,1,n??null,l,!1,i,o),e[Mt]=t.current,br(e),r)for(e=0;e<r.length;e++)n=r[e],l=n._getVersion,l=l(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,l]:t.mutableSourceEagerHydrationData.push(n,l);return new Bi(t)};qe.render=function(e,t,n){if(!$i(t))throw Error(_(200));return Vi(null,e,t,!1,n)};qe.unmountComponentAtNode=function(e){if(!$i(e))throw Error(_(40));return e._reactRootContainer?(Tn(function(){Vi(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};qe.unstable_batchedUpdates=ua;qe.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!$i(n))throw Error(_(200));if(e==null||e._reactInternals===void 0)throw Error(_(38));return Vi(e,t,n,!1,r)};qe.version="18.3.1-next-f1338f8080-20240426";function Sd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Sd)}catch(e){console.error(e)}}Sd(),wc.exports=qe;var Ed=wc.exports;const xm=sc(Ed),Cm=ac({__proto__:null,default:xm},[Ed]);/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ue(){return ue=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ue.apply(this,arguments)}var ye;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ye||(ye={}));const As="popstate";function Pm(e){e===void 0&&(e={});function t(r,l){let{pathname:i,search:o,hash:u}=r.location;return al("",{pathname:i,search:o,hash:u},l.state&&l.state.usr||null,l.state&&l.state.key||"default")}function n(r,l){return typeof l=="string"?l:Nn(l)}return Rm(t,n,null,e)}function Q(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function cr(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function _m(){return Math.random().toString(36).substr(2,8)}function Bs(e,t){return{usr:e.state,key:e.key,idx:t}}function al(e,t,n,r){return n===void 0&&(n=null),ue({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?sn(t):t,{state:n,key:t&&t.key||r||_m()})}function Nn(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function sn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function Rm(e,t,n,r){r===void 0&&(r={});let{window:l=document.defaultView,v5Compat:i=!1}=r,o=l.history,u=ye.Pop,a=null,s=d();s==null&&(s=0,o.replaceState(ue({},o.state,{idx:s}),""));function d(){return(o.state||{idx:null}).idx}function f(){u=ye.Pop;let D=d(),m=D==null?null:D-s;s=D,a&&a({action:u,location:E.location,delta:m})}function h(D,m){u=ye.Push;let c=al(E.location,D,m);s=d()+1;let v=Bs(c,s),x=E.createHref(c);try{o.pushState(v,"",x)}catch(L){if(L instanceof DOMException&&L.name==="DataCloneError")throw L;l.location.assign(x)}i&&a&&a({action:u,location:E.location,delta:1})}function w(D,m){u=ye.Replace;let c=al(E.location,D,m);s=d();let v=Bs(c,s),x=E.createHref(c);o.replaceState(v,"",x),i&&a&&a({action:u,location:E.location,delta:0})}function k(D){let m=l.location.origin!=="null"?l.location.origin:l.location.href,c=typeof D=="string"?D:Nn(D);return c=c.replace(/ $/,"%20"),Q(m,"No window.location.(origin|href) available to create URL for href: "+c),new URL(c,m)}let E={get action(){return u},get location(){return e(l,o)},listen(D){if(a)throw new Error("A history only accepts one active listener");return l.addEventListener(As,f),a=D,()=>{l.removeEventListener(As,f),a=null}},createHref(D){return t(l,D)},createURL:k,encodeLocation(D){let m=k(D);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:h,replace:w,go(D){return o.go(D)}};return E}var Z;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Z||(Z={}));const Lm=new Set(["lazy","caseSensitive","path","id","index","children"]);function Tm(e){return e.index===!0}function Pi(e,t,n,r){return n===void 0&&(n=[]),r===void 0&&(r={}),e.map((l,i)=>{let o=[...n,String(i)],u=typeof l.id=="string"?l.id:o.join("-");if(Q(l.index!==!0||!l.children,"Cannot specify children on an index route"),Q(!r[u],'Found a route id collision on id "'+u+`".  Route id's must be globally unique within Data Router usages`),Tm(l)){let a=ue({},l,t(l),{id:u});return r[u]=a,a}else{let a=ue({},l,t(l),{id:u,children:void 0});return r[u]=a,l.children&&(a.children=Pi(l.children,t,o,r)),a}})}function yn(e,t,n){return n===void 0&&(n="/"),ei(e,t,n,!1)}function ei(e,t,n,r){let l=typeof t=="string"?sn(t):t,i=hr(l.pathname||"/",n);if(i==null)return null;let o=kd(e);Dm(o);let u=null;for(let a=0;u==null&&a<o.length;++a){let s=Vm(i);u=Bm(o[a],s,r)}return u}function Nm(e,t){let{route:n,pathname:r,params:l}=e;return{id:n.id,pathname:r,params:l,data:t[n.id],handle:n.handle}}function kd(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let l=(i,o,u)=>{let a={relativePath:u===void 0?i.path||"":u,caseSensitive:i.caseSensitive===!0,childrenIndex:o,route:i};a.relativePath.startsWith("/")&&(Q(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let s=Nt([r,a.relativePath]),d=n.concat(a);i.children&&i.children.length>0&&(Q(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+s+'".')),kd(i.children,t,d,s)),!(i.path==null&&!i.index)&&t.push({path:s,score:Um(s,i.index),routesMeta:d})};return e.forEach((i,o)=>{var u;if(i.path===""||!((u=i.path)!=null&&u.includes("?")))l(i,o);else for(let a of xd(i.path))l(i,o,a)}),t}function xd(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,l=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return l?[i,""]:[i];let o=xd(r.join("/")),u=[];return u.push(...o.map(a=>a===""?i:[i,a].join("/"))),l&&u.push(...o),u.map(a=>e.startsWith("/")&&a===""?"/":a)}function Dm(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:Am(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const Mm=/^:[\w-]+$/,zm=3,Om=2,Fm=1,Im=10,jm=-2,$s=e=>e==="*";function Um(e,t){let n=e.split("/"),r=n.length;return n.some($s)&&(r+=jm),t&&(r+=Om),n.filter(l=>!$s(l)).reduce((l,i)=>l+(Mm.test(i)?zm:i===""?Fm:Im),r)}function Am(e,t){return e.length===t.length&&e.slice(0,-1).every((r,l)=>r===t[l])?e[e.length-1]-t[t.length-1]:0}function Bm(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,l={},i="/",o=[];for(let u=0;u<r.length;++u){let a=r[u],s=u===r.length-1,d=i==="/"?t:t.slice(i.length)||"/",f=Vs({path:a.relativePath,caseSensitive:a.caseSensitive,end:s},d),h=a.route;if(!f&&s&&n&&!r[r.length-1].route.index&&(f=Vs({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},d)),!f)return null;Object.assign(l,f.params),o.push({params:l,pathname:Nt([i,f.pathname]),pathnameBase:Qm(Nt([i,f.pathnameBase])),route:h}),f.pathnameBase!=="/"&&(i=Nt([i,f.pathnameBase]))}return o}function Vs(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=$m(e.path,e.caseSensitive,e.end),l=t.match(n);if(!l)return null;let i=l[0],o=i.replace(/(.)\/+$/,"$1"),u=l.slice(1);return{params:r.reduce((s,d,f)=>{let{paramName:h,isOptional:w}=d;if(h==="*"){let E=u[f]||"";o=i.slice(0,i.length-E.length).replace(/(.)\/+$/,"$1")}const k=u[f];return w&&!k?s[h]=void 0:s[h]=(k||"").replace(/%2F/g,"/"),s},{}),pathname:i,pathnameBase:o,pattern:e}}function $m(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),cr(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,u,a)=>(r.push({paramName:u,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),r]}function Vm(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return cr(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function hr(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Hm(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:l=""}=typeof e=="string"?sn(e):e;return{pathname:n?n.startsWith("/")?n:Wm(n,t):t,search:Km(r),hash:Ym(l)}}function Wm(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?n.length>1&&n.pop():l!=="."&&n.push(l)}),n.length>1?n.join("/"):"/"}function xo(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Cd(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Hi(e,t){let n=Cd(e);return t?n.map((r,l)=>l===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function Wi(e,t,n,r){r===void 0&&(r=!1);let l;typeof e=="string"?l=sn(e):(l=ue({},e),Q(!l.pathname||!l.pathname.includes("?"),xo("?","pathname","search",l)),Q(!l.pathname||!l.pathname.includes("#"),xo("#","pathname","hash",l)),Q(!l.search||!l.search.includes("#"),xo("#","search","hash",l)));let i=e===""||l.pathname==="",o=i?"/":l.pathname,u;if(o==null)u=n;else{let f=t.length-1;if(!r&&o.startsWith("..")){let h=o.split("/");for(;h[0]==="..";)h.shift(),f-=1;l.pathname=h.join("/")}u=f>=0?t[f]:"/"}let a=Hm(l,u),s=o&&o!=="/"&&o.endsWith("/"),d=(i||o===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(s||d)&&(a.pathname+="/"),a}const Nt=e=>e.join("/").replace(/\/\/+/g,"/"),Qm=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Km=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ym=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;class _i{constructor(t,n,r,l){l===void 0&&(l=!1),this.status=t,this.statusText=n||"",this.internal=l,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function sl(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Pd=["post","put","patch","delete"],Xm=new Set(Pd),Gm=["get",...Pd],Jm=new Set(Gm),Zm=new Set([301,302,303,307,308]),qm=new Set([307,308]),Co={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},bm={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},Lr={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},ma=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ev=e=>({hasErrorBoundary:!!e.hasErrorBoundary}),_d="remix-router-transitions";function tv(e){const t=e.window?e.window:typeof window<"u"?window:void 0,n=typeof t<"u"&&typeof t.document<"u"&&typeof t.document.createElement<"u",r=!n;Q(e.routes.length>0,"You must provide a non-empty routes array to createRouter");let l;if(e.mapRouteProperties)l=e.mapRouteProperties;else if(e.detectErrorBoundary){let p=e.detectErrorBoundary;l=g=>({hasErrorBoundary:p(g)})}else l=ev;let i={},o=Pi(e.routes,l,void 0,i),u,a=e.basename||"/",s=e.dataStrategy||iv,d=e.patchRoutesOnNavigation,f=ue({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),h=null,w=new Set,k=null,E=null,D=null,m=e.hydrationData!=null,c=yn(o,e.history.location,a),v=!1,x=null;if(c==null&&!d){let p=Be(404,{pathname:e.history.location.pathname}),{matches:g,route:S}=bs(o);c=g,x={[S.id]:p}}c&&!e.hydrationData&&Sl(c,o,e.history.location.pathname).active&&(c=null);let L;if(c)if(c.some(p=>p.route.lazy))L=!1;else if(!c.some(p=>p.route.loader))L=!0;else if(f.v7_partialHydration){let p=e.hydrationData?e.hydrationData.loaderData:null,g=e.hydrationData?e.hydrationData.errors:null;if(g){let S=c.findIndex(C=>g[C.route.id]!==void 0);L=c.slice(0,S+1).every(C=>!wu(C.route,p,g))}else L=c.every(S=>!wu(S.route,p,g))}else L=e.hydrationData!=null;else if(L=!1,c=[],f.v7_partialHydration){let p=Sl(null,o,e.history.location.pathname);p.active&&p.matches&&(v=!0,c=p.matches)}let O,y={historyAction:e.history.action,location:e.history.location,matches:c,initialized:L,navigation:Co,restoreScrollPosition:e.hydrationData!=null?!1:null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||x,fetchers:new Map,blockers:new Map},P=ye.Pop,B=!1,M,b=!1,ne=new Map,we=null,Ce=!1,ut=!1,It=[],jt=new Set,T=new Map,$=0,H=-1,ee=new Map,te=new Set,at=new Map,Ke=new Map,ze=new Set,Oe=new Map,et=new Map,yl;function Ad(){if(h=e.history.listen(p=>{let{action:g,location:S,delta:C}=p;if(yl){yl(),yl=void 0;return}cr(et.size===0||C!=null,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let N=Ra({currentLocation:y.location,nextLocation:S,historyAction:g});if(N&&C!=null){let A=new Promise(V=>{yl=V});e.history.go(C*-1),wl(N,{state:"blocked",location:S,proceed(){wl(N,{state:"proceeding",proceed:void 0,reset:void 0,location:S}),A.then(()=>e.history.go(C))},reset(){let V=new Map(y.blockers);V.set(N,Lr),Fe({blockers:V})}});return}return dn(g,S)}),n){wv(t,ne);let p=()=>Sv(t,ne);t.addEventListener("pagehide",p),we=()=>t.removeEventListener("pagehide",p)}return y.initialized||dn(ye.Pop,y.location,{initialHydration:!0}),O}function Bd(){h&&h(),we&&we(),w.clear(),M&&M.abort(),y.fetchers.forEach((p,g)=>gl(g)),y.blockers.forEach((p,g)=>_a(g))}function $d(p){return w.add(p),()=>w.delete(p)}function Fe(p,g){g===void 0&&(g={}),y=ue({},y,p);let S=[],C=[];f.v7_fetcherPersist&&y.fetchers.forEach((N,A)=>{N.state==="idle"&&(ze.has(A)?C.push(A):S.push(A))}),ze.forEach(N=>{!y.fetchers.has(N)&&!T.has(N)&&C.push(N)}),[...w].forEach(N=>N(y,{deletedFetchers:C,viewTransitionOpts:g.viewTransitionOpts,flushSync:g.flushSync===!0})),f.v7_fetcherPersist?(S.forEach(N=>y.fetchers.delete(N)),C.forEach(N=>gl(N))):C.forEach(N=>ze.delete(N))}function zn(p,g,S){var C,N;let{flushSync:A}=S===void 0?{}:S,V=y.actionData!=null&&y.navigation.formMethod!=null&&dt(y.navigation.formMethod)&&y.navigation.state==="loading"&&((C=p.state)==null?void 0:C._isRedirect)!==!0,I;g.actionData?Object.keys(g.actionData).length>0?I=g.actionData:I=null:V?I=y.actionData:I=null;let j=g.loaderData?Zs(y.loaderData,g.loaderData,g.matches||[],g.errors):y.loaderData,F=y.blockers;F.size>0&&(F=new Map(F),F.forEach((Y,Pe)=>F.set(Pe,Lr)));let U=B===!0||y.navigation.formMethod!=null&&dt(y.navigation.formMethod)&&((N=p.state)==null?void 0:N._isRedirect)!==!0;u&&(o=u,u=void 0),Ce||P===ye.Pop||(P===ye.Push?e.history.push(p,p.state):P===ye.Replace&&e.history.replace(p,p.state));let W;if(P===ye.Pop){let Y=ne.get(y.location.pathname);Y&&Y.has(p.pathname)?W={currentLocation:y.location,nextLocation:p}:ne.has(p.pathname)&&(W={currentLocation:p,nextLocation:y.location})}else if(b){let Y=ne.get(y.location.pathname);Y?Y.add(p.pathname):(Y=new Set([p.pathname]),ne.set(y.location.pathname,Y)),W={currentLocation:y.location,nextLocation:p}}Fe(ue({},g,{actionData:I,loaderData:j,historyAction:P,location:p,initialized:!0,navigation:Co,revalidation:"idle",restoreScrollPosition:Ta(p,g.matches||y.matches),preventScrollReset:U,blockers:F}),{viewTransitionOpts:W,flushSync:A===!0}),P=ye.Pop,B=!1,b=!1,Ce=!1,ut=!1,It=[]}async function wa(p,g){if(typeof p=="number"){e.history.go(p);return}let S=gu(y.location,y.matches,a,f.v7_prependBasename,p,f.v7_relativeSplatPath,g==null?void 0:g.fromRouteId,g==null?void 0:g.relative),{path:C,submission:N,error:A}=Hs(f.v7_normalizeFormMethod,!1,S,g),V=y.location,I=al(y.location,C,g&&g.state);I=ue({},I,e.history.encodeLocation(I));let j=g&&g.replace!=null?g.replace:void 0,F=ye.Push;j===!0?F=ye.Replace:j===!1||N!=null&&dt(N.formMethod)&&N.formAction===y.location.pathname+y.location.search&&(F=ye.Replace);let U=g&&"preventScrollReset"in g?g.preventScrollReset===!0:void 0,W=(g&&g.flushSync)===!0,Y=Ra({currentLocation:V,nextLocation:I,historyAction:F});if(Y){wl(Y,{state:"blocked",location:I,proceed(){wl(Y,{state:"proceeding",proceed:void 0,reset:void 0,location:I}),wa(p,g)},reset(){let Pe=new Map(y.blockers);Pe.set(Y,Lr),Fe({blockers:Pe})}});return}return await dn(F,I,{submission:N,pendingError:A,preventScrollReset:U,replace:g&&g.replace,enableViewTransition:g&&g.viewTransition,flushSync:W})}function Vd(){if(Ki(),Fe({revalidation:"loading"}),y.navigation.state!=="submitting"){if(y.navigation.state==="idle"){dn(y.historyAction,y.location,{startUninterruptedRevalidation:!0});return}dn(P||y.historyAction,y.navigation.location,{overrideNavigation:y.navigation,enableViewTransition:b===!0})}}async function dn(p,g,S){M&&M.abort(),M=null,P=p,Ce=(S&&S.startUninterruptedRevalidation)===!0,qd(y.location,y.matches),B=(S&&S.preventScrollReset)===!0,b=(S&&S.enableViewTransition)===!0;let C=u||o,N=S&&S.overrideNavigation,A=S!=null&&S.initialHydration&&y.matches&&y.matches.length>0&&!v?y.matches:yn(C,g,a),V=(S&&S.flushSync)===!0;if(A&&y.initialized&&!ut&&fv(y.location,g)&&!(S&&S.submission&&dt(S.submission.formMethod))){zn(g,{matches:A},{flushSync:V});return}let I=Sl(A,C,g.pathname);if(I.active&&I.matches&&(A=I.matches),!A){let{error:re,notFoundMatches:J,route:de}=Yi(g.pathname);zn(g,{matches:J,loaderData:{},errors:{[de.id]:re}},{flushSync:V});return}M=new AbortController;let j=Un(e.history,g,M.signal,S&&S.submission),F;if(S&&S.pendingError)F=[gn(A).route.id,{type:Z.error,error:S.pendingError}];else if(S&&S.submission&&dt(S.submission.formMethod)){let re=await Hd(j,g,S.submission,A,I.active,{replace:S.replace,flushSync:V});if(re.shortCircuited)return;if(re.pendingActionResult){let[J,de]=re.pendingActionResult;if(Xe(de)&&sl(de.error)&&de.error.status===404){M=null,zn(g,{matches:re.matches,loaderData:{},errors:{[J]:de.error}});return}}A=re.matches||A,F=re.pendingActionResult,N=Po(g,S.submission),V=!1,I.active=!1,j=Un(e.history,j.url,j.signal)}let{shortCircuited:U,matches:W,loaderData:Y,errors:Pe}=await Wd(j,g,A,I.active,N,S&&S.submission,S&&S.fetcherSubmission,S&&S.replace,S&&S.initialHydration===!0,V,F);U||(M=null,zn(g,ue({matches:W||A},qs(F),{loaderData:Y,errors:Pe})))}async function Hd(p,g,S,C,N,A){A===void 0&&(A={}),Ki();let V=yv(g,S);if(Fe({navigation:V},{flushSync:A.flushSync===!0}),N){let F=await El(C,g.pathname,p.signal);if(F.type==="aborted")return{shortCircuited:!0};if(F.type==="error"){let U=gn(F.partialMatches).route.id;return{matches:F.partialMatches,pendingActionResult:[U,{type:Z.error,error:F.error}]}}else if(F.matches)C=F.matches;else{let{notFoundMatches:U,error:W,route:Y}=Yi(g.pathname);return{matches:U,pendingActionResult:[Y.id,{type:Z.error,error:W}]}}}let I,j=Fr(C,g);if(!j.route.action&&!j.route.lazy)I={type:Z.error,error:Be(405,{method:p.method,pathname:g.pathname,routeId:j.route.id})};else if(I=(await vr("action",y,p,[j],C,null))[j.route.id],p.signal.aborted)return{shortCircuited:!0};if(kn(I)){let F;return A&&A.replace!=null?F=A.replace:F=Xs(I.response.headers.get("Location"),new URL(p.url),a)===y.location.pathname+y.location.search,await pn(p,I,!0,{submission:S,replace:F}),{shortCircuited:!0}}if(Xt(I))throw Be(400,{type:"defer-action"});if(Xe(I)){let F=gn(C,j.route.id);return(A&&A.replace)!==!0&&(P=ye.Push),{matches:C,pendingActionResult:[F.route.id,I]}}return{matches:C,pendingActionResult:[j.route.id,I]}}async function Wd(p,g,S,C,N,A,V,I,j,F,U){let W=N||Po(g,A),Y=A||V||tc(W),Pe=!Ce&&(!f.v7_partialHydration||!j);if(C){if(Pe){let pe=Sa(U);Fe(ue({navigation:W},pe!==void 0?{actionData:pe}:{}),{flushSync:F})}let G=await El(S,g.pathname,p.signal);if(G.type==="aborted")return{shortCircuited:!0};if(G.type==="error"){let pe=gn(G.partialMatches).route.id;return{matches:G.partialMatches,loaderData:{},errors:{[pe]:G.error}}}else if(G.matches)S=G.matches;else{let{error:pe,notFoundMatches:Fn,route:wr}=Yi(g.pathname);return{matches:Fn,loaderData:{},errors:{[wr.id]:pe}}}}let re=u||o,[J,de]=Qs(e.history,y,S,Y,g,f.v7_partialHydration&&j===!0,f.v7_skipActionErrorRevalidation,ut,It,jt,ze,at,te,re,a,U);if(Xi(G=>!(S&&S.some(pe=>pe.route.id===G))||J&&J.some(pe=>pe.route.id===G)),H=++$,J.length===0&&de.length===0){let G=Ca();return zn(g,ue({matches:S,loaderData:{},errors:U&&Xe(U[1])?{[U[0]]:U[1].error}:null},qs(U),G?{fetchers:new Map(y.fetchers)}:{}),{flushSync:F}),{shortCircuited:!0}}if(Pe){let G={};if(!C){G.navigation=W;let pe=Sa(U);pe!==void 0&&(G.actionData=pe)}de.length>0&&(G.fetchers=Qd(de)),Fe(G,{flushSync:F})}de.forEach(G=>{At(G.key),G.controller&&T.set(G.key,G.controller)});let On=()=>de.forEach(G=>At(G.key));M&&M.signal.addEventListener("abort",On);let{loaderResults:yr,fetcherResults:Ct}=await Ea(y,S,J,de,p);if(p.signal.aborted)return{shortCircuited:!0};M&&M.signal.removeEventListener("abort",On),de.forEach(G=>T.delete(G.key));let yt=$l(yr);if(yt)return await pn(p,yt.result,!0,{replace:I}),{shortCircuited:!0};if(yt=$l(Ct),yt)return te.add(yt.key),await pn(p,yt.result,!0,{replace:I}),{shortCircuited:!0};let{loaderData:Gi,errors:gr}=Js(y,S,yr,U,de,Ct,Oe);Oe.forEach((G,pe)=>{G.subscribe(Fn=>{(Fn||G.done)&&Oe.delete(pe)})}),f.v7_partialHydration&&j&&y.errors&&(gr=ue({},y.errors,gr));let hn=Ca(),kl=Pa(H),xl=hn||kl||de.length>0;return ue({matches:S,loaderData:Gi,errors:gr},xl?{fetchers:new Map(y.fetchers)}:{})}function Sa(p){if(p&&!Xe(p[1]))return{[p[0]]:p[1].data};if(y.actionData)return Object.keys(y.actionData).length===0?null:y.actionData}function Qd(p){return p.forEach(g=>{let S=y.fetchers.get(g.key),C=Tr(void 0,S?S.data:void 0);y.fetchers.set(g.key,C)}),new Map(y.fetchers)}function Kd(p,g,S,C){if(r)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");At(p);let N=(C&&C.flushSync)===!0,A=u||o,V=gu(y.location,y.matches,a,f.v7_prependBasename,S,f.v7_relativeSplatPath,g,C==null?void 0:C.relative),I=yn(A,V,a),j=Sl(I,A,V);if(j.active&&j.matches&&(I=j.matches),!I){xt(p,g,Be(404,{pathname:V}),{flushSync:N});return}let{path:F,submission:U,error:W}=Hs(f.v7_normalizeFormMethod,!0,V,C);if(W){xt(p,g,W,{flushSync:N});return}let Y=Fr(I,F),Pe=(C&&C.preventScrollReset)===!0;if(U&&dt(U.formMethod)){Yd(p,g,F,Y,I,j.active,N,Pe,U);return}at.set(p,{routeId:g,path:F}),Xd(p,g,F,Y,I,j.active,N,Pe,U)}async function Yd(p,g,S,C,N,A,V,I,j){Ki(),at.delete(p);function F(ve){if(!ve.route.action&&!ve.route.lazy){let In=Be(405,{method:j.formMethod,pathname:S,routeId:g});return xt(p,g,In,{flushSync:V}),!0}return!1}if(!A&&F(C))return;let U=y.fetchers.get(p);Ut(p,gv(j,U),{flushSync:V});let W=new AbortController,Y=Un(e.history,S,W.signal,j);if(A){let ve=await El(N,new URL(Y.url).pathname,Y.signal,p);if(ve.type==="aborted")return;if(ve.type==="error"){xt(p,g,ve.error,{flushSync:V});return}else if(ve.matches){if(N=ve.matches,C=Fr(N,S),F(C))return}else{xt(p,g,Be(404,{pathname:S}),{flushSync:V});return}}T.set(p,W);let Pe=$,J=(await vr("action",y,Y,[C],N,p))[C.route.id];if(Y.signal.aborted){T.get(p)===W&&T.delete(p);return}if(f.v7_fetcherPersist&&ze.has(p)){if(kn(J)||Xe(J)){Ut(p,$t(void 0));return}}else{if(kn(J))if(T.delete(p),H>Pe){Ut(p,$t(void 0));return}else return te.add(p),Ut(p,Tr(j)),pn(Y,J,!1,{fetcherSubmission:j,preventScrollReset:I});if(Xe(J)){xt(p,g,J.error);return}}if(Xt(J))throw Be(400,{type:"defer-action"});let de=y.navigation.location||y.location,On=Un(e.history,de,W.signal),yr=u||o,Ct=y.navigation.state!=="idle"?yn(yr,y.navigation.location,a):y.matches;Q(Ct,"Didn't find any matches after fetcher action");let yt=++$;ee.set(p,yt);let Gi=Tr(j,J.data);y.fetchers.set(p,Gi);let[gr,hn]=Qs(e.history,y,Ct,j,de,!1,f.v7_skipActionErrorRevalidation,ut,It,jt,ze,at,te,yr,a,[C.route.id,J]);hn.filter(ve=>ve.key!==p).forEach(ve=>{let In=ve.key,Na=y.fetchers.get(In),tp=Tr(void 0,Na?Na.data:void 0);y.fetchers.set(In,tp),At(In),ve.controller&&T.set(In,ve.controller)}),Fe({fetchers:new Map(y.fetchers)});let kl=()=>hn.forEach(ve=>At(ve.key));W.signal.addEventListener("abort",kl);let{loaderResults:xl,fetcherResults:G}=await Ea(y,Ct,gr,hn,On);if(W.signal.aborted)return;W.signal.removeEventListener("abort",kl),ee.delete(p),T.delete(p),hn.forEach(ve=>T.delete(ve.key));let pe=$l(xl);if(pe)return pn(On,pe.result,!1,{preventScrollReset:I});if(pe=$l(G),pe)return te.add(pe.key),pn(On,pe.result,!1,{preventScrollReset:I});let{loaderData:Fn,errors:wr}=Js(y,Ct,xl,void 0,hn,G,Oe);if(y.fetchers.has(p)){let ve=$t(J.data);y.fetchers.set(p,ve)}Pa(yt),y.navigation.state==="loading"&&yt>H?(Q(P,"Expected pending action"),M&&M.abort(),zn(y.navigation.location,{matches:Ct,loaderData:Fn,errors:wr,fetchers:new Map(y.fetchers)})):(Fe({errors:wr,loaderData:Zs(y.loaderData,Fn,Ct,wr),fetchers:new Map(y.fetchers)}),ut=!1)}async function Xd(p,g,S,C,N,A,V,I,j){let F=y.fetchers.get(p);Ut(p,Tr(j,F?F.data:void 0),{flushSync:V});let U=new AbortController,W=Un(e.history,S,U.signal);if(A){let J=await El(N,new URL(W.url).pathname,W.signal,p);if(J.type==="aborted")return;if(J.type==="error"){xt(p,g,J.error,{flushSync:V});return}else if(J.matches)N=J.matches,C=Fr(N,S);else{xt(p,g,Be(404,{pathname:S}),{flushSync:V});return}}T.set(p,U);let Y=$,re=(await vr("loader",y,W,[C],N,p))[C.route.id];if(Xt(re)&&(re=await va(re,W.signal,!0)||re),T.get(p)===U&&T.delete(p),!W.signal.aborted){if(ze.has(p)){Ut(p,$t(void 0));return}if(kn(re))if(H>Y){Ut(p,$t(void 0));return}else{te.add(p),await pn(W,re,!1,{preventScrollReset:I});return}if(Xe(re)){xt(p,g,re.error);return}Q(!Xt(re),"Unhandled fetcher deferred data"),Ut(p,$t(re.data))}}async function pn(p,g,S,C){let{submission:N,fetcherSubmission:A,preventScrollReset:V,replace:I}=C===void 0?{}:C;g.response.headers.has("X-Remix-Revalidate")&&(ut=!0);let j=g.response.headers.get("Location");Q(j,"Expected a Location header on the redirect Response"),j=Xs(j,new URL(p.url),a);let F=al(y.location,j,{_isRedirect:!0});if(n){let J=!1;if(g.response.headers.has("X-Remix-Reload-Document"))J=!0;else if(ma.test(j)){const de=e.history.createURL(j);J=de.origin!==t.location.origin||hr(de.pathname,a)==null}if(J){I?t.location.replace(j):t.location.assign(j);return}}M=null;let U=I===!0||g.response.headers.has("X-Remix-Replace")?ye.Replace:ye.Push,{formMethod:W,formAction:Y,formEncType:Pe}=y.navigation;!N&&!A&&W&&Y&&Pe&&(N=tc(y.navigation));let re=N||A;if(qm.has(g.response.status)&&re&&dt(re.formMethod))await dn(U,F,{submission:ue({},re,{formAction:j}),preventScrollReset:V||B,enableViewTransition:S?b:void 0});else{let J=Po(F,N);await dn(U,F,{overrideNavigation:J,fetcherSubmission:A,preventScrollReset:V||B,enableViewTransition:S?b:void 0})}}async function vr(p,g,S,C,N,A){let V,I={};try{V=await ov(s,p,g,S,C,N,A,i,l)}catch(j){return C.forEach(F=>{I[F.route.id]={type:Z.error,error:j}}),I}for(let[j,F]of Object.entries(V))if(dv(F)){let U=F.result;I[j]={type:Z.redirect,response:sv(U,S,j,N,a,f.v7_relativeSplatPath)}}else I[j]=await av(F);return I}async function Ea(p,g,S,C,N){let A=p.matches,V=vr("loader",p,N,S,g,null),I=Promise.all(C.map(async U=>{if(U.matches&&U.match&&U.controller){let Y=(await vr("loader",p,Un(e.history,U.path,U.controller.signal),[U.match],U.matches,U.key))[U.match.route.id];return{[U.key]:Y}}else return Promise.resolve({[U.key]:{type:Z.error,error:Be(404,{pathname:U.path})}})})),j=await V,F=(await I).reduce((U,W)=>Object.assign(U,W),{});return await Promise.all([mv(g,j,N.signal,A,p.loaderData),vv(g,F,C)]),{loaderResults:j,fetcherResults:F}}function Ki(){ut=!0,It.push(...Xi()),at.forEach((p,g)=>{T.has(g)&&jt.add(g),At(g)})}function Ut(p,g,S){S===void 0&&(S={}),y.fetchers.set(p,g),Fe({fetchers:new Map(y.fetchers)},{flushSync:(S&&S.flushSync)===!0})}function xt(p,g,S,C){C===void 0&&(C={});let N=gn(y.matches,g);gl(p),Fe({errors:{[N.route.id]:S},fetchers:new Map(y.fetchers)},{flushSync:(C&&C.flushSync)===!0})}function ka(p){return Ke.set(p,(Ke.get(p)||0)+1),ze.has(p)&&ze.delete(p),y.fetchers.get(p)||bm}function gl(p){let g=y.fetchers.get(p);T.has(p)&&!(g&&g.state==="loading"&&ee.has(p))&&At(p),at.delete(p),ee.delete(p),te.delete(p),f.v7_fetcherPersist&&ze.delete(p),jt.delete(p),y.fetchers.delete(p)}function Gd(p){let g=(Ke.get(p)||0)-1;g<=0?(Ke.delete(p),ze.add(p),f.v7_fetcherPersist||gl(p)):Ke.set(p,g),Fe({fetchers:new Map(y.fetchers)})}function At(p){let g=T.get(p);g&&(g.abort(),T.delete(p))}function xa(p){for(let g of p){let S=ka(g),C=$t(S.data);y.fetchers.set(g,C)}}function Ca(){let p=[],g=!1;for(let S of te){let C=y.fetchers.get(S);Q(C,"Expected fetcher: "+S),C.state==="loading"&&(te.delete(S),p.push(S),g=!0)}return xa(p),g}function Pa(p){let g=[];for(let[S,C]of ee)if(C<p){let N=y.fetchers.get(S);Q(N,"Expected fetcher: "+S),N.state==="loading"&&(At(S),ee.delete(S),g.push(S))}return xa(g),g.length>0}function Jd(p,g){let S=y.blockers.get(p)||Lr;return et.get(p)!==g&&et.set(p,g),S}function _a(p){y.blockers.delete(p),et.delete(p)}function wl(p,g){let S=y.blockers.get(p)||Lr;Q(S.state==="unblocked"&&g.state==="blocked"||S.state==="blocked"&&g.state==="blocked"||S.state==="blocked"&&g.state==="proceeding"||S.state==="blocked"&&g.state==="unblocked"||S.state==="proceeding"&&g.state==="unblocked","Invalid blocker state transition: "+S.state+" -> "+g.state);let C=new Map(y.blockers);C.set(p,g),Fe({blockers:C})}function Ra(p){let{currentLocation:g,nextLocation:S,historyAction:C}=p;if(et.size===0)return;et.size>1&&cr(!1,"A router only supports one blocker at a time");let N=Array.from(et.entries()),[A,V]=N[N.length-1],I=y.blockers.get(A);if(!(I&&I.state==="proceeding")&&V({currentLocation:g,nextLocation:S,historyAction:C}))return A}function Yi(p){let g=Be(404,{pathname:p}),S=u||o,{matches:C,route:N}=bs(S);return Xi(),{notFoundMatches:C,route:N,error:g}}function Xi(p){let g=[];return Oe.forEach((S,C)=>{(!p||p(C))&&(S.cancel(),g.push(C),Oe.delete(C))}),g}function Zd(p,g,S){if(k=p,D=g,E=S||null,!m&&y.navigation===Co){m=!0;let C=Ta(y.location,y.matches);C!=null&&Fe({restoreScrollPosition:C})}return()=>{k=null,D=null,E=null}}function La(p,g){return E&&E(p,g.map(C=>Nm(C,y.loaderData)))||p.key}function qd(p,g){if(k&&D){let S=La(p,g);k[S]=D()}}function Ta(p,g){if(k){let S=La(p,g),C=k[S];if(typeof C=="number")return C}return null}function Sl(p,g,S){if(d)if(p){if(Object.keys(p[0].params).length>0)return{active:!0,matches:ei(g,S,a,!0)}}else return{active:!0,matches:ei(g,S,a,!0)||[]};return{active:!1,matches:null}}async function El(p,g,S,C){if(!d)return{type:"success",matches:p};let N=p;for(;;){let A=u==null,V=u||o,I=i;try{await d({signal:S,path:g,matches:N,fetcherKey:C,patch:(U,W)=>{S.aborted||Ys(U,W,V,I,l)}})}catch(U){return{type:"error",error:U,partialMatches:N}}finally{A&&!S.aborted&&(o=[...o])}if(S.aborted)return{type:"aborted"};let j=yn(V,g,a);if(j)return{type:"success",matches:j};let F=ei(V,g,a,!0);if(!F||N.length===F.length&&N.every((U,W)=>U.route.id===F[W].route.id))return{type:"success",matches:null};N=F}}function bd(p){i={},u=Pi(p,l,void 0,i)}function ep(p,g){let S=u==null;Ys(p,g,u||o,i,l),S&&(o=[...o],Fe({}))}return O={get basename(){return a},get future(){return f},get state(){return y},get routes(){return o},get window(){return t},initialize:Ad,subscribe:$d,enableScrollRestoration:Zd,navigate:wa,fetch:Kd,revalidate:Vd,createHref:p=>e.history.createHref(p),encodeLocation:p=>e.history.encodeLocation(p),getFetcher:ka,deleteFetcher:Gd,dispose:Bd,getBlocker:Jd,deleteBlocker:_a,patchRoutes:ep,_internalFetchControllers:T,_internalActiveDeferreds:Oe,_internalSetRoutes:bd},O}function nv(e){return e!=null&&("formData"in e&&e.formData!=null||"body"in e&&e.body!==void 0)}function gu(e,t,n,r,l,i,o,u){let a,s;if(o){a=[];for(let f of t)if(a.push(f),f.route.id===o){s=f;break}}else a=t,s=t[t.length-1];let d=Wi(l||".",Hi(a,i),hr(e.pathname,n)||e.pathname,u==="path");if(l==null&&(d.search=e.search,d.hash=e.hash),(l==null||l===""||l===".")&&s){let f=ya(d.search);if(s.route.index&&!f)d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index";else if(!s.route.index&&f){let h=new URLSearchParams(d.search),w=h.getAll("index");h.delete("index"),w.filter(E=>E).forEach(E=>h.append("index",E));let k=h.toString();d.search=k?"?"+k:""}}return r&&n!=="/"&&(d.pathname=d.pathname==="/"?n:Nt([n,d.pathname])),Nn(d)}function Hs(e,t,n,r){if(!r||!nv(r))return{path:n};if(r.formMethod&&!hv(r.formMethod))return{path:n,error:Be(405,{method:r.formMethod})};let l=()=>({path:n,error:Be(400,{type:"invalid-body"})}),i=r.formMethod||"get",o=e?i.toUpperCase():i.toLowerCase(),u=Td(n);if(r.body!==void 0){if(r.formEncType==="text/plain"){if(!dt(o))return l();let h=typeof r.body=="string"?r.body:r.body instanceof FormData||r.body instanceof URLSearchParams?Array.from(r.body.entries()).reduce((w,k)=>{let[E,D]=k;return""+w+E+"="+D+`
`},""):String(r.body);return{path:n,submission:{formMethod:o,formAction:u,formEncType:r.formEncType,formData:void 0,json:void 0,text:h}}}else if(r.formEncType==="application/json"){if(!dt(o))return l();try{let h=typeof r.body=="string"?JSON.parse(r.body):r.body;return{path:n,submission:{formMethod:o,formAction:u,formEncType:r.formEncType,formData:void 0,json:h,text:void 0}}}catch{return l()}}}Q(typeof FormData=="function","FormData is not available in this environment");let a,s;if(r.formData)a=Su(r.formData),s=r.formData;else if(r.body instanceof FormData)a=Su(r.body),s=r.body;else if(r.body instanceof URLSearchParams)a=r.body,s=Gs(a);else if(r.body==null)a=new URLSearchParams,s=new FormData;else try{a=new URLSearchParams(r.body),s=Gs(a)}catch{return l()}let d={formMethod:o,formAction:u,formEncType:r&&r.formEncType||"application/x-www-form-urlencoded",formData:s,json:void 0,text:void 0};if(dt(d.formMethod))return{path:n,submission:d};let f=sn(n);return t&&f.search&&ya(f.search)&&a.append("index",""),f.search="?"+a,{path:Nn(f),submission:d}}function Ws(e,t,n){n===void 0&&(n=!1);let r=e.findIndex(l=>l.route.id===t);return r>=0?e.slice(0,n?r+1:r):e}function Qs(e,t,n,r,l,i,o,u,a,s,d,f,h,w,k,E){let D=E?Xe(E[1])?E[1].error:E[1].data:void 0,m=e.createURL(t.location),c=e.createURL(l),v=n;i&&t.errors?v=Ws(n,Object.keys(t.errors)[0],!0):E&&Xe(E[1])&&(v=Ws(n,E[0]));let x=E?E[1].statusCode:void 0,L=o&&x&&x>=400,O=v.filter((P,B)=>{let{route:M}=P;if(M.lazy)return!0;if(M.loader==null)return!1;if(i)return wu(M,t.loaderData,t.errors);if(rv(t.loaderData,t.matches[B],P)||a.some(we=>we===P.route.id))return!0;let b=t.matches[B],ne=P;return Ks(P,ue({currentUrl:m,currentParams:b.params,nextUrl:c,nextParams:ne.params},r,{actionResult:D,actionStatus:x,defaultShouldRevalidate:L?!1:u||m.pathname+m.search===c.pathname+c.search||m.search!==c.search||Rd(b,ne)}))}),y=[];return f.forEach((P,B)=>{if(i||!n.some(Ce=>Ce.route.id===P.routeId)||d.has(B))return;let M=yn(w,P.path,k);if(!M){y.push({key:B,routeId:P.routeId,path:P.path,matches:null,match:null,controller:null});return}let b=t.fetchers.get(B),ne=Fr(M,P.path),we=!1;h.has(B)?we=!1:s.has(B)?(s.delete(B),we=!0):b&&b.state!=="idle"&&b.data===void 0?we=u:we=Ks(ne,ue({currentUrl:m,currentParams:t.matches[t.matches.length-1].params,nextUrl:c,nextParams:n[n.length-1].params},r,{actionResult:D,actionStatus:x,defaultShouldRevalidate:L?!1:u})),we&&y.push({key:B,routeId:P.routeId,path:P.path,matches:M,match:ne,controller:new AbortController})}),[O,y]}function wu(e,t,n){if(e.lazy)return!0;if(!e.loader)return!1;let r=t!=null&&t[e.id]!==void 0,l=n!=null&&n[e.id]!==void 0;return!r&&l?!1:typeof e.loader=="function"&&e.loader.hydrate===!0?!0:!r&&!l}function rv(e,t,n){let r=!t||n.route.id!==t.route.id,l=e[n.route.id]===void 0;return r||l}function Rd(e,t){let n=e.route.path;return e.pathname!==t.pathname||n!=null&&n.endsWith("*")&&e.params["*"]!==t.params["*"]}function Ks(e,t){if(e.route.shouldRevalidate){let n=e.route.shouldRevalidate(t);if(typeof n=="boolean")return n}return t.defaultShouldRevalidate}function Ys(e,t,n,r,l){var i;let o;if(e){let s=r[e];Q(s,"No route found to patch children into: routeId = "+e),s.children||(s.children=[]),o=s.children}else o=n;let u=t.filter(s=>!o.some(d=>Ld(s,d))),a=Pi(u,l,[e||"_","patch",String(((i=o)==null?void 0:i.length)||"0")],r);o.push(...a)}function Ld(e,t){return"id"in e&&"id"in t&&e.id===t.id?!0:e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive?(!e.children||e.children.length===0)&&(!t.children||t.children.length===0)?!0:e.children.every((n,r)=>{var l;return(l=t.children)==null?void 0:l.some(i=>Ld(n,i))}):!1}async function lv(e,t,n){if(!e.lazy)return;let r=await e.lazy();if(!e.lazy)return;let l=n[e.id];Q(l,"No route found in manifest");let i={};for(let o in r){let a=l[o]!==void 0&&o!=="hasErrorBoundary";cr(!a,'Route "'+l.id+'" has a static property "'+o+'" defined but its lazy function is also returning a value for this property. '+('The lazy route property "'+o+'" will be ignored.')),!a&&!Lm.has(o)&&(i[o]=r[o])}Object.assign(l,i),Object.assign(l,ue({},t(l),{lazy:void 0}))}async function iv(e){let{matches:t}=e,n=t.filter(l=>l.shouldLoad);return(await Promise.all(n.map(l=>l.resolve()))).reduce((l,i,o)=>Object.assign(l,{[n[o].route.id]:i}),{})}async function ov(e,t,n,r,l,i,o,u,a,s){let d=i.map(w=>w.route.lazy?lv(w.route,a,u):void 0),f=i.map((w,k)=>{let E=d[k],D=l.some(c=>c.route.id===w.route.id);return ue({},w,{shouldLoad:D,resolve:async c=>(c&&r.method==="GET"&&(w.route.lazy||w.route.loader)&&(D=!0),D?uv(t,r,w,E,c,s):Promise.resolve({type:Z.data,result:void 0}))})}),h=await e({matches:f,request:r,params:i[0].params,fetcherKey:o,context:s});try{await Promise.all(d)}catch{}return h}async function uv(e,t,n,r,l,i){let o,u,a=s=>{let d,f=new Promise((k,E)=>d=E);u=()=>d(),t.signal.addEventListener("abort",u);let h=k=>typeof s!="function"?Promise.reject(new Error("You cannot call the handler for a route which defines a boolean "+('"'+e+'" [routeId: '+n.route.id+"]"))):s({request:t,params:n.params,context:i},...k!==void 0?[k]:[]),w=(async()=>{try{return{type:"data",result:await(l?l(E=>h(E)):h())}}catch(k){return{type:"error",result:k}}})();return Promise.race([w,f])};try{let s=n.route[e];if(r)if(s){let d,[f]=await Promise.all([a(s).catch(h=>{d=h}),r]);if(d!==void 0)throw d;o=f}else if(await r,s=n.route[e],s)o=await a(s);else if(e==="action"){let d=new URL(t.url),f=d.pathname+d.search;throw Be(405,{method:t.method,pathname:f,routeId:n.route.id})}else return{type:Z.data,result:void 0};else if(s)o=await a(s);else{let d=new URL(t.url),f=d.pathname+d.search;throw Be(404,{pathname:f})}Q(o.result!==void 0,"You defined "+(e==="action"?"an action":"a loader")+" for route "+('"'+n.route.id+"\" but didn't return anything from your `"+e+"` ")+"function. Please return a value or `null`.")}catch(s){return{type:Z.error,result:s}}finally{u&&t.signal.removeEventListener("abort",u)}return o}async function av(e){let{result:t,type:n}=e;if(Nd(t)){let f;try{let h=t.headers.get("Content-Type");h&&/\bapplication\/json\b/.test(h)?t.body==null?f=null:f=await t.json():f=await t.text()}catch(h){return{type:Z.error,error:h}}return n===Z.error?{type:Z.error,error:new _i(t.status,t.statusText,f),statusCode:t.status,headers:t.headers}:{type:Z.data,data:f,statusCode:t.status,headers:t.headers}}if(n===Z.error){if(ec(t)){var r,l;if(t.data instanceof Error){var i,o;return{type:Z.error,error:t.data,statusCode:(i=t.init)==null?void 0:i.status,headers:(o=t.init)!=null&&o.headers?new Headers(t.init.headers):void 0}}return{type:Z.error,error:new _i(((r=t.init)==null?void 0:r.status)||500,void 0,t.data),statusCode:sl(t)?t.status:void 0,headers:(l=t.init)!=null&&l.headers?new Headers(t.init.headers):void 0}}return{type:Z.error,error:t,statusCode:sl(t)?t.status:void 0}}if(pv(t)){var u,a;return{type:Z.deferred,deferredData:t,statusCode:(u=t.init)==null?void 0:u.status,headers:((a=t.init)==null?void 0:a.headers)&&new Headers(t.init.headers)}}if(ec(t)){var s,d;return{type:Z.data,data:t.data,statusCode:(s=t.init)==null?void 0:s.status,headers:(d=t.init)!=null&&d.headers?new Headers(t.init.headers):void 0}}return{type:Z.data,data:t}}function sv(e,t,n,r,l,i){let o=e.headers.get("Location");if(Q(o,"Redirects returned/thrown from loaders/actions must have a Location header"),!ma.test(o)){let u=r.slice(0,r.findIndex(a=>a.route.id===n)+1);o=gu(new URL(t.url),u,l,!0,o,i),e.headers.set("Location",o)}return e}function Xs(e,t,n){if(ma.test(e)){let r=e,l=r.startsWith("//")?new URL(t.protocol+r):new URL(r),i=hr(l.pathname,n)!=null;if(l.origin===t.origin&&i)return l.pathname+l.search+l.hash}return e}function Un(e,t,n,r){let l=e.createURL(Td(t)).toString(),i={signal:n};if(r&&dt(r.formMethod)){let{formMethod:o,formEncType:u}=r;i.method=o.toUpperCase(),u==="application/json"?(i.headers=new Headers({"Content-Type":u}),i.body=JSON.stringify(r.json)):u==="text/plain"?i.body=r.text:u==="application/x-www-form-urlencoded"&&r.formData?i.body=Su(r.formData):i.body=r.formData}return new Request(l,i)}function Su(e){let t=new URLSearchParams;for(let[n,r]of e.entries())t.append(n,typeof r=="string"?r:r.name);return t}function Gs(e){let t=new FormData;for(let[n,r]of e.entries())t.append(n,r);return t}function cv(e,t,n,r,l){let i={},o=null,u,a=!1,s={},d=n&&Xe(n[1])?n[1].error:void 0;return e.forEach(f=>{if(!(f.route.id in t))return;let h=f.route.id,w=t[h];if(Q(!kn(w),"Cannot handle redirect results in processLoaderData"),Xe(w)){let k=w.error;d!==void 0&&(k=d,d=void 0),o=o||{};{let E=gn(e,h);o[E.route.id]==null&&(o[E.route.id]=k)}i[h]=void 0,a||(a=!0,u=sl(w.error)?w.error.status:500),w.headers&&(s[h]=w.headers)}else Xt(w)?(r.set(h,w.deferredData),i[h]=w.deferredData.data,w.statusCode!=null&&w.statusCode!==200&&!a&&(u=w.statusCode),w.headers&&(s[h]=w.headers)):(i[h]=w.data,w.statusCode&&w.statusCode!==200&&!a&&(u=w.statusCode),w.headers&&(s[h]=w.headers))}),d!==void 0&&n&&(o={[n[0]]:d},i[n[0]]=void 0),{loaderData:i,errors:o,statusCode:u||200,loaderHeaders:s}}function Js(e,t,n,r,l,i,o){let{loaderData:u,errors:a}=cv(t,n,r,o);return l.forEach(s=>{let{key:d,match:f,controller:h}=s,w=i[d];if(Q(w,"Did not find corresponding fetcher result"),!(h&&h.signal.aborted))if(Xe(w)){let k=gn(e.matches,f==null?void 0:f.route.id);a&&a[k.route.id]||(a=ue({},a,{[k.route.id]:w.error})),e.fetchers.delete(d)}else if(kn(w))Q(!1,"Unhandled fetcher revalidation redirect");else if(Xt(w))Q(!1,"Unhandled fetcher deferred data");else{let k=$t(w.data);e.fetchers.set(d,k)}}),{loaderData:u,errors:a}}function Zs(e,t,n,r){let l=ue({},t);for(let i of n){let o=i.route.id;if(t.hasOwnProperty(o)?t[o]!==void 0&&(l[o]=t[o]):e[o]!==void 0&&i.route.loader&&(l[o]=e[o]),r&&r.hasOwnProperty(o))break}return l}function qs(e){return e?Xe(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function gn(e,t){return(t?e.slice(0,e.findIndex(r=>r.route.id===t)+1):[...e]).reverse().find(r=>r.route.hasErrorBoundary===!0)||e[0]}function bs(e){let t=e.length===1?e[0]:e.find(n=>n.index||!n.path||n.path==="/")||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Be(e,t){let{pathname:n,routeId:r,method:l,type:i,message:o}=t===void 0?{}:t,u="Unknown Server Error",a="Unknown @remix-run/router error";return e===400?(u="Bad Request",l&&n&&r?a="You made a "+l+' request to "'+n+'" but '+('did not provide a `loader` for route "'+r+'", ')+"so there is no way to handle the request.":i==="defer-action"?a="defer() is not supported in actions":i==="invalid-body"&&(a="Unable to encode submission body")):e===403?(u="Forbidden",a='Route "'+r+'" does not match URL "'+n+'"'):e===404?(u="Not Found",a='No route matches URL "'+n+'"'):e===405&&(u="Method Not Allowed",l&&n&&r?a="You made a "+l.toUpperCase()+' request to "'+n+'" but '+('did not provide an `action` for route "'+r+'", ')+"so there is no way to handle the request.":l&&(a='Invalid request method "'+l.toUpperCase()+'"')),new _i(e||500,u,new Error(a),!0)}function $l(e){let t=Object.entries(e);for(let n=t.length-1;n>=0;n--){let[r,l]=t[n];if(kn(l))return{key:r,result:l}}}function Td(e){let t=typeof e=="string"?sn(e):e;return Nn(ue({},t,{hash:""}))}function fv(e,t){return e.pathname!==t.pathname||e.search!==t.search?!1:e.hash===""?t.hash!=="":e.hash===t.hash?!0:t.hash!==""}function dv(e){return Nd(e.result)&&Zm.has(e.result.status)}function Xt(e){return e.type===Z.deferred}function Xe(e){return e.type===Z.error}function kn(e){return(e&&e.type)===Z.redirect}function ec(e){return typeof e=="object"&&e!=null&&"type"in e&&"data"in e&&"init"in e&&e.type==="DataWithResponseInit"}function pv(e){let t=e;return t&&typeof t=="object"&&typeof t.data=="object"&&typeof t.subscribe=="function"&&typeof t.cancel=="function"&&typeof t.resolveData=="function"}function Nd(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.headers=="object"&&typeof e.body<"u"}function hv(e){return Jm.has(e.toLowerCase())}function dt(e){return Xm.has(e.toLowerCase())}async function mv(e,t,n,r,l){let i=Object.entries(t);for(let o=0;o<i.length;o++){let[u,a]=i[o],s=e.find(h=>(h==null?void 0:h.route.id)===u);if(!s)continue;let d=r.find(h=>h.route.id===s.route.id),f=d!=null&&!Rd(d,s)&&(l&&l[s.route.id])!==void 0;Xt(a)&&f&&await va(a,n,!1).then(h=>{h&&(t[u]=h)})}}async function vv(e,t,n){for(let r=0;r<n.length;r++){let{key:l,routeId:i,controller:o}=n[r],u=t[l];e.find(s=>(s==null?void 0:s.route.id)===i)&&Xt(u)&&(Q(o,"Expected an AbortController for revalidating fetcher deferred result"),await va(u,o.signal,!0).then(s=>{s&&(t[l]=s)}))}}async function va(e,t,n){if(n===void 0&&(n=!1),!await e.deferredData.resolveData(t)){if(n)try{return{type:Z.data,data:e.deferredData.unwrappedData}}catch(l){return{type:Z.error,error:l}}return{type:Z.data,data:e.deferredData.data}}}function ya(e){return new URLSearchParams(e).getAll("index").some(t=>t==="")}function Fr(e,t){let n=typeof t=="string"?sn(t).search:t.search;if(e[e.length-1].route.index&&ya(n||""))return e[e.length-1];let r=Cd(e);return r[r.length-1]}function tc(e){let{formMethod:t,formAction:n,formEncType:r,text:l,formData:i,json:o}=e;if(!(!t||!n||!r)){if(l!=null)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:void 0,text:l};if(i!=null)return{formMethod:t,formAction:n,formEncType:r,formData:i,json:void 0,text:void 0};if(o!==void 0)return{formMethod:t,formAction:n,formEncType:r,formData:void 0,json:o,text:void 0}}}function Po(e,t){return t?{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}:{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function yv(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function Tr(e,t){return e?{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}:{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function gv(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}function $t(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}function wv(e,t){try{let n=e.sessionStorage.getItem(_d);if(n){let r=JSON.parse(n);for(let[l,i]of Object.entries(r||{}))i&&Array.isArray(i)&&t.set(l,new Set(i||[]))}}catch{}}function Sv(e,t){if(t.size>0){let n={};for(let[r,l]of t)n[r]=[...l];try{e.sessionStorage.setItem(_d,JSON.stringify(n))}catch(r){cr(!1,"Failed to save applied view transitions in sessionStorage ("+r+").")}}}/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ri(){return Ri=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ri.apply(this,arguments)}const Qi=R.createContext(null),Dd=R.createContext(null),cn=R.createContext(null),ga=R.createContext(null),fn=R.createContext({outlet:null,matches:[],isDataRoute:!1}),Md=R.createContext(null);function Ev(e,t){let{relative:n}=t===void 0?{}:t;mr()||Q(!1);let{basename:r,navigator:l}=R.useContext(cn),{hash:i,pathname:o,search:u}=Fd(e,{relative:n}),a=o;return r!=="/"&&(a=o==="/"?r:Nt([r,o])),l.createHref({pathname:a,search:u,hash:i})}function mr(){return R.useContext(ga)!=null}function vl(){return mr()||Q(!1),R.useContext(ga).location}function zd(e){R.useContext(cn).static||R.useLayoutEffect(e)}function Od(){let{isDataRoute:e}=R.useContext(fn);return e?zv():kv()}function kv(){mr()||Q(!1);let e=R.useContext(Qi),{basename:t,future:n,navigator:r}=R.useContext(cn),{matches:l}=R.useContext(fn),{pathname:i}=vl(),o=JSON.stringify(Hi(l,n.v7_relativeSplatPath)),u=R.useRef(!1);return zd(()=>{u.current=!0}),R.useCallback(function(s,d){if(d===void 0&&(d={}),!u.current)return;if(typeof s=="number"){r.go(s);return}let f=Wi(s,JSON.parse(o),i,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Nt([t,f.pathname])),(d.replace?r.replace:r.push)(f,d.state,d)},[t,r,o,i,e])}function Fd(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=R.useContext(cn),{matches:l}=R.useContext(fn),{pathname:i}=vl(),o=JSON.stringify(Hi(l,r.v7_relativeSplatPath));return R.useMemo(()=>Wi(e,JSON.parse(o),i,n==="path"),[e,o,i,n])}function xv(e,t,n,r){mr()||Q(!1);let{navigator:l}=R.useContext(cn),{matches:i}=R.useContext(fn),o=i[i.length-1],u=o?o.params:{};o&&o.pathname;let a=o?o.pathnameBase:"/";o&&o.route;let s=vl(),d;d=s;let f=d.pathname||"/",h=f;if(a!=="/"){let E=a.replace(/^\//,"").split("/");h="/"+f.replace(/^\//,"").split("/").slice(E.length).join("/")}let w=yn(e,{pathname:h});return Lv(w&&w.map(E=>Object.assign({},E,{params:Object.assign({},u,E.params),pathname:Nt([a,l.encodeLocation?l.encodeLocation(E.pathname).pathname:E.pathname]),pathnameBase:E.pathnameBase==="/"?a:Nt([a,l.encodeLocation?l.encodeLocation(E.pathnameBase).pathname:E.pathnameBase])})),i,n,r)}function Cv(){let e=Mv(),t=sl(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,l={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return R.createElement(R.Fragment,null,R.createElement("h2",null,"Unexpected Application Error!"),R.createElement("h3",{style:{fontStyle:"italic"}},t),n?R.createElement("pre",{style:l},n):null,null)}const Pv=R.createElement(Cv,null);class _v extends R.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?R.createElement(fn.Provider,{value:this.props.routeContext},R.createElement(Md.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Rv(e){let{routeContext:t,match:n,children:r}=e,l=R.useContext(Qi);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),R.createElement(fn.Provider,{value:t},r)}function Lv(e,t,n,r){var l;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,u=(l=n)==null?void 0:l.errors;if(u!=null){let d=o.findIndex(f=>f.route.id&&(u==null?void 0:u[f.route.id])!==void 0);d>=0||Q(!1),o=o.slice(0,Math.min(o.length,d+1))}let a=!1,s=-1;if(n&&r&&r.v7_partialHydration)for(let d=0;d<o.length;d++){let f=o[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(s=d),f.route.id){let{loaderData:h,errors:w}=n,k=f.route.loader&&h[f.route.id]===void 0&&(!w||w[f.route.id]===void 0);if(f.route.lazy||k){a=!0,s>=0?o=o.slice(0,s+1):o=[o[0]];break}}}return o.reduceRight((d,f,h)=>{let w,k=!1,E=null,D=null;n&&(w=u&&f.route.id?u[f.route.id]:void 0,E=f.route.errorElement||Pv,a&&(s<0&&h===0?(Ov("route-fallback"),k=!0,D=null):s===h&&(k=!0,D=f.route.hydrateFallbackElement||null)));let m=t.concat(o.slice(0,h+1)),c=()=>{let v;return w?v=E:k?v=D:f.route.Component?v=R.createElement(f.route.Component,null):f.route.element?v=f.route.element:v=d,R.createElement(Rv,{match:f,routeContext:{outlet:d,matches:m,isDataRoute:n!=null},children:v})};return n&&(f.route.ErrorBoundary||f.route.errorElement||h===0)?R.createElement(_v,{location:n.location,revalidation:n.revalidation,component:E,error:w,children:c(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):c()},null)}var Id=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Id||{}),jd=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(jd||{});function Tv(e){let t=R.useContext(Qi);return t||Q(!1),t}function Nv(e){let t=R.useContext(Dd);return t||Q(!1),t}function Dv(e){let t=R.useContext(fn);return t||Q(!1),t}function Ud(e){let t=Dv(),n=t.matches[t.matches.length-1];return n.route.id||Q(!1),n.route.id}function Mv(){var e;let t=R.useContext(Md),n=Nv(),r=Ud();return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function zv(){let{router:e}=Tv(Id.UseNavigateStable),t=Ud(jd.UseNavigateStable),n=R.useRef(!1);return zd(()=>{n.current=!0}),R.useCallback(function(l,i){i===void 0&&(i={}),n.current&&(typeof l=="number"?e.navigate(l):e.navigate(l,Ri({fromRouteId:t},i)))},[e,t])}const nc={};function Ov(e,t,n){nc[e]||(nc[e]=!0)}function Fv(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function iy(e){let{to:t,replace:n,state:r,relative:l}=e;mr()||Q(!1);let{future:i,static:o}=R.useContext(cn),{matches:u}=R.useContext(fn),{pathname:a}=vl(),s=Od(),d=Wi(t,Hi(u,i.v7_relativeSplatPath),a,l==="path"),f=JSON.stringify(d);return R.useEffect(()=>s(JSON.parse(f),{replace:n,state:r,relative:l}),[s,f,l,n,r]),null}function Iv(e){Q(!1)}function jv(e){let{basename:t="/",children:n=null,location:r,navigationType:l=ye.Pop,navigator:i,static:o=!1,future:u}=e;mr()&&Q(!1);let a=t.replace(/^\/*/,"/"),s=R.useMemo(()=>({basename:a,navigator:i,static:o,future:Ri({v7_relativeSplatPath:!1},u)}),[a,u,i,o]);typeof r=="string"&&(r=sn(r));let{pathname:d="/",search:f="",hash:h="",state:w=null,key:k="default"}=r,E=R.useMemo(()=>{let D=hr(d,a);return D==null?null:{location:{pathname:D,search:f,hash:h,state:w,key:k},navigationType:l}},[a,d,f,h,w,k,l]);return E==null?null:R.createElement(cn.Provider,{value:s},R.createElement(ga.Provider,{children:n,value:E}))}new Promise(()=>{});function rc(e,t){t===void 0&&(t=[]);let n=[];return R.Children.forEach(e,(r,l)=>{if(!R.isValidElement(r))return;let i=[...t,l];if(r.type===R.Fragment){n.push.apply(n,rc(r.props.children,i));return}r.type!==Iv&&Q(!1),!r.props.index||!r.props.children||Q(!1);let o={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=rc(r.props.children,i)),n.push(o)}),n}function Uv(e){let t={hasErrorBoundary:e.ErrorBoundary!=null||e.errorElement!=null};return e.Component&&Object.assign(t,{element:R.createElement(e.Component),Component:void 0}),e.HydrateFallback&&Object.assign(t,{hydrateFallbackElement:R.createElement(e.HydrateFallback),HydrateFallback:void 0}),e.ErrorBoundary&&Object.assign(t,{errorElement:R.createElement(e.ErrorBoundary),ErrorBoundary:void 0}),t}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function cl(){return cl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},cl.apply(this,arguments)}function Av(e,t){if(e==null)return{};var n={},r=Object.keys(e),l,i;for(i=0;i<r.length;i++)l=r[i],!(t.indexOf(l)>=0)&&(n[l]=e[l]);return n}function Bv(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function $v(e,t){return e.button===0&&(!t||t==="_self")&&!Bv(e)}const Vv=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Hv="6";try{window.__reactRouterVersion=Hv}catch{}function oy(e,t){return tv({basename:t==null?void 0:t.basename,future:cl({},t==null?void 0:t.future,{v7_prependBasename:!0}),history:Pm({window:t==null?void 0:t.window}),hydrationData:(t==null?void 0:t.hydrationData)||Wv(),routes:e,mapRouteProperties:Uv,dataStrategy:t==null?void 0:t.dataStrategy,patchRoutesOnNavigation:t==null?void 0:t.patchRoutesOnNavigation,window:t==null?void 0:t.window}).initialize()}function Wv(){var e;let t=(e=window)==null?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=cl({},t,{errors:Qv(t.errors)})),t}function Qv(e){if(!e)return null;let t=Object.entries(e),n={};for(let[r,l]of t)if(l&&l.__type==="RouteErrorResponse")n[r]=new _i(l.status,l.statusText,l.data,l.internal===!0);else if(l&&l.__type==="Error"){if(l.__subType){let i=window[l.__subType];if(typeof i=="function")try{let o=new i(l.message);o.stack="",n[r]=o}catch{}}if(n[r]==null){let i=new Error(l.message);i.stack="",n[r]=i}}else n[r]=l;return n}const Kv=R.createContext({isTransitioning:!1}),Yv=R.createContext(new Map),Xv="startTransition",lc=gp[Xv],Gv="flushSync",ic=Cm[Gv];function Jv(e){lc?lc(e):e()}function Nr(e){ic?ic(e):e()}class Zv{constructor(){this.status="pending",this.promise=new Promise((t,n)=>{this.resolve=r=>{this.status==="pending"&&(this.status="resolved",t(r))},this.reject=r=>{this.status==="pending"&&(this.status="rejected",n(r))}})}}function uy(e){let{fallbackElement:t,router:n,future:r}=e,[l,i]=R.useState(n.state),[o,u]=R.useState(),[a,s]=R.useState({isTransitioning:!1}),[d,f]=R.useState(),[h,w]=R.useState(),[k,E]=R.useState(),D=R.useRef(new Map),{v7_startTransition:m}=r||{},c=R.useCallback(P=>{m?Jv(P):P()},[m]),v=R.useCallback((P,B)=>{let{deletedFetchers:M,flushSync:b,viewTransitionOpts:ne}=B;P.fetchers.forEach((Ce,ut)=>{Ce.data!==void 0&&D.current.set(ut,Ce.data)}),M.forEach(Ce=>D.current.delete(Ce));let we=n.window==null||n.window.document==null||typeof n.window.document.startViewTransition!="function";if(!ne||we){b?Nr(()=>i(P)):c(()=>i(P));return}if(b){Nr(()=>{h&&(d&&d.resolve(),h.skipTransition()),s({isTransitioning:!0,flushSync:!0,currentLocation:ne.currentLocation,nextLocation:ne.nextLocation})});let Ce=n.window.document.startViewTransition(()=>{Nr(()=>i(P))});Ce.finished.finally(()=>{Nr(()=>{f(void 0),w(void 0),u(void 0),s({isTransitioning:!1})})}),Nr(()=>w(Ce));return}h?(d&&d.resolve(),h.skipTransition(),E({state:P,currentLocation:ne.currentLocation,nextLocation:ne.nextLocation})):(u(P),s({isTransitioning:!0,flushSync:!1,currentLocation:ne.currentLocation,nextLocation:ne.nextLocation}))},[n.window,h,d,D,c]);R.useLayoutEffect(()=>n.subscribe(v),[n,v]),R.useEffect(()=>{a.isTransitioning&&!a.flushSync&&f(new Zv)},[a]),R.useEffect(()=>{if(d&&o&&n.window){let P=o,B=d.promise,M=n.window.document.startViewTransition(async()=>{c(()=>i(P)),await B});M.finished.finally(()=>{f(void 0),w(void 0),u(void 0),s({isTransitioning:!1})}),w(M)}},[c,o,d,n.window]),R.useEffect(()=>{d&&o&&l.location.key===o.location.key&&d.resolve()},[d,h,l.location,o]),R.useEffect(()=>{!a.isTransitioning&&k&&(u(k.state),s({isTransitioning:!0,flushSync:!1,currentLocation:k.currentLocation,nextLocation:k.nextLocation}),E(void 0))},[a.isTransitioning,k]),R.useEffect(()=>{},[]);let x=R.useMemo(()=>({createHref:n.createHref,encodeLocation:n.encodeLocation,go:P=>n.navigate(P),push:(P,B,M)=>n.navigate(P,{state:B,preventScrollReset:M==null?void 0:M.preventScrollReset}),replace:(P,B,M)=>n.navigate(P,{replace:!0,state:B,preventScrollReset:M==null?void 0:M.preventScrollReset})}),[n]),L=n.basename||"/",O=R.useMemo(()=>({router:n,navigator:x,static:!1,basename:L}),[n,x,L]),y=R.useMemo(()=>({v7_relativeSplatPath:n.future.v7_relativeSplatPath}),[n.future.v7_relativeSplatPath]);return R.useEffect(()=>Fv(r,n.future),[r,n.future]),R.createElement(R.Fragment,null,R.createElement(Qi.Provider,{value:O},R.createElement(Dd.Provider,{value:l},R.createElement(Yv.Provider,{value:D.current},R.createElement(Kv.Provider,{value:a},R.createElement(jv,{basename:L,location:l.location,navigationType:l.historyAction,navigator:x,future:y},l.initialized||n.future.v7_partialHydration?R.createElement(qv,{routes:n.routes,future:n.future,state:l}):t))))),null)}const qv=R.memo(bv);function bv(e){let{routes:t,future:n,state:r}=e;return xv(t,void 0,r,n)}const ey=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ty=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ay=R.forwardRef(function(t,n){let{onClick:r,relative:l,reloadDocument:i,replace:o,state:u,target:a,to:s,preventScrollReset:d,viewTransition:f}=t,h=Av(t,Vv),{basename:w}=R.useContext(cn),k,E=!1;if(typeof s=="string"&&ty.test(s)&&(k=s,ey))try{let v=new URL(window.location.href),x=s.startsWith("//")?new URL(v.protocol+s):new URL(s),L=hr(x.pathname,w);x.origin===v.origin&&L!=null?s=L+x.search+x.hash:E=!0}catch{}let D=Ev(s,{relative:l}),m=ny(s,{replace:o,state:u,target:a,preventScrollReset:d,relative:l,viewTransition:f});function c(v){r&&r(v),v.defaultPrevented||m(v)}return R.createElement("a",cl({},h,{href:k||D,onClick:E||i?r:c,ref:n,target:a}))});var oc;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(oc||(oc={}));var uc;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(uc||(uc={}));function ny(e,t){let{target:n,replace:r,state:l,preventScrollReset:i,relative:o,viewTransition:u}=t===void 0?{}:t,a=Od(),s=vl(),d=Fd(e,{relative:o});return R.useCallback(f=>{if($v(f,n)){f.preventDefault();let h=r!==void 0?r:Nn(s)===Nn(d);a(e,{replace:h,state:l,preventScrollReset:i,relative:o,viewTransition:u})}},[s,a,d,r,l,n,e,i,o,u])}export{ay as L,iy as N,yp as R,Ed as a,gp as b,ry as c,ly as d,oy as e,rc as f,sc as g,uy as h,Iv as i,R as r,Od as u};
//# sourceMappingURL=vendor-TMfmWqqA.js.map
