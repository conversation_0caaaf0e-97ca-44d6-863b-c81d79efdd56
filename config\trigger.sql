DELIMITER $$

CREATE TRIGGER AfterSteeringOfRoamingInsert
AFTER INSERT ON SteeringOfRoaming FOR EACH ROW
BEGIN
    DECLARE lastTestRunId BIGINT;
    DECLARE lastProbeDataId BIGINT;

    -- Insert into TestRuns
    INSERT INTO TestRuns (
        TestcaseId, OrderId, TCName, Timestamp, Verdict, TestDefinitionPath, `User`, UserGroup,
        TCTestDefinitionId, Completed, Failure, Incomplete, Success, ServiceType, CauseText_L3,
        CauseValue_L3, L3_Flag, TCDuration, TestId, TestrunId, Description, Valid
    ) VALUES (
        NEW.TestcaseId, NEW.OrderId, NEW.TCName, NEW.Timestamp, NEW.Verdict, NEW.TestDefinitionPath,
        NEW.`User`, NEW.UserGroup, NEW.TCTestDefinitionId, NEW.Completed, NEW.Failure, NEW.Incomplete,
        NEW.Success, NEW.ServiceType, NEW.CauseText_L3, NEW.CauseValue_L3, NEW.L3_Flag, NEW.TCDuration,
        NEW.TestId, NEW.TestrunId, NEW.Description, NEW.Valid
    );
    
    SET lastTestRunId = LAST_INSERT_ID();

    -- Insert into TimeInfo
    INSERT INTO TimeInfo (
        TestRunId, DayOfMonth, DayOfWeek, HourOfDay, MonthOfYear, TimeZone, WeekOfYear
    ) VALUES (
        lastTestRunId, NEW.DayOfMonth, NEW.DayOfWeek, NEW.HourOfDay, NEW.MonthOfYear,
        NEW.TimeZone, NEW.WeekOfYear
    );

    -- Insert into ExecutionInfo
    INSERT INTO ExecutionInfo (
        TestRunId, Distance, ExecutionHost, ExecutionId, ExternalNumber, ExternalNumberOrURI,
        unitsMaxTimeOffset, GRP, GlobalResourceCount, RecordId, InsertId
    ) VALUES (
        lastTestRunId, NEW.Distance, NEW.ExecutionHost, NEW.ExecutionId, NEW.ExternalNumber,
        NEW.ExternalNumberOrURI, NEW.unitsMaxTimeOffset, NEW.GRP, NEW.GlobalResourceCount,
        NEW.RecordId, NEW.InsertId
    );

    -- Insert into ErrorInfo if there is an error
    IF NEW.errorId IS NOT NULL THEN
        INSERT INTO ErrorInfo (
            TestRunId, errorId, errorText, errorSideCountry, errorSideId, errorSideLocation,
            errorSideNumber, errorSidePlmn, errorSideProbe, errorSideRxLevel, errorSideUsedPLMNName,
            errorState, errorStateId
        ) VALUES (
            lastTestRunId, NEW.errorId, NEW.errorText, NEW.errorSideCountry, NEW.errorSideId,
            NEW.errorSideLocation, NEW.errorSideNumber, NEW.errorSidePlmn, NEW.errorSideProbe,
            NEW.errorSideRxLevel, NEW.errorSideUsedPLMNName, NEW.errorState, NEW.errorStateId
        );
    END IF;

    -- Process Probe 'a' data
    IF NEW.a_imsi IS NOT NULL THEN
        -- Insert basic probe data
        INSERT INTO ProbeData (
            TestRunId, ProbeLabel, EcNO, HappyEyeBallSelectedIPVersion, IP_Version, IPv4_Used,
            IPv6_Used, InitialLupReqTime, LTE_RgAttachDuration, LupAcceptDuration,
            LupAcceptDuration_All_VPLMN, LupDuration, LupMode, LupRejectDuration,
            NrOfLupRejects_All_VPLMN, NrOfLupRequests, NrOfLupRequests_All_VPLMN,
            NrOfPlmnsRejected, OverallNrOfLupRequests, RejectCauses, RejectedPLMNs,
            SimAuthenticationAfterAttach, SimAuthenticationAfterLup
        ) VALUES (
            lastTestRunId, 'a', NEW.a_EcNO, NEW.a_HappyEyeBallSelectedIPVersion,
            NEW.a_IP_Version, NEW.a_IPv4_Used, NEW.a_IPv6_Used, NEW.a_InitialLupReqTime,
            NEW.a_LTE_RgAttachDuration, NEW.a_LupAcceptDuration, NEW.a_LupAcceptDuration_All_VPLMN,
            NEW.a_LupDuration, NEW.a_LupMode, NEW.a_LupRejectDuration, NEW.a_NrOfLupRejects_All_VPLMN,
            NEW.a_NrOfLupRequests, NEW.a_NrOfLupRequests_All_VPLMN, NEW.a_NrOfPlmnsRejected,
            NEW.a_OverallNrOfLupRequests, NEW.a_RejectCauses, NEW.a_RejectedPLMNs,
            NEW.a_SimAuthenticationAfterAttach, NEW.a_SimAuthenticationAfterLup
        );

        SET lastProbeDataId = LAST_INSERT_ID();

        -- Insert network info
        INSERT INTO NetworkInfo (
            ProbeDataId, TAC, UsedPLMNNameShort, VPLMN_registered, _5QI_dedicated_L3,
            _5QI_default_L3, LTE_Freq, _5G_NSA_availability, _5G_NSA_used, ATR_Mobile,
            ATR_SimEmu, CI, DCNR_restricted, EcIo, LAC, LTE_Band, LTE_Bandwidth,
            MMEName, NID, NRI_cs, NRI_ps, NR_Band, NR_DL_Bandwidth, NR_RSRP,
            NR_RSRQ, NR_SINR, NR_pCI, NSSAI, RAC, RSCP, RSRP, RxLevel
        ) VALUES (
            lastProbeDataId, NEW.a_TAC, NEW.a_UsedPLMNNameShort, NEW.a_VPLMN_registered,
            NEW.a_5QI_dedicated_L3, NEW.a_5QI_default_L3, NEW.a_LTE_Freq,
            NEW.a_5G_NSA_availability, NEW.a_5G_NSA_used, NEW.a_ATR_Mobile,
            NEW.a_ATR_SimEmu, NEW.a_CI, NEW.a_DCNR_restricted, NEW.a_EcIo,
            NEW.a_LAC, NEW.a_LTE_Band, NEW.a_LTE_Bandwidth, NEW.a_MMEName,
            NEW.a_NID, NEW.a_NRI_cs, NEW.a_NRI_ps, NEW.a_NR_Band,
            NEW.a_NR_DL_Bandwidth, NEW.a_NR_RSRP, NEW.a_NR_RSRQ, NEW.a_NR_SINR,
            NEW.a_NR_pCI, NEW.a_NSSAI, NEW.a_RAC, NEW.a_RSCP, NEW.a_RSRP,
            NEW.a_RxLevel
        );

        -- Insert authentication info
        INSERT INTO AuthenticationInfo (
            ProbeDataId, SID, SIM_AuthenticationEnd, SIM_AuthenticationStart, TADIG,
            UsedPLMN, UsedPLMNName, imsi, location, location_country, number,
            plmn, plmnShort, probe
        ) VALUES (
            lastProbeDataId, NEW.a_SID, NEW.a_SIM_AuthenticationEnd,
            NEW.a_SIM_AuthenticationStart, NEW.a_TADIG, NEW.a_UsedPLMN,
            NEW.a_UsedPLMNName, NEW.a_imsi, NEW.a_location, NEW.a_location_country,
            NEW.a_number, NEW.a_plmn, NEW.a_plmnShort, NEW.a_probe
        );

        -- Insert quality info
        INSERT INTO QualityInfo (
            ProbeDataId, uuCqiAverage, uuCqiSamples, uuPppHsdpaUsed,
            uuPppHsupaUsed, NetworkType, hlr, mobileType, type, UnitGPS
        ) VALUES (
            lastProbeDataId, NEW.a_uuCqiAverage, NEW.a_uuCqiSamples,
            NEW.a_uuPppHsdpaUsed, NEW.a_uuPppHsupaUsed, NEW.a_NetworkType,
            NEW.a_hlr, NEW.a_mobileType, NEW.a_type, NEW.a_UnitGPS
        );

        -- Insert PLMN search info
        INSERT INTO PLMNSearchInfo (
            ProbeDataId, SearchedPLMN, SearchedPLMNName, SearchedPLMNNameShort,
            GlobalUsedPLMN
        ) VALUES (
            lastProbeDataId, NEW.a_SearchedPLMN, NEW.a_SearchedPLMNName,
            NEW.a_SearchedPLMNNameShort, NEW.a_GlobalUsedPLMN
        );
    END IF;

    -- Répéter le même processus pour les sondes 'b', 'c' et 'd'
    -- (Code similaire avec les préfixes b_, c_ et d_)

END$$

DELIMITER ; 